"""
Management command to fix database and role issues
"""

from django.core.management.base import BaseCommand
from users.models import UserRole
from core.config.role_config import REQUIRED_ROLES


class Command(BaseCommand):
    help = 'Fix database and role issues'

    def handle(self, *args, **options):
        self.stdout.write('FIXING DATABASE AND ROLE ISSUES')
        self.stdout.write('=' * 50)
        
        # Fix missing moderator role
        self.fix_missing_roles()
        
        # Verify role consistency
        self.verify_roles()
        
        self.stdout.write(self.style.SUCCESS('All role issues fixed!'))

    def fix_missing_roles(self):
        """Create any missing roles"""
        self.stdout.write('\nChecking for missing roles...')
        
        required_role_names = [role['name'] for role in REQUIRED_ROLES]
        existing_role_names = [role.name for role in UserRole.objects.all()]
        missing_roles = set(required_role_names) - set(existing_role_names)
        
        if missing_roles:
            self.stdout.write(f'Found missing roles: {missing_roles}')
            
            for role_name in missing_roles:
                role_config = next((role for role in REQUIRED_ROLES if role['name'] == role_name), None)
                
                if role_config:
                    role = UserRole.objects.create(
                        name=role_config['name'],
                        display_name=role_config['display_name'],
                        description=role_config['description'],
                        permission_level=role_config['permission_level'],
                        is_active=role_config['is_active'],
                        requires_approval=role_config['requires_approval']
                    )
                    self.stdout.write(f'  Created role: {role.display_name}')
        else:
            self.stdout.write('No missing roles found')

    def verify_roles(self):
        """Verify all roles are properly configured"""
        self.stdout.write('\nCurrent roles in database:')
        
        for role in UserRole.objects.all().order_by('name'):
            status = "Active" if role.is_active else "Inactive"
            approval = "Auto" if not role.requires_approval else "Manual"
            
            self.stdout.write(
                f'  - {role.name}: {role.display_name} '
                f'({role.permission_level}) - {status}, {approval} approval'
            )
