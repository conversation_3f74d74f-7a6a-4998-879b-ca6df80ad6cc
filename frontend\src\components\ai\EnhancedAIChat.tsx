import React, { useState, useEffect, useRef } from 'react';
import { useLanguage } from '../../hooks/useLanguage';
import { useAppSelector } from '../../store/hooks';
import { useUnifiedRoles } from '../../hooks/useUnifiedRoles';
import { integratedAiApi } from '../../services/integratedAiApi';
import { Send, Bot, User, Loader2, Settings, MessageSquare, Lightbulb, Brain, FileText, Sparkles } from 'lucide-react';
import ReactMarkdown from 'react-markdown';

interface Message {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
}

// Typewriter Animation Component
const TypewriterText: React.FC<{ text: string; speed?: number; onComplete?: () => void }> = ({
  text,
  speed = 30,
  onComplete
}) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timer = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex]);
        setCurrentIndex(prev => prev + 1);
      }, speed);
      return () => clearTimeout(timer);
    } else if (onComplete) {
      onComplete();
    }
  }, [currentIndex, text, speed, onComplete]);

  return (
    <span className="inline-block">
      {displayText}
      {currentIndex < text.length && (
        <span className="animate-pulse text-purple-300">|</span>
      )}
    </span>
  );
};

interface EnhancedAIChatProps {
  height?: string;
  className?: string;
  showFeatures?: boolean;
  onStatsUpdate?: (stats: { messageCount: number; sessionDuration: number; isConnected: boolean }) => void;
  chatType?: 'syrian_business' | 'general';
  onChatTypeChange?: (type: 'syrian_business' | 'general') => void;
}

let messageIdCounter = 0;
const generateUniqueId = (prefix: string = 'msg') => {
  messageIdCounter++;
  return `${prefix}-${Date.now()}-${messageIdCounter}`;
};

const EnhancedAIChat: React.FC<EnhancedAIChatProps> = ({
  height = 'h-full',
  className = '',
  showFeatures = true,
  onStatsUpdate,
  chatType = 'general',
  onChatTypeChange
}) => {
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const { primaryRole } = useUnifiedRoles();

  // Core state
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error'>('connecting');

  // Syrian regional state
  const [selectedRegion, setSelectedRegion] = useState<string>('damascus_dialect');

  // ✅ NEW: Enhanced LangGraph Workflow Features
  const [workflowState, setWorkflowState] = useState<string>('');
  const [personalityEstablished, setPersonalityEstablished] = useState(false);
  const [regionalKnowledge, setRegionalKnowledge] = useState<any>(null);
  const [authenticityLevel, setAuthenticityLevel] = useState<'street_level' | 'formal' | 'mixed'>('street_level');
  const [dialectExpressions, setDialectExpressions] = useState<any>({});
  const [personalExperiences, setPersonalExperiences] = useState<string[]>([]);

  // UI state
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const [responseLanguage, setResponseLanguage] = useState('auto');

  // Session state
  const [sessionStartTime, setSessionStartTime] = useState<Date | null>(null);
  const [sessionDuration, setSessionDuration] = useState(0);
  const [messageCount, setMessageCount] = useState(0);

  // Refs
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const settingsRef = useRef<HTMLDivElement>(null);

  // Initialize chat
  useEffect(() => {
    initializeChat();
  }, [primaryRole]);

  // Update welcome message when region changes
  useEffect(() => {
    const welcomeMessage: Message = {
      id: generateUniqueId('welcome'),
      type: 'ai',
      content: getWelcomeMessage(),
      timestamp: new Date()
    };
    
    if (messages.length === 0) {
      setMessages([welcomeMessage]);
    } else {
      setMessages([welcomeMessage]);
      setMessageCount(1);
    }
  }, [selectedRegion]);

  // Click outside to close settings
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {
        setShowSettings(false);
      }
    };

    if (showSettings) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showSettings]);

  // Handle chat type changes
  useEffect(() => {
    if (connectionStatus === 'connected') {
      const welcomeMessage: Message = {
        id: generateUniqueId('welcome'),
        type: 'ai',
        content: getWelcomeMessage(),
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [chatType]);

  // Auto scroll to bottom
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Session timer
  useEffect(() => {
    if (sessionStartTime) {
      const interval = setInterval(() => {
        const duration = Math.floor((Date.now() - sessionStartTime.getTime()) / 1000);
        setSessionDuration(duration);

        // Update parent with stats
        if (onStatsUpdate) {
          onStatsUpdate({
            messageCount,
            sessionDuration: duration,
            isConnected: connectionStatus === 'connected'
          });
        }
      }, 10000);

      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [sessionStartTime, messageCount, connectionStatus, onStatsUpdate]);

  const initializeChat = async () => {
    try {
      setConnectionStatus('connecting');
      
      const status = await integratedAiApi.getStatus();
      
      if (status.available) {
        setConnectionStatus('connected');

        // Start session timer
        setSessionStartTime(new Date());

        const welcomeMessage: Message = {
          id: generateUniqueId('welcome'),
          type: 'ai',
          content: getWelcomeMessage(),
          timestamp: new Date()
        };

        setMessages([welcomeMessage]);
        setMessageCount(1);

        // Direct chat - no session management needed
      } else {
        setConnectionStatus('error');
        addSystemMessage('❌ AI service is currently unavailable. Please try again later.');
      }
    } catch (error) {
      setConnectionStatus('error');
      addSystemMessage('❌ Failed to connect to AI service. Please check your connection.');
    }
  };

  const getWelcomeMessage = () => {
    const userName = user?.first_name || user?.username || 'صديقي';
    const userRole = primaryRole || 'user';

    const regionalWelcomes = {
      'damascus_dialect': `أهلاً وسهلاً ${userName}! أنا ياسمين من دمشق الشام العتيقة. كيفك شو أخبارك؟ أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين. بعرف كل زاوية بالشام وأسواقها وحاراتها. يمكنني مساعدتك في أي شي يخص دمشق وريادة الأعمال. ماشي؟`,
      'aleppo_dialect': `أهلين وسهلين ${userName}! أنا ياسمين من حلب الشهباء. إيش أخبارك كيف صحتك؟ أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين. بعرف كل شارع بحلب وصناعاتها وتجارتها العريقة. يمكنني مساعدتك في كل ما يخص حلب وأعمالها. هيك الحكي!`,
      'homs_dialect': `أهلاً يا زلمة ${userName}! أنا ياسمين من حمص المرحة. شو الأخبار كيف الأحوال؟ أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين. بحب الضحك والمرح وأساعدك في أعمالك بطريقة مرحة. يمكنني مساعدتك بكل شي وبضحك كمان! هههه`,
      'latakia_dialect': `أهلاً وسهلاً ${userName}! أنا ياسمين من اللاذقية الساحل الجميل. كيفك منيح؟ أعرف أنك ${getRoleInArabic(userRole)} في منصة ياسمين. بحب البحر والهوا الطلق وبعرف كل شي عن الساحل السوري وتجارته. يمكنني مساعدتك في أعمالك وأفكارك. منيح هيك؟`
    };

    return regionalWelcomes[selectedRegion] || regionalWelcomes['damascus_dialect'];
  };

  // Get role in Arabic
  const getRoleInArabic = (role: string) => {
    const roleTranslations = {
      'user': 'مستخدم',
      'entrepreneur': 'رائد أعمال',
      'investor': 'مستثمر',
      'mentor': 'مرشد',
      'admin': 'مدير',
      'super_admin': 'مدير عام'
    };
    return roleTranslations[role] || 'مستخدم';
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addSystemMessage = (content: string) => {
    const systemMessage: Message = {
      id: generateUniqueId('system'),
      type: 'system',
      content,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, systemMessage]);
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const userMessage: Message = {
      id: generateUniqueId('user'),
      type: 'user',
      content: inputMessage.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);
    setMessageCount(prev => prev + 1);

    try {
      const userName = user?.first_name || user?.username || 'صديقي';

      // ✅ Enhanced Context for LangGraph Workflow
      const enhancedContext = {
        // User context
        user_name: userName,
        user_role: user?.user_role || 'user',
        user_region: selectedRegion,

        // LangGraph workflow preferences
        authenticity_level: authenticityLevel,
        personality_established: personalityEstablished,
        regional_knowledge: regionalKnowledge,
        dialect_expressions: dialectExpressions,
        personal_experiences: personalExperiences,

        // Syrian context flags
        syrian_context: true,
        enable_langgraph: true,
        workflow_state: workflowState,

        // AI parameters
        temperature: 0.8,
        max_tokens: 4000,
        model: 'gemini-2.5-flash',
        enable_thinking: true,
        creativity_level: 0.8,
        response_length: 4000
      };

      const response = await integratedAiApi.chat(
        userMessage.content,
        chatType,
        responseLanguage === 'auto' ? 'auto' : responseLanguage,
        enhancedContext
      );

      if (response.success && response.response) {
        // ✅ Update LangGraph workflow state from response
        if (response.workflow_state) {
          setWorkflowState(response.workflow_state);
        }
        if (response.user_region) {
          setSelectedRegion(response.user_region);
        }
        if (response.analysis && response.analysis.personality_established) {
          setPersonalityEstablished(true);
        }

        const aiMessage: Message = {
          id: generateUniqueId('ai'),
          type: 'ai',
          content: response.response,
          timestamp: new Date(),
          isTyping: true,
          // ✅ Enhanced metadata from LangGraph
          metadata: {
            workflow_state: response.workflow_state,
            user_region: response.user_region,
            authenticity_level: response.authenticity_level,
            detected_language: response.detected_language
          }
        };

        setMessages(prev => [...prev, aiMessage]);
        setMessageCount(prev => prev + 1);
      } else {
        addSystemMessage('❌ فشل في الحصول على رد من ياسمين. يرجى المحاولة مرة أخرى.');
      }
    } catch (error) {
      console.error('Chat error:', error);
      addSystemMessage('❌ An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <>
      {/* Enhanced CSS Animations */}
      <style jsx="true">{`
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&family=Tajawal:wght@300;400;500;700&display=swap');

        .font-arabic {
          font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', Arial, sans-serif;
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideInRight {
          from { opacity: 0; transform: translateX(20px); }
          to { opacity: 1; transform: translateX(0); }
        }

        @keyframes slideInLeft {
          from { opacity: 0; transform: translateX(-20px); }
          to { opacity: 1; transform: translateX(0); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out;
        }

        .animate-slideInRight {
          animation: slideInRight 0.5s ease-out;
        }

        .animate-slideInLeft {
          animation: slideInLeft 0.5s ease-out;
        }

        .border-3 {
          border-width: 3px;
        }
      `}</style>

      <div className={`${height} ${className} flex flex-col bg-gradient-to-br from-slate-900 via-purple-900 to-indigo-900 text-white relative overflow-hidden`} dir={isRTL ? 'rtl' : 'ltr'}>
      {/* Enhanced Header with Syrian Branding */}
      <div className="flex-shrink-0 p-4 sm:p-6 border-b border-white/20 bg-gradient-to-r from-purple-900/50 to-indigo-900/50 backdrop-blur-md">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 sm:gap-6">
            {/* Enhanced Avatar with Animation */}
            <div className="relative">
              <div className="w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br from-purple-400 via-pink-400 to-blue-400 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 animate-pulse">
                <Bot className="w-6 h-6 sm:w-8 sm:h-8 text-white drop-shadow-lg" />
                <Sparkles className="absolute -top-1 -right-1 w-4 h-4 text-yellow-300 animate-bounce" />
              </div>
              <div className={`absolute -bottom-1 ${isRTL ? '-left-1' : '-right-1'} w-4 h-4 sm:w-5 sm:h-5 rounded-full border-3 border-white shadow-lg ${
                connectionStatus === 'connected' ? 'bg-green-400 animate-pulse' :
                connectionStatus === 'connecting' ? 'bg-yellow-400 animate-spin' : 'bg-red-400'
              }`} />
            </div>

            {/* Enhanced Status and Region */}
            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-3">
                <h2 className="text-lg sm:text-xl font-bold text-white font-arabic">
                  ياسمين الذكية 🇸🇾
                </h2>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  connectionStatus === 'connected' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                  connectionStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                  'bg-red-500/20 text-red-300 border border-red-500/30'
                }`}>
                  {connectionStatus === 'connected' ? '🟢 متصلة' :
                   connectionStatus === 'connecting' ? '🟡 جاري الاتصال...' : '🔴 غير متصلة'}
                </span>

                {/* ✅ NEW: Enhanced Workflow Status */}
                {connectionStatus === 'connected' && personalityEstablished && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-purple-500/20 text-purple-300 border border-purple-500/30">
                    🧠 شخصية محددة
                  </span>
                )}

                {connectionStatus === 'connected' && workflowState && (
                  <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-500/20 text-blue-300 border border-blue-500/30">
                    ⚡ {workflowState === 'finalized' ? 'جاهزة' : 'معالجة'}
                  </span>
                )}
              </div>

              {connectionStatus === 'connected' && (
                <div className="flex items-center gap-2">
                  <span className="text-purple-200 text-sm font-arabic">من:</span>
                  <select
                    value={selectedRegion}
                    onChange={(e) => setSelectedRegion(e.target.value)}
                    className="bg-white/10 border border-white/30 rounded-lg px-3 py-1 text-purple-100 text-sm backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent font-arabic"
                  >
                    <option value="damascus_dialect">🏛️ دمشق الشام</option>
                    <option value="aleppo_dialect">🏰 حلب الشهباء</option>
                    <option value="homs_dialect">😄 حمص المرحة</option>
                    <option value="latakia_dialect">🌊 اللاذقية الساحل</option>
                  </select>
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Settings Button */}
          {showFeatures && (
            <div className="flex items-center gap-2">
              <button
                onClick={() => setShowSettings(true)}
                className="p-3 hover:bg-white/20 rounded-xl transition-all duration-200 group border border-white/20 backdrop-blur-sm"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-300" />
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Enhanced Messages with Arabic Support */}
      <div className="flex-1 overflow-y-auto p-4 sm:p-6 space-y-4 sm:space-y-6 bg-gradient-to-b from-transparent to-black/10 arabic-scroll">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? (isRTL ? 'justify-start' : 'justify-end') : (isRTL ? 'justify-end' : 'justify-start')} animate-fadeIn`}
          >
            <div className={`max-w-[85%] sm:max-w-[75%] ${message.type === 'user' ? 'order-2' : 'order-1'}`}>
              <div className={`flex items-start gap-3 sm:gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                {/* Enhanced Avatar */}
                <div className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center flex-shrink-0 shadow-lg ring-2 ring-white/20 ${
                  message.type === 'user'
                    ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                    : message.type === 'system'
                    ? 'bg-gradient-to-br from-yellow-500 to-orange-500'
                    : 'bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 animate-pulse'
                }`}>
                  {message.type === 'user' ? (
                    <User className="w-4 h-4 sm:w-5 sm:h-5 text-white drop-shadow" />
                  ) : message.type === 'system' ? (
                    <span className="text-white text-sm font-bold">!</span>
                  ) : (
                    <Bot className="w-4 h-4 sm:w-5 sm:h-5 text-white drop-shadow" />
                  )}
                </div>

                {/* Enhanced Message Content */}
                <div className={`rounded-2xl sm:rounded-3xl p-4 sm:p-6 shadow-xl ${
                  message.type === 'user'
                    ? 'bg-gradient-to-br from-blue-500/30 to-blue-600/20 border border-blue-400/40 backdrop-blur-md'
                    : message.type === 'system'
                    ? 'bg-gradient-to-br from-yellow-500/30 to-orange-500/20 border border-yellow-400/40 backdrop-blur-md'
                    : 'bg-gradient-to-br from-white/15 to-white/5 border border-white/30 backdrop-blur-md'
                } relative overflow-hidden`}>
                  {/* Decorative Background Pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white to-transparent rounded-full -translate-y-10 translate-x-10"></div>
                    <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-white to-transparent rounded-full translate-y-8 -translate-x-8"></div>
                  </div>

                  {/* Message Text with Enhanced Arabic Support */}
                  <div className={`relative z-10 text-sm sm:text-base leading-relaxed text-right font-arabic`}
                       dir="rtl"
                       style={{ fontFamily: 'Cairo, Tajawal, "Noto Sans Arabic", Arial, sans-serif' }}>
                    {message.type === 'ai' ? (
                      message.isTyping ? (
                        <TypewriterText
                          text={message.content}
                          speed={25}
                          onComplete={() => {
                            // Mark typing as complete
                            setMessages(prev => prev.map(msg =>
                              msg.id === message.id ? { ...msg, isTyping: false } : msg
                            ));
                          }}
                        />
                      ) : (
                        <div className="prose prose-invert prose-sm sm:prose-base max-w-none text-white/90">
                          <ReactMarkdown>{message.content}</ReactMarkdown>
                        </div>
                      )
                    ) : (
                      <p className="whitespace-pre-wrap text-white/95 leading-relaxed">{message.content}</p>
                    )}
                  </div>

                  {/* Message Timestamp */}
                  <div className="mt-2 text-xs text-white/50 text-left">
                    {message.timestamp.toLocaleTimeString('ar-SY', {
                      hour: '2-digit',
                      minute: '2-digit',
                      hour12: false
                    })}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Enhanced Loading Indicator */}
        {isLoading && (
          <div className={`flex ${isRTL ? 'justify-end' : 'justify-start'} animate-fadeIn`}>
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg ring-2 ring-white/20 animate-pulse">
                <Bot className="w-5 h-5 text-white drop-shadow" />
              </div>
              <div className="bg-gradient-to-br from-white/15 to-white/5 border border-white/30 rounded-2xl p-4 sm:p-5 backdrop-blur-md shadow-xl relative overflow-hidden">
                {/* Animated Background */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 animate-pulse"></div>

                <div className="relative z-10 flex items-center gap-3">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-purple-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
                    <div className="w-2 h-2 bg-pink-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
                  </div>
                  <span className="text-white/90 text-sm font-arabic">ياسمين تفكر وتحضر الإجابة...</span>
                  <Sparkles className="w-4 h-4 text-yellow-300 animate-spin" />
                </div>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Suggestions */}
      {showSuggestions && messages.length <= 1 && (
        <div className="flex-shrink-0 p-2 sm:p-4 border-t border-white/10 bg-white/5 backdrop-blur-sm">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3 max-w-4xl mx-auto">
            {getSuggestionPrompts().map((prompt, index) => (
              <button
                key={index}
                onClick={() => {
                  setInputMessage(prompt.text);
                  setShowSuggestions(false);
                }}
                className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg sm:rounded-xl transition-all duration-200 text-left backdrop-blur-sm group"
              >
                <prompt.icon className="w-4 h-4 sm:w-5 sm:h-5 text-purple-300 group-hover:text-purple-200 flex-shrink-0" />
                <span className="text-white/90 group-hover:text-white text-xs sm:text-sm">{prompt.text}</span>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Enhanced Input Area with Arabic Support */}
      <div className="flex-shrink-0 p-4 sm:p-6 border-t border-white/20 bg-gradient-to-r from-purple-900/30 to-indigo-900/30 backdrop-blur-md">
        <div className="flex gap-3 sm:gap-4 max-w-4xl mx-auto">
          <div className="flex-1 relative group">
            {/* Input Field with Enhanced Styling */}
            <textarea
              ref={inputRef}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="اكتب رسالتك هنا... 💬"
              className="w-full bg-gradient-to-br from-white/15 to-white/5 border-2 border-white/30 rounded-2xl sm:rounded-3xl px-4 sm:px-6 py-3 sm:py-4 text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-purple-400/50 resize-none backdrop-blur-md shadow-xl transition-all duration-300 group-hover:border-white/40 font-arabic text-right"
              rows={1}
              style={{
                minHeight: '50px',
                maxHeight: '150px',
                fontFamily: 'Cairo, Tajawal, "Noto Sans Arabic", Arial, sans-serif',
                fontSize: '16px',
                lineHeight: '1.5'
              }}
              dir="rtl"
              disabled={isLoading || connectionStatus !== 'connected'}
            />

            {/* Input Enhancement Overlay */}
            <div className="absolute inset-0 rounded-2xl sm:rounded-3xl bg-gradient-to-r from-purple-500/5 to-blue-500/5 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            {/* Character Count (for long messages) */}
            {inputMessage.length > 100 && (
              <div className="absolute bottom-2 left-4 text-xs text-white/50">
                {inputMessage.length}/500
              </div>
            )}
          </div>

          {/* Enhanced Send Button */}
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading || connectionStatus !== 'connected'}
            className="bg-gradient-to-br from-purple-500 via-pink-500 to-blue-500 hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 disabled:opacity-50 disabled:cursor-not-allowed p-3 sm:p-4 rounded-2xl transition-all duration-300 shadow-xl flex-shrink-0 group relative overflow-hidden"
            title={isRTL ? "إرسال الرسالة" : "Send Message"}
          >
            {/* Button Background Animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            <Send className="w-5 h-5 sm:w-6 sm:h-6 text-white drop-shadow-lg relative z-10 transition-transform duration-200 group-hover:scale-110 rotate-180" />

            {/* Send Button Sparkle Effect */}
            {!isLoading && inputMessage.trim() && (
              <Sparkles className="absolute -top-1 -right-1 w-3 h-3 text-yellow-300 animate-ping" />
            )}
          </button>
        </div>

        {/* Connection Status Indicator */}
        {connectionStatus !== 'connected' && (
          <div className="mt-3 text-center">
            <span className="text-sm text-white/60 font-arabic" dir="rtl">
              {connectionStatus === 'connecting' ? '🔄 جاري الاتصال بياسمين...' : '❌ غير متصل - يرجى المحاولة لاحقاً'}
            </span>
          </div>
        )}
      </div>

      {/* Settings Modal */}
      {showSettings && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-indigo-900 rounded-xl shadow-2xl border border-purple-500/20 w-full max-w-lg" ref={settingsRef}>
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/10">
              <h2 className="text-xl font-bold text-white flex items-center gap-3">
                <Settings className="w-6 h-6 text-purple-400" />
                🇸🇾 الإعدادات السورية
              </h2>
              <button
                onClick={() => setShowSettings(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <span className="text-white text-xl">×</span>
              </button>
            </div>

            {/* Settings Content */}
            <div className="p-6 space-y-6">
              {/* Regional Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  🏛️ المنطقة السورية
                </label>
                <select
                  value={selectedRegion}
                  onChange={(e) => setSelectedRegion(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="damascus_dialect">🏛️ دمشق الشام</option>
                  <option value="aleppo_dialect">🏰 حلب الشهباء</option>
                  <option value="homs_dialect">😄 حمص المرحة</option>
                  <option value="latakia_dialect">🌊 اللاذقية الساحل</option>
                </select>
              </div>

              {/* Chat Type Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  💬 نوع المحادثة
                </label>
                <select
                  value={chatType}
                  onChange={(e) => onChatTypeChange?.(e.target.value as any)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="syrian_business">🏢 استشارات أعمال</option>
                  <option value="general">💬 محادثة عامة</option>
                </select>
              </div>

              {/* Response Language */}
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-3">
                  🌐 لغة الاستجابة
                </label>
                <select
                  value={responseLanguage}
                  onChange={(e) => setResponseLanguage(e.target.value)}
                  className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  <option value="auto">🌐 تلقائي (حسب لغة السؤال)</option>
                  <option value="ar">🇸🇾 العربية</option>
                  <option value="en">🇺🇸 English</option>
                  <option value="mixed">🔄 مختلط (عربي وإنجليزي)</option>
                </select>
              </div>

              {/* ✅ NEW: Enhanced LangGraph Settings */}
              <div className="border-t border-white/10 pt-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  🧠 إعدادات الذكاء المتقدم
                </h3>

                {/* Authenticity Level */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    🎭 مستوى الأصالة
                  </label>
                  <select
                    value={authenticityLevel}
                    onChange={(e) => setAuthenticityLevel(e.target.value as any)}
                    className="w-full px-4 py-3 bg-gray-800/50 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    <option value="street_level">🏘️ شعبي (لهجة الحارة)</option>
                    <option value="mixed">🔄 مختلط (شعبي ورسمي)</option>
                    <option value="formal">🏛️ رسمي (لغة فصحى)</option>
                  </select>
                </div>

                {/* Workflow Status Display */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    ⚡ حالة المعالجة
                  </label>
                  <div className="px-4 py-3 bg-gray-800/30 border border-gray-600 rounded-lg text-gray-300">
                    {workflowState ? `🔄 ${workflowState}` : '⏳ في انتظار المحادثة'}
                  </div>
                </div>

                {/* Personality Status */}
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-300 mb-3">
                    🎭 حالة الشخصية
                  </label>
                  <div className={`px-4 py-3 border rounded-lg ${
                    personalityEstablished
                      ? 'bg-green-500/20 border-green-500/30 text-green-300'
                      : 'bg-yellow-500/20 border-yellow-500/30 text-yellow-300'
                  }`}>
                    {personalityEstablished ? '✅ شخصية محددة' : '⏳ جاري تحديد الشخصية'}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-center p-6 border-t border-white/10">
              <button
                onClick={() => setShowSettings(false)}
                className="px-8 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
              >
                ✅ تم
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );

  // Regional suggestions
  function getSuggestionPrompts() {
    const regionalSuggestions: Record<string, Array<{icon: any, text: string, category: string}>> = {
      'damascus_dialect': [
        { icon: Lightbulb, text: 'اقترح لي مشروع يناسب دمشق وأحياؤها', category: 'business' },
        { icon: Brain, text: 'وين أفضل الأماكن للاستثمار في دمشق؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن أسواق دمشق التجارية', category: 'local' },
        { icon: MessageSquare, text: 'شو أهم المناطق التجارية في العاصمة؟', category: 'general' },
        // ✅ Enhanced LangGraph-powered suggestions
        { icon: Brain, text: '🧠 حللي شخصيتي التجارية بذكاء متقدم', category: 'enhanced' },
        { icon: Lightbulb, text: '⚡ اعطيني نصائح مخصصة حسب خبرتي', category: 'enhanced' },
        { icon: FileText, text: '🎭 احكيلي بلهجة دمشقية أصيلة', category: 'enhanced' }
      ],
      'aleppo_dialect': [
        { icon: Lightbulb, text: 'اقترح لي مشروع يناسب حلب وصناعاتها', category: 'business' },
        { icon: Brain, text: 'شو أهم الصناعات التقليدية في حلب؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن تجارة النسيج في حلب', category: 'local' },
        { icon: MessageSquare, text: 'كيف يمكنني الاستفادة من تراث حلب التجاري؟', category: 'general' },
        // ✅ Enhanced LangGraph-powered suggestions
        { icon: Brain, text: '🧠 حللي مشروعي بذكاء حلبي أصيل', category: 'enhanced' },
        { icon: Lightbulb, text: '⚡ اعطيني أفكار من تراث حلب التجاري', category: 'enhanced' }
      ],
      'homs_dialect': [
        { icon: Lightbulb, text: 'اقترح لي مشروع مرح يناسب حمص', category: 'business' },
        { icon: Brain, text: 'شو أحلى الأعمال في حمص؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن تجارة حمص وأسواقها', category: 'local' },
        { icon: MessageSquare, text: 'كيف أبدأ مشروع في حمص؟', category: 'general' }
      ],
      'latakia_dialect': [
        { icon: Lightbulb, text: 'اقترح لي مشروع يناسب الساحل السوري', category: 'business' },
        { icon: Brain, text: 'ما هي الفرص في اللاذقية والساحل؟', category: 'business' },
        { icon: FileText, text: 'حدثني عن تجارة الساحل والبحر', category: 'local' },
        { icon: MessageSquare, text: 'كيف أستفيد من موقع اللاذقية؟', category: 'general' }
      ]
    };

    return regionalSuggestions[selectedRegion] || regionalSuggestions['damascus_dialect'];
  }
};

export default EnhancedAIChat;
