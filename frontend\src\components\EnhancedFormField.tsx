import React from 'react';
import { Check, <PERSON>, <PERSON>ader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, EyeOff } from 'lucide-react';

interface EnhancedFormFieldProps {
  label: string;
  labelAr?: string;
  type?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  placeholderAr?: string;
  error?: string;
  validationStatus?: 'valid' | 'invalid' | 'checking';
  required?: boolean;
  disabled?: boolean;
  language: string;
  className?: string;
  showPasswordToggle?: boolean;
  hint?: string;
  hintAr?: string;
  maxLength?: number;
  rows?: number; // For textarea
  options?: { value: string; label: string; labelAr?: string }[]; // For select
}

const EnhancedFormField: React.FC<EnhancedFormFieldProps> = ({
  label,
  labelAr,
  type = 'text',
  value,
  onChange,
  placeholder,
  placeholderAr,
  error,
  validationStatus,
  required = false,
  disabled = false,
  language,
  className = '',
  showPasswordToggle = false,
  hint,
  hintAr,
  maxLength,
  rows,
  options
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [isFocused, setIsFocused] = React.useState(false);
  const isRTL = language === 'ar';
  const isTextarea = type === 'textarea';
  const isSelect = type === 'select';
  const isPassword = type === 'password';

  const getFieldClasses = () => {
    const baseClasses = `
      w-full px-4 py-3 rounded-lg border transition-all duration-200 
      focus:outline-none focus:ring-2 text-white placeholder-gray-400
      ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
      ${isTextarea ? 'resize-none' : ''}
    `;

    let borderClasses = 'border-white/30 bg-white/20 focus:ring-purple-500 focus:border-purple-500';
    let shadowClasses = '';

    if (error) {
      borderClasses = 'border-red-500 bg-red-500/10 focus:ring-red-500 focus:border-red-500';
      shadowClasses = 'shadow-red-500/20 shadow-lg';
    } else if (validationStatus === 'valid') {
      borderClasses = 'border-green-500 bg-green-500/10 focus:ring-green-500 focus:border-green-500';
      shadowClasses = 'shadow-green-500/20 shadow-lg';
    } else if (validationStatus === 'checking') {
      borderClasses = 'border-yellow-500 bg-yellow-500/10 focus:ring-yellow-500 focus:border-yellow-500';
      shadowClasses = 'shadow-yellow-500/20 shadow-lg';
    } else if (isFocused) {
      shadowClasses = 'shadow-purple-500/20 shadow-lg';
    }

    return `${baseClasses} ${borderClasses} ${shadowClasses}`;
  };

  const renderValidationIcon = () => {
    if (!validationStatus) return null;

    const iconClasses = `w-5 h-5 ${isRTL ? 'ml-3' : 'mr-3'}`;

    switch (validationStatus) {
      case 'checking':
        return <Loader2 className={`${iconClasses} text-yellow-400 animate-spin`} />;
      case 'valid':
        return <Check className={`${iconClasses} text-green-400`} />;
      case 'invalid':
        return <X className={`${iconClasses} text-red-400`} />;
      default:
        return null;
    }
  };

  const renderPasswordToggle = () => {
    if (!showPasswordToggle || !isPassword) return null;

    return (
      <button
        type="button"
        onClick={() => setShowPassword(!showPassword)}
        className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center text-gray-400 hover:text-white transition-colors`}
        tabIndex={-1}
      >
        {showPassword ? (
          <EyeOff className="w-5 h-5" />
        ) : (
          <Eye className="w-5 h-5" />
        )}
      </button>
    );
  };

  const renderField = () => {
    const commonProps = {
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => 
        onChange(e.target.value),
      onFocus: () => setIsFocused(true),
      onBlur: () => setIsFocused(false),
      placeholder: language === 'ar' ? placeholderAr || placeholder : placeholder,
      disabled,
      maxLength,
      className: getFieldClasses(),
      dir: isRTL ? 'rtl' : 'ltr'
    };

    if (isSelect && options) {
      return (
        <select {...commonProps} className={`${getFieldClasses()} custom-select`}>
          <option value="">
            {language === 'ar' ? placeholderAr || placeholder : placeholder}
          </option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {language === 'ar' ? option.labelAr || option.label : option.label}
            </option>
          ))}
        </select>
      );
    }

    if (isTextarea) {
      return (
        <textarea
          {...commonProps}
          rows={rows || 3}
        />
      );
    }

    return (
      <input
        {...commonProps}
        type={isPassword ? (showPassword ? 'text' : 'password') : type}
      />
    );
  };

  const displayLabel = language === 'ar' ? labelAr || label : label;
  const displayHint = language === 'ar' ? hintAr || hint : hint;

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Label */}
      <label className={`block text-sm font-medium text-gray-300 ${isRTL ? 'text-right' : 'text-left'}`}>
        {displayLabel}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>

      {/* Field Container */}
      <div className="relative">
        {renderField()}
        
        {/* Validation Icon */}
        {validationStatus && (
          <div className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center pointer-events-none ${showPasswordToggle && isPassword ? (isRTL ? 'left-10' : 'right-10') : ''}`}>
            {renderValidationIcon()}
          </div>
        )}

        {/* Password Toggle */}
        {renderPasswordToggle()}
      </div>

      {/* Character Count */}
      {maxLength && value && (
        <div className={`text-xs text-gray-400 ${isRTL ? 'text-right' : 'text-left'}`}>
          {value.length}/{maxLength}
        </div>
      )}

      {/* Hint */}
      {displayHint && !error && (
        <div className={`flex items-start text-sm text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <AlertCircle className={`w-4 h-4 flex-shrink-0 mt-0.5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span>{displayHint}</span>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className={`flex items-start text-sm text-red-400 animate-fadeIn ${isRTL ? 'flex-row-reverse' : ''}`}>
          <X className={`w-4 h-4 flex-shrink-0 mt-0.5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span>{error}</span>
        </div>
      )}

      {/* Success Message */}
      {validationStatus === 'valid' && !error && (
        <div className={`flex items-start text-sm text-green-400 animate-fadeIn ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Check className={`w-4 h-4 flex-shrink-0 mt-0.5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span>
            {language === 'ar' ? 'تم التحقق بنجاح' : 'Verified successfully'}
          </span>
        </div>
      )}

      {/* Checking Message */}
      {validationStatus === 'checking' && (
        <div className={`flex items-start text-sm text-yellow-400 animate-pulse ${isRTL ? 'flex-row-reverse' : ''}`}>
          <Loader2 className={`w-4 h-4 flex-shrink-0 mt-0.5 animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`} />
          <span>
            {language === 'ar' ? 'جاري التحقق...' : 'Checking...'}
          </span>
        </div>
      )}
    </div>
  );
};

export default EnhancedFormField;
