@echo off
REM Enhanced Deployment script for Yasmeen AI Platform with Super Admin Setup (Windows)

echo 🚀 Deploying Yasmeen AI Platform with Enhanced Super Admin Dashboard...
echo ==================================================================

REM Check if we're in the right directory
if not exist "backend" (
    echo ❌ Error: backend directory not found
    echo    Please run this script from the project root directory
    pause
    exit /b 1
)

if not exist "frontend" (
    echo ❌ Error: frontend directory not found
    echo    Please run this script from the project root directory
    pause
    exit /b 1
)

REM Backend Setup
echo 📦 Setting up backend...
cd backend

REM Install Python dependencies if requirements.txt exists
if exist "requirements.txt" (
    echo 📋 Installing Python dependencies...
    pip install -r requirements.txt
)

REM Database setup
echo 🗄️ Setting up database...
python manage.py makemigrations
python manage.py migrate

REM Create admin user
echo 👑 Creating Admin user...
python create_admin.py

REM Collect static files
echo 📁 Collecting static files...
python manage.py collectstatic --noinput

REM Start backend server
echo 🚀 Starting backend server...
start "Yasmeen AI Backend" python manage.py runserver 0.0.0.0:8000

REM Frontend Setup
echo 📦 Setting up frontend...
cd ..\frontend

REM Install Node dependencies if package.json exists
if exist "package.json" (
    echo 📋 Installing Node.js dependencies...
    npm install
)

REM Build frontend for production
echo 🏗️ Building frontend...
npm run build

REM Start frontend server
echo 🚀 Starting frontend server...
start "Yasmeen AI Frontend" npm run preview

echo.
echo ✅ Deployment complete!
echo ==================================================================
echo 🔗 ACCESS URLS:
echo    Frontend: http://localhost:4178
echo    Backend API: http://localhost:8000
echo    Django Admin: http://localhost:8000/admin/
echo.
echo 👑 ADMIN CREDENTIALS:
echo    Username: admin
echo    Email: <EMAIL>
echo    Password: YasmeenAI2024!
echo.
echo 🎯 ADMIN DASHBOARD:
echo    URL: http://localhost:4178/admin/dashboard
echo.
echo 💡 TIPS:
echo    - Both servers are running in separate windows
echo    - Close the terminal windows to stop the servers
echo    - Or use Ctrl+C in each window
echo ==================================================================

REM Create stop script
echo @echo off > stop.bat
echo echo 🛑 Stopping Yasmeen AI Platform... >> stop.bat
echo taskkill /F /IM python.exe /T 2^>nul >> stop.bat
echo taskkill /F /IM node.exe /T 2^>nul >> stop.bat
echo echo ✅ All servers stopped! >> stop.bat
echo pause >> stop.bat

echo 💡 TIP: Use 'stop.bat' to force stop all servers
echo.
pause
