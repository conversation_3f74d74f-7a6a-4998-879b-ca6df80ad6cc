# Generated by Django 5.2.1 on 2025-08-05 14:09

import api.storage
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="EmailVerificationSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "require_verification",
                    models.BooleanField(
                        default=True,
                        help_text="Require email verification for new registrations",
                    ),
                ),
                (
                    "token_expiry_hours",
                    models.PositiveIntegerField(
                        default=24, help_text="Hours until verification token expires"
                    ),
                ),
                (
                    "max_resend_attempts",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum number of verification emails that can be sent per day",
                    ),
                ),
                (
                    "resend_cooldown_minutes",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Minutes to wait between resending verification emails",
                    ),
                ),
            ],
            options={
                "verbose_name": "Email Verification Settings",
                "verbose_name_plural": "Email Verification Settings",
                "db_table": "email_verification_settings",
            },
        ),
        migrations.CreateModel(
            name="PasswordResetSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token_expiry_hours",
                    models.PositiveIntegerField(
                        default=1, help_text="Hours until password reset token expires"
                    ),
                ),
                (
                    "max_attempts_per_email_per_hour",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum password reset attempts per email per hour",
                    ),
                ),
                (
                    "max_attempts_per_ip_per_hour",
                    models.PositiveIntegerField(
                        default=5,
                        help_text="Maximum password reset attempts per IP per hour",
                    ),
                ),
                (
                    "require_email_verification",
                    models.BooleanField(
                        default=True,
                        help_text="Require email to be verified before allowing password reset",
                    ),
                ),
            ],
            options={
                "verbose_name": "Password Reset Settings",
                "verbose_name_plural": "Password Reset Settings",
                "db_table": "password_reset_settings",
            },
        ),
        migrations.CreateModel(
            name="UserRole",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "name",
                    models.CharField(
                        choices=[
                            ("admin", "Administrator"),
                            ("moderator", "Moderator"),
                            ("entrepreneur", "Entrepreneur"),
                            ("mentor", "Mentor"),
                            ("investor", "Investor"),
                            ("user", "Regular User"),
                        ],
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("display_name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "permission_level",
                    models.CharField(
                        choices=[
                            ("read", "Read Only"),
                            ("write", "Read & Write"),
                            ("moderate", "Moderate Content"),
                            ("admin", "Full Admin Access"),
                        ],
                        default="read",
                        max_length=20,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=True,
                        help_text="Whether this role requires admin approval",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "User Role",
                "verbose_name_plural": "User Roles",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="UserApproval",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "rejection_reason",
                    models.TextField(
                        blank=True,
                        help_text="Reason for rejection (sent to user via email)",
                    ),
                ),
                (
                    "admin_notes",
                    models.TextField(blank=True, help_text="Internal notes for admins"),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="user_approvals_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="approval_status",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Approval",
                "verbose_name_plural": "User Approvals",
            },
        ),
        migrations.CreateModel(
            name="UserProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("bio", models.TextField(blank=True, max_length=500)),
                ("location", models.CharField(blank=True, max_length=100)),
                ("birth_date", models.DateField(blank=True, null=True)),
                ("phone_number", models.CharField(blank=True, max_length=20)),
                (
                    "website",
                    models.CharField(
                        blank=True, help_text="Website URL (optional)", max_length=500
                    ),
                ),
                (
                    "linkedin_url",
                    models.CharField(
                        blank=True,
                        help_text="LinkedIn profile URL (optional)",
                        max_length=500,
                    ),
                ),
                (
                    "twitter_url",
                    models.CharField(
                        blank=True,
                        help_text="Twitter profile URL (optional)",
                        max_length=500,
                    ),
                ),
                (
                    "github_url",
                    models.CharField(
                        blank=True,
                        help_text="GitHub profile URL (optional)",
                        max_length=500,
                    ),
                ),
                (
                    "profile_image",
                    models.ImageField(
                        blank=True,
                        help_text="Profile picture (will be optimized automatically)",
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="profile_images/",
                    ),
                ),
                ("company", models.CharField(blank=True, max_length=200)),
                ("job_title", models.CharField(blank=True, max_length=100)),
                ("industry", models.CharField(blank=True, max_length=100)),
                (
                    "experience_years",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MaxValueValidator(50)],
                    ),
                ),
                (
                    "expertise",
                    models.TextField(
                        blank=True,
                        default="",
                        help_text="Areas of expertise and skills",
                        null=True,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("email_notifications", models.BooleanField(default=True)),
                ("marketing_emails", models.BooleanField(default=False)),
                (
                    "profile_visibility",
                    models.CharField(
                        choices=[
                            ("public", "Public"),
                            ("members", "Members Only"),
                            ("private", "Private"),
                        ],
                        default="members",
                        max_length=20,
                    ),
                ),
                (
                    "language",
                    models.CharField(
                        choices=[("en", "English"), ("ar", "Arabic")],
                        default="en",
                        max_length=10,
                    ),
                ),
                (
                    "role_additional_info",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional information specific to user's role",
                    ),
                ),
                (
                    "requested_role_name",
                    models.CharField(
                        blank=True,
                        help_text="Requested role name for manual review if role not found",
                        max_length=50,
                    ),
                ),
                (
                    "business_name",
                    models.CharField(
                        blank=True,
                        help_text="Name of the business or startup",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "business_stage",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("idea", "Idea"),
                            ("prototype", "Prototype"),
                            ("mvp", "MVP"),
                            ("growth", "Growth"),
                            ("established", "Established"),
                        ],
                        help_text="Current stage of the business",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "funding_needed",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("none", "No funding needed"),
                            ("under-50k", "Under $50K"),
                            ("50k-250k", "$50K - $250K"),
                            ("250k-1m", "$250K - $1M"),
                            ("over-1m", "Over $1M"),
                        ],
                        help_text="Amount of funding needed",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "business_description",
                    models.TextField(
                        blank=True,
                        help_text="Description of the business or startup idea",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "team_size",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1", "Just me"),
                            ("2-5", "2-5 people"),
                            ("6-10", "6-10 people"),
                            ("11-20", "11-20 people"),
                            ("20+", "20+ people"),
                        ],
                        help_text="Current team size",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "support_needed",
                    models.TextField(
                        blank=True,
                        help_text="Type of support needed",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "previous_experience",
                    models.TextField(
                        blank=True,
                        help_text="Previous entrepreneurial experience",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "mentor_experience",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1-3", "1-3 years"),
                            ("4-7", "4-7 years"),
                            ("8-15", "8-15 years"),
                            ("15+", "15+ years"),
                        ],
                        help_text="Years of professional experience for mentoring",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "mentorship_areas",
                    models.TextField(
                        blank=True,
                        help_text="Areas where mentor can provide guidance",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "availability",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1-2-hours", "1-2 hours per week"),
                            ("3-5-hours", "3-5 hours per week"),
                            ("5+-hours", "5+ hours per week"),
                            ("flexible", "Flexible"),
                        ],
                        help_text="Time availability for mentoring",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "preferred_communication",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("email", "Email"),
                            ("video-call", "Video Call"),
                            ("phone", "Phone"),
                            ("chat", "Chat/Messaging"),
                            ("in-person", "In-person"),
                        ],
                        help_text="Preferred communication method",
                        max_length=100,
                        null=True,
                    ),
                ),
                (
                    "investment_range",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("10k-50k", "$10K - $50K"),
                            ("50k-250k", "$50K - $250K"),
                            ("250k-1m", "$250K - $1M"),
                            ("1m-5m", "$1M - $5M"),
                            ("over-5m", "Over $5M"),
                        ],
                        help_text="Typical investment range",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "investment_stage",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("pre-seed", "Pre-seed"),
                            ("seed", "Seed"),
                            ("series-a", "Series A"),
                            ("series-b", "Series B"),
                            ("growth", "Growth"),
                        ],
                        help_text="Preferred investment stage",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "preferred_industries",
                    models.TextField(
                        blank=True,
                        help_text="Industries of interest for investment",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "investment_criteria",
                    models.TextField(
                        blank=True,
                        help_text="Investment criteria and requirements",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "portfolio_companies",
                    models.TextField(
                        blank=True,
                        help_text="Previous investments or portfolio companies",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "interests",
                    models.TextField(
                        blank=True,
                        help_text="Areas of interest in entrepreneurship",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "goals",
                    models.TextField(
                        blank=True,
                        help_text="Goals and objectives on the platform",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "portfolio_url",
                    models.CharField(
                        blank=True,
                        help_text="Portfolio or work samples URL (optional)",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "motivation",
                    models.TextField(
                        blank=True,
                        help_text="Motivation for joining the platform",
                        max_length=1000,
                        null=True,
                    ),
                ),
                (
                    "qualifications",
                    models.TextField(
                        blank=True,
                        help_text="Qualifications and background",
                        max_length=1000,
                        null=True,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "last_activity",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "User Profile",
                "verbose_name_plural": "User Profiles",
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("attempted_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("success", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="verification_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "email_verification_attempts",
                "ordering": ["-attempted_at"],
                "indexes": [
                    models.Index(
                        fields=["user", "attempted_at"],
                        name="email_verif_user_id_b42034_idx",
                    ),
                    models.Index(
                        fields=["email", "attempted_at"],
                        name="email_verif_email_d231cd_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="email_verif_ip_addr_2e565d_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="EmailVerificationToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("email", models.EmailField(max_length=254)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="email_verification_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "email_verification_tokens",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(fields=["token"], name="email_verif_token_df7c5e_idx"),
                    models.Index(
                        fields=["user", "is_used"],
                        name="email_verif_user_id_35194a_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="email_verif_expires_770728_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetAttempt",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("attempted_at", models.DateTimeField(auto_now_add=True)),
                ("success", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_attempts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "password_reset_attempts",
                "ordering": ["-attempted_at"],
                "indexes": [
                    models.Index(
                        fields=["email", "attempted_at"],
                        name="password_re_email_eb0c38_idx",
                    ),
                    models.Index(
                        fields=["ip_address", "attempted_at"],
                        name="password_re_ip_addr_a19a3f_idx",
                    ),
                    models.Index(
                        fields=["user", "attempted_at"],
                        name="password_re_user_id_51fcf5_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="PasswordResetToken",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                ("secure_hash", models.CharField(max_length=128)),
                ("email", models.EmailField(max_length=254)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("is_used", models.BooleanField(default=False)),
                ("used_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="password_reset_tokens",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "password_reset_tokens",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(fields=["token"], name="password_re_token_060a1f_idx"),
                    models.Index(
                        fields=["user", "is_used"],
                        name="password_re_user_id_cd37a3_idx",
                    ),
                    models.Index(
                        fields=["expires_at"], name="password_re_expires_8e96b7_idx"
                    ),
                    models.Index(fields=["email"], name="password_re_email_2bb9da_idx"),
                ],
            },
        ),
        migrations.CreateModel(
            name="RoleApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "motivation",
                    models.TextField(help_text="Why do you want this role?"),
                ),
                (
                    "qualifications",
                    models.TextField(help_text="What qualifies you for this role?"),
                ),
                (
                    "experience",
                    models.TextField(blank=True, help_text="Relevant experience"),
                ),
                (
                    "portfolio_url",
                    models.URLField(blank=True, help_text="Portfolio or work samples"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("withdrawn", "Withdrawn"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "admin_notes",
                    models.TextField(blank=True, help_text="Internal notes for admins"),
                ),
                (
                    "company_name",
                    models.CharField(
                        blank=True, help_text="Company or project name", max_length=200
                    ),
                ),
                (
                    "project_stage",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("idea", "Just an Idea"),
                            ("planning", "Planning Stage"),
                            ("prototype", "Prototype"),
                            ("mvp", "MVP (Minimum Viable Product)"),
                            ("launched", "Launched"),
                            ("scaling", "Scaling"),
                        ],
                        help_text="Current stage of the project",
                        max_length=50,
                    ),
                ),
                (
                    "industry",
                    models.CharField(
                        blank=True, help_text="Industry or sector", max_length=100
                    ),
                ),
                (
                    "project_description",
                    models.TextField(
                        blank=True, help_text="Detailed description of the project/idea"
                    ),
                ),
                (
                    "funding_needed",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("0-10k", "Less than $10,000"),
                            ("10k-50k", "$10,000 - $50,000"),
                            ("50k-100k", "$50,000 - $100,000"),
                            ("100k-500k", "$100,000 - $500,000"),
                            ("500k-1m", "$500,000 - $1,000,000"),
                            ("1m+", "More than $1,000,000"),
                            ("no-funding", "No funding needed currently"),
                        ],
                        help_text="Amount of funding needed",
                        max_length=50,
                    ),
                ),
                (
                    "team_size",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("solo", "Solo Founder"),
                            ("2-3", "2-3 People"),
                            ("4-10", "4-10 People"),
                            ("11-25", "11-25 People"),
                            ("25+", "More than 25 People"),
                        ],
                        help_text="Current team size",
                        max_length=20,
                    ),
                ),
                (
                    "support_needed",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Types of support needed (JSON array)",
                    ),
                ),
                (
                    "previous_experience",
                    models.TextField(
                        blank=True, help_text="Previous entrepreneurship experience"
                    ),
                ),
                (
                    "expertise_areas",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Areas of expertise (JSON array)",
                    ),
                ),
                (
                    "mentoring_experience",
                    models.TextField(
                        blank=True, help_text="Previous mentoring experience"
                    ),
                ),
                (
                    "availability",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1-2h/week", "1-2 hours per week"),
                            ("3-5h/week", "3-5 hours per week"),
                            ("5-10h/week", "5-10 hours per week"),
                            ("10+h/week", "More than 10 hours per week"),
                            ("flexible", "Flexible schedule"),
                        ],
                        help_text="Time availability for mentoring",
                        max_length=50,
                    ),
                ),
                (
                    "preferred_communication",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Preferred communication methods",
                    ),
                ),
                (
                    "investment_range",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("1k-10k", "$1,000 - $10,000"),
                            ("10k-50k", "$10,000 - $50,000"),
                            ("50k-100k", "$50,000 - $100,000"),
                            ("100k-500k", "$100,000 - $500,000"),
                            ("500k-1m", "$500,000 - $1,000,000"),
                            ("1m-5m", "$1,000,000 - $5,000,000"),
                            ("5m+", "More than $5,000,000"),
                        ],
                        help_text="Typical investment range",
                        max_length=50,
                    ),
                ),
                (
                    "investment_focus",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Investment focus areas (JSON array)",
                    ),
                ),
                (
                    "investment_stage",
                    models.JSONField(
                        blank=True,
                        default=list,
                        help_text="Preferred investment stages",
                    ),
                ),
                (
                    "portfolio_companies",
                    models.TextField(
                        blank=True,
                        help_text="Previous investments or portfolio companies",
                    ),
                ),
                (
                    "due_diligence_requirements",
                    models.TextField(
                        blank=True, help_text="Due diligence requirements"
                    ),
                ),
                (
                    "interests",
                    models.TextField(blank=True, help_text="Areas of interest"),
                ),
                (
                    "goals",
                    models.TextField(
                        blank=True, help_text="Personal or professional goals"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_reviews_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="role_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "requested_role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.userrole"
                    ),
                ),
            ],
            options={
                "verbose_name": "Role Application",
                "verbose_name_plural": "Role Applications",
                "ordering": ["-created_at"],
                "unique_together": {("user", "requested_role")},
            },
        ),
        migrations.CreateModel(
            name="UserRoleAssignment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("assigned_at", models.DateTimeField(auto_now_add=True)),
                ("is_active", models.BooleanField(default=True)),
                ("is_approved", models.BooleanField(default=True)),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional expiration date for temporary roles",
                        null=True,
                    ),
                ),
                (
                    "notes",
                    models.TextField(
                        blank=True, help_text="Notes about this role assignment"
                    ),
                ),
                (
                    "approved_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_approvals_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "assigned_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="role_assignments_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "role",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to="users.userrole"
                    ),
                ),
                (
                    "user_profile",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="users.userprofile",
                    ),
                ),
            ],
            options={
                "verbose_name": "User Role Assignment",
                "verbose_name_plural": "User Role Assignments",
                "ordering": ["-assigned_at"],
                "unique_together": {("user_profile", "role")},
            },
        ),
    ]
