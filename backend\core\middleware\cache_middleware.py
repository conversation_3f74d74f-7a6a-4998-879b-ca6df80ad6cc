"""
Performance Optimization: API Response Caching Middleware
"""
import hashlib
import json
from django.core.cache import cache
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


class APIResponseCacheMiddleware(MiddlewareMixin):
    """
    Middleware to cache API responses for improved performance
    """
    
    # Cache timeout in seconds (5 minutes for most endpoints)
    CACHE_TIMEOUT = 300
    
    # Endpoints to cache (GET requests only)
    CACHEABLE_ENDPOINTS = [
        '/api/users/profile/',
        '/api/users/roles/',
        '/api/community/posts/',
        '/api/incubator/programs/',
        '/api/events/',
    ]
    
    # Endpoints to exclude from caching
    EXCLUDE_ENDPOINTS = [
        '/api/auth/login/',
        '/api/auth/logout/',
        '/api/auth/register/',
        '/api/auth/refresh/',
    ]
    
    def process_request(self, request):
        """
        Check if we have a cached response for this request
        """
        # Only cache GET requests
        if request.method != 'GET':
            return None
        
        # Check if endpoint should be cached
        if not self._should_cache_endpoint(request.path):
            return None
        
        # Generate cache key
        cache_key = self._generate_cache_key(request)
        
        # Try to get cached response
        cached_response = cache.get(cache_key)
        if cached_response:
            # Return cached response
            response = JsonResponse(cached_response['data'])
            response.status_code = cached_response['status_code']
            response['X-Cache'] = 'HIT'
            return response
        
        # Store cache key in request for use in process_response
        request._cache_key = cache_key
        return None
    
    def process_response(self, request, response):
        """
        Cache the response if appropriate
        """
        # Only cache successful GET responses
        if (request.method == 'GET' and 
            response.status_code == 200 and
            hasattr(request, '_cache_key') and
            self._should_cache_endpoint(request.path)):
            
            try:
                # Only cache JSON responses
                if response.get('Content-Type', '').startswith('application/json'):
                    # Parse response data
                    response_data = json.loads(response.content.decode('utf-8'))
                    
                    # Cache the response
                    cache_data = {
                        'data': response_data,
                        'status_code': response.status_code
                    }
                    
                    cache.set(request._cache_key, cache_data, self.CACHE_TIMEOUT)
                    response['X-Cache'] = 'MISS'
                
            except (json.JSONDecodeError, UnicodeDecodeError):
                # Skip caching if response is not valid JSON
                pass
        
        return response
    
    def _should_cache_endpoint(self, path):
        """
        Determine if an endpoint should be cached
        """
        # Check exclude list first
        for exclude_path in self.EXCLUDE_ENDPOINTS:
            if path.startswith(exclude_path):
                return False
        
        # Check include list
        for cache_path in self.CACHEABLE_ENDPOINTS:
            if path.startswith(cache_path):
                return True
        
        return False
    
    def _generate_cache_key(self, request):
        """
        Generate a unique cache key for the request
        """
        # Include path, query parameters, and user ID (if authenticated)
        key_parts = [
            request.path,
            request.GET.urlencode(),
        ]
        
        # Include user ID for user-specific caching
        if hasattr(request, 'user') and request.user.is_authenticated:
            key_parts.append(f"user_{request.user.id}")
        
        # Create hash of key parts
        key_string = '|'.join(key_parts)
        cache_key = hashlib.md5(key_string.encode('utf-8')).hexdigest()
        
        return f"api_cache_{cache_key}"


class DatabaseQueryCacheMiddleware(MiddlewareMixin):
    """
    Middleware to cache database query results
    """
    
    def process_request(self, request):
        """
        Set up query caching for this request
        """
        # Enable query caching for read-only requests
        if request.method in ['GET', 'HEAD', 'OPTIONS']:
            request._enable_query_cache = True
        
        return None


# Cache configuration for Django settings
CACHE_SETTINGS = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'yasmeen-ai-cache',
        'TIMEOUT': 300,  # 5 minutes default
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
            'CULL_FREQUENCY': 3,
        }
    }
}

# Performance monitoring decorator
def monitor_performance(func):
    """
    Decorator to monitor function performance
    """
    import time
    import logging
    
    logger = logging.getLogger('performance')
    
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        duration = time.time() - start_time
        
        if duration > 1.0:  # Log slow operations
            logger.warning(f"Slow operation: {func.__name__} took {duration:.3f}s")
        
        return result
    
    return wrapper
