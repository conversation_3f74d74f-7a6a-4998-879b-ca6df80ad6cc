// A/B Test Admin Dashboard
import React, { useState, useEffect } from 'react';
import { 
  ABTestExperiment, 
  ABTestResults, 
  AnalyticsDashboardData,
  ExperimentFormData 
} from '../../types/abtest';
import { abTestManager } from '../../services/ABTestManager';
import {
  BarChart3,
  Users,
  Target,
  Plus,
  Play,
  Pause,
  Trash2,
  RefreshCw
} from 'lucide-react';

const ABTestDashboard: React.FC = () => {
  const [experiments, setExperiments] = useState<ABTestExperiment[]>([]);
  const [dashboardData, setDashboardData] = useState<AnalyticsDashboardData | null>(null);
  const [selectedExperiment, setSelectedExperiment] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      const allExperiments = abTestManager.getAllExperiments();
      setExperiments(allExperiments);
      
      // Calculate dashboard metrics
      const activeExperiments = allExperiments.filter(exp => exp.status === 'running').length;
      const completedExperiments = allExperiments.filter(exp => exp.status === 'completed').length;
      
      let totalParticipants = 0;
      let totalConversions = 0;
      const topPerformingVariants: Array<{ id: string; name: string; conversionRate: number }> = [];
      
      allExperiments.forEach(exp => {
        const metrics = abTestManager.getExperimentMetrics(exp.id);
        metrics.forEach(metric => {
          totalParticipants += metric.totalUsers;
          totalConversions += metric.conversions;
          
          if (metric.conversionRate > 0) {
            topPerformingVariants.push({
              experimentName: exp.name,
              variantName: exp.variants.find(v => v.id === metric.variantId)?.name || 'Unknown',
              conversionRate: metric.conversionRate,
              uplift: 0 // Will be calculated properly in real implementation
            });
          }
        });
      });
      
      const averageConversionRate = totalParticipants > 0 ? (totalConversions / totalParticipants) * 100 : 0;
      
      setDashboardData({
        totalExperiments: allExperiments.length,
        activeExperiments,
        completedExperiments,
        totalParticipants,
        averageConversionRate,
        topPerformingVariants: topPerformingVariants
          .sort((a, b) => b.conversionRate - a.conversionRate)
          .slice(0, 5),
        recentEvents: [] // Would be populated from event tracking
      });
      
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateExperiment = (formData: ExperimentFormData) => {
    try {
      abTestManager.createExperiment({
        name: formData.name,
        description: formData.description,
        status: 'draft',
        startDate: formData.startDate,
        endDate: formData.endDate,
        targetMetric: formData.targetMetric,
        variants: formData.variants.map((variant, index) => ({
          id: `variant_${index}`,
          ...variant
        })),
        trafficAllocation: formData.trafficAllocation,
        createdBy: 'admin' // Would be actual user in real implementation
      });
      
      setShowCreateForm(false);
      loadDashboardData();
      
    } catch (error) {
      console.error('Error creating experiment:', error);
    }
  };

  const handleToggleExperiment = (experimentId: string, currentStatus: string) => {
    const newStatus = currentStatus === 'running' ? 'paused' : 'running';
    abTestManager.updateExperiment(experimentId, { status: newStatus });
    loadDashboardData();
  };

  const handleDeleteExperiment = (experimentId: string) => {
    if (window.confirm('Are you sure you want to delete this experiment?')) {
      abTestManager.deleteExperiment(experimentId);
      loadDashboardData();
    }
  };

  const getExperimentResults = (experimentId: string): ABTestResults | null => {
    return abTestManager.getExperimentResults(experimentId);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
        <span className="ml-2 text-gray-600">Loading dashboard...</span>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">A/B Testing Dashboard</h1>
            <p className="text-gray-600 mt-2">Manage and analyze your experiments</p>
          </div>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center"
          >
            <Plus className="w-4 h-4 mr-2" />
            New Experiment
          </button>
        </div>
      </div>

      {/* Dashboard Stats */}
      {dashboardData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <BarChart3 className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Experiments</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.totalExperiments}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Play className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Experiments</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.activeExperiments}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Participants</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.totalParticipants.toLocaleString()}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg shadow">
            <div className="flex items-center">
              <Target className="w-8 h-8 text-orange-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg. Conversion Rate</p>
                <p className="text-2xl font-bold text-gray-900">{dashboardData.averageConversionRate.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Experiments List */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Experiments</h2>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Participants
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Conversion Rate
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {experiments.map((experiment) => {
                const results = getExperimentResults(experiment.id);
                const totalParticipants = results?.totalParticipants || 0;
                const avgConversionRate = results?.metrics.reduce((sum, m) => sum + m.conversionRate, 0) / (results?.metrics.length || 1);
                
                return (
                  <tr key={experiment.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{experiment.name}</div>
                        <div className="text-sm text-gray-500">{experiment.description}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        experiment.status === 'running' ? 'bg-green-100 text-green-800' :
                        experiment.status === 'paused' ? 'bg-yellow-100 text-yellow-800' :
                        experiment.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {experiment.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {totalParticipants.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {avgConversionRate.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {results?.duration || 0} days
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleToggleExperiment(experiment.id, experiment.status)}
                          className={`${
                            experiment.status === 'running' ? 'text-yellow-600 hover:text-yellow-900' : 'text-green-600 hover:text-green-900'
                          }`}
                        >
                          {experiment.status === 'running' ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                        </button>
                        <button
                          onClick={() => setSelectedExperiment(experiment.id)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <BarChart3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteExperiment(experiment.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Experiment Modal */}
      {showCreateForm && (
        <CreateExperimentModal
          onClose={() => setShowCreateForm(false)}
          onSubmit={handleCreateExperiment}
        />
      )}

      {/* Experiment Details Modal */}
      {selectedExperiment && (
        <ExperimentDetailsModal
          experimentId={selectedExperiment}
          onClose={() => setSelectedExperiment(null)}
        />
      )}
    </div>
  );
};

// TODO: Implement placeholder components for modals when needed
// const CreateExperimentModal: React.FC<{
//   onClose: () => void;
//   onSubmit: (data: ExperimentFormData) => void;
// }> = ({ onClose, onSubmit }) => {
//   // Implementation would go here
//   return <div>Create Experiment Modal</div>;
// };

// const ExperimentDetailsModal: React.FC<{
//   experimentId: string;
//   onClose: () => void;
// }> = ({ experimentId, onClose }) => {
//   // Implementation would go here
//   return <div>Experiment Details Modal</div>;
// };

export default ABTestDashboard;
