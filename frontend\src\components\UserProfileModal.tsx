import React, { useState, useEffect, useCallback } from 'react';
import { X, User, Mail, MapPin, Globe, Linkedin, Phone, Building, Briefcase, Clock } from 'lucide-react';
import { userAPI, User as UserType } from '../services/api';
import { useLanguage } from './LanguageProvider';

interface UserProfileModalProps {
  userId: string | number;
  isRTL: boolean;
  isOpen: boolean;
  onClose: () => void;
}

const UserProfileModal: React.FC<UserProfileModalProps> = ({
  userId,
  isRTL,
  isOpen,
  onClose
}) => {
  const [user, setUser] = useState<UserType | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { language } = useLanguage();

  useEffect(() => {
    if (isOpen && userId) {
      fetchUserProfile();
    }
  }, [isOpen, userId, fetchUserProfile]);

  const fetchUserProfile = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const userData = await userAPI.getUserProfile(userId.toString());
      setUser(userData);
    } catch (err) {
      console.error('Failed to fetch user profile:', err);
      setError('Failed to load user profile');
    } finally {
      setLoading(false);
    }
  }, [userId]);

  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="bg-indigo-900/90 rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className={`flex justify-between items-center mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <h2 className="text-xl font-bold text-white">
              {language === 'ar' ? 'الملف الشخصي' : 'User Profile'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X size={20} />
            </button>
          </div>

          {/* Content */}
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <p className="text-red-400 mb-4">{error}</p>
              <button
                onClick={fetchUserProfile}
                className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                {language === 'ar' ? 'إعادة المحاولة' : 'Retry'}
              </button>
            </div>
          ) : user ? (
            <div className="space-y-6">
              {/* Profile Header */}
              <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="relative">
                  {user.profile?.profile_image ? (
                    <img
                      src={user.profile.profile_image}
                      alt={`${user.first_name} ${user.last_name}`}
                      className="w-16 h-16 rounded-full object-cover border-2 border-purple-500"
                    />
                  ) : (
                    <div className="w-16 h-16 rounded-full bg-purple-600 flex items-center justify-center">
                      <User size={24} className="text-white" />
                    </div>
                  )}
                </div>
                <div className={`flex-1 ${isRTL ? 'text-right' : 'text-left'}`}>
                  <h3 className="text-lg font-semibold text-white">
                    {user.first_name} {user.last_name}
                  </h3>
                  <p className="text-gray-300">@{user.username}</p>
                  {user.profile?.job_title && (
                    <p className="text-purple-300 text-sm">{user.profile.job_title}</p>
                  )}
                </div>
              </div>

              {/* Bio */}
              {user.profile?.bio && (
                <div className="bg-indigo-800/30 rounded-lg p-4">
                  <p className="text-gray-300 leading-relaxed">{user.profile.bio}</p>
                </div>
              )}

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Mail size={16} className="text-purple-400" />
                  <span className="text-gray-300">{user.email}</span>
                </div>
                
                {user.profile?.phone_number && (
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Phone size={16} className="text-purple-400" />
                    <span className="text-gray-300">{user.profile.phone_number}</span>
                  </div>
                )}
                
                {user.profile?.location && (
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <MapPin size={16} className="text-purple-400" />
                    <span className="text-gray-300">{user.profile.location}</span>
                  </div>
                )}
                
                {user.profile?.company && (
                  <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Building size={16} className="text-purple-400" />
                    <span className="text-gray-300">{user.profile.company}</span>
                  </div>
                )}
              </div>

              {/* Professional Information */}
              {(user.profile?.industry || user.profile?.experience_years) && (
                <div className="bg-indigo-800/30 rounded-lg p-4">
                  <h4 className="text-white font-semibold mb-3">
                    {language === 'ar' ? 'المعلومات المهنية' : 'Professional Information'}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {user.profile?.industry && (
                      <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Briefcase size={16} className="text-purple-400" />
                        <span className="text-gray-300">{user.profile.industry}</span>
                      </div>
                    )}
                    
                    {user.profile?.experience_years && (
                      <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <Clock size={16} className="text-purple-400" />
                        <span className="text-gray-300">
                          {user.profile.experience_years} {language === 'ar' ? 'سنوات خبرة' : 'years experience'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Social Links */}
              {(user.profile?.website || user.profile?.linkedin_url) && (
                <div className="bg-indigo-800/30 rounded-lg p-4">
                  <h4 className="text-white font-semibold mb-3">
                    {language === 'ar' ? 'الروابط الاجتماعية' : 'Social Links'}
                  </h4>
                  <div className="flex flex-wrap gap-4">
                    {user.profile?.website && (
                      <a
                        href={user.profile.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        <Globe size={16} />
                        <span>{language === 'ar' ? 'الموقع الإلكتروني' : 'Website'}</span>
                      </a>
                    )}
                    
                    {user.profile?.linkedin_url && (
                      <a
                        href={user.profile.linkedin_url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-2 text-purple-400 hover:text-purple-300 transition-colors"
                      >
                        <Linkedin size={16} />
                        <span>LinkedIn</span>
                      </a>
                    )}
                  </div>
                </div>
              )}

              {/* Member Since */}
              <div className="text-center pt-4 border-t border-indigo-700/50">
                <p className="text-gray-400 text-sm">
                  {language === 'ar' ? 'عضو منذ' : 'Member since'} {new Date(user.date_joined).toLocaleDateString()}
                </p>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-400">
                {language === 'ar' ? 'لم يتم العثور على المستخدم' : 'User not found'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserProfileModal;
