/**
 * 🔒 SECURITY SERVICE
 * Comprehensive security hardening and monitoring
 */

import { api } from './api';

export interface SecurityThreat {
  id: string;
  type: 'xss' | 'csrf' | 'injection' | 'brute_force' | 'suspicious_activity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  source: string;
  timestamp: string;
  blocked: boolean;
  details: Record<string, any>;
}

export interface SecurityConfig {
  csp: {
    enabled: boolean;
    directives: Record<string, string[]>;
  };
  xss: {
    enabled: boolean;
    sanitizeInputs: boolean;
    blockSuspiciousPatterns: boolean;
  };
  csrf: {
    enabled: boolean;
    tokenValidation: boolean;
  };
  rateLimit: {
    enabled: boolean;
    maxRequests: number;
    windowMs: number;
  };
  monitoring: {
    enabled: boolean;
    logLevel: 'info' | 'warn' | 'error';
    alertThreshold: number;
  };
}

class SecurityService {
  private threats: SecurityThreat[] = [];
  private config: SecurityConfig;
  private requestCounts = new Map<string, { count: number; resetTime: number }>();
  private suspiciousPatterns = [
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /document\.cookie/gi,
    /window\.location/gi,
    /<iframe/gi,
    /data:text\/html/gi
  ];

  constructor() {
    this.config = this.getDefaultConfig();
    this.setupSecurityHeaders();
    this.setupCSP();
    this.setupXSSProtection();
    this.setupEventListeners();
  }

  /**
   * Get default security configuration
   */
  private getDefaultConfig(): SecurityConfig {
    return {
      csp: {
        enabled: true,
        directives: {
          'default-src': ["'self'"],
          'script-src': ["'self'", "'unsafe-inline'", 'https://apis.google.com'],
          'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
          'img-src': ["'self'", 'data:', 'https:'],
          'font-src': ["'self'", 'https://fonts.gstatic.com'],
          'connect-src': ["'self'", 'wss:', 'https:'],
          'frame-ancestors': ["'none'"],
          'base-uri': ["'self'"],
          'form-action': ["'self'"]
        }
      },
      xss: {
        enabled: true,
        sanitizeInputs: true,
        blockSuspiciousPatterns: true
      },
      csrf: {
        enabled: true,
        tokenValidation: true
      },
      rateLimit: {
        enabled: true,
        maxRequests: 100,
        windowMs: 60000 // 1 minute
      },
      monitoring: {
        enabled: true,
        logLevel: 'warn',
        alertThreshold: 5
      }
    };
  }

  /**
   * Setup security headers
   */
  private setupSecurityHeaders(): void {
    // These would typically be set by the server, but we can validate them
    const expectedHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Referrer-Policy': 'strict-origin-when-cross-origin'
    };

    // Validate security headers on API responses
    this.validateSecurityHeaders(expectedHeaders);
  }

  /**
   * Setup Content Security Policy
   */
  private setupCSP(): void {
    if (!this.config.csp.enabled) return;

    const cspString = Object.entries(this.config.csp.directives)
      .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
      .join('; ');

    // Create meta tag for CSP if not already present
    if (!document.querySelector('meta[http-equiv="Content-Security-Policy"]')) {
      const meta = document.createElement('meta');
      meta.httpEquiv = 'Content-Security-Policy';
      meta.content = cspString;
      document.head.appendChild(meta);
    }
  }

  /**
   * Setup XSS protection
   */
  private setupXSSProtection(): void {
    if (!this.config.xss.enabled) return;

    // Override dangerous functions
    this.overrideDangerousFunctions();
    
    // Monitor DOM mutations for suspicious content
    this.setupDOMMonitoring();
  }

  /**
   * Override dangerous functions
   */
  private overrideDangerousFunctions(): void {
    const originalEval = window.eval;
    window.eval = (code: string) => {
      this.logThreat({
        type: 'xss',
        severity: 'high',
        description: 'Attempted eval() execution blocked',
        source: 'eval-override',
        details: { code: code.substring(0, 100) }
      });
      throw new Error('eval() is disabled for security reasons');
    };

    // Override innerHTML setter
    const originalInnerHTML = Object.getOwnPropertyDescriptor(Element.prototype, 'innerHTML');
    if (originalInnerHTML) {
      Object.defineProperty(Element.prototype, 'innerHTML', {
        set: function(value: string) {
          const sanitized = this.sanitizeHTML(value);
          if (sanitized !== value) {
            this.logThreat({
              type: 'xss',
              severity: 'medium',
              description: 'Suspicious HTML content sanitized',
              source: 'innerHTML-override',
              details: { original: value, sanitized }
            });
          }
          originalInnerHTML!.set!.call(this, sanitized);
        },
        get: originalInnerHTML.get
      });
    }
  }

  /**
   * Setup DOM monitoring
   */
  private setupDOMMonitoring(): void {
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.scanElementForThreats(node as Element);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['onclick', 'onload', 'onerror', 'src', 'href']
    });
  }

  /**
   * Scan element for security threats
   */
  private scanElementForThreats(element: Element): void {
    // Check for suspicious attributes
    const suspiciousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover'];
    suspiciousAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        this.logThreat({
          type: 'xss',
          severity: 'high',
          description: `Suspicious ${attr} attribute detected`,
          source: 'dom-monitoring',
          details: { 
            tagName: element.tagName,
            attribute: attr,
            value: element.getAttribute(attr)
          }
        });
        element.removeAttribute(attr);
      }
    });

    // Check for suspicious URLs
    const urlAttrs = ['src', 'href'];
    urlAttrs.forEach(attr => {
      const value = element.getAttribute(attr);
      if (value && this.isSuspiciousURL(value)) {
        this.logThreat({
          type: 'xss',
          severity: 'medium',
          description: `Suspicious URL blocked: ${attr}`,
          source: 'url-validation',
          details: { url: value }
        });
        element.removeAttribute(attr);
      }
    });
  }

  /**
   * Check if URL is suspicious
   */
  private isSuspiciousURL(url: string): boolean {
    const suspiciousPatterns = [
      /^javascript:/i,
      /^data:text\/html/i,
      /^vbscript:/i,
      /^file:/i
    ];

    return suspiciousPatterns.some(pattern => pattern.test(url));
  }

  /**
   * Sanitize HTML content
   */
  private sanitizeHTML(html: string): string {
    if (!this.config.xss.sanitizeInputs) return html;

    let sanitized = html;

    // Remove suspicious patterns
    this.suspiciousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    // Remove dangerous attributes
    sanitized = sanitized.replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '');

    return sanitized;
  }

  /**
   * Validate input for XSS
   */
  validateInput(input: string): { isValid: boolean; sanitized: string; threats: string[] } {
    const threats: string[] = [];
    let sanitized = input;

    if (this.config.xss.blockSuspiciousPatterns) {
      this.suspiciousPatterns.forEach((pattern, index) => {
        if (pattern.test(input)) {
          threats.push(`Suspicious pattern ${index + 1} detected`);
          sanitized = sanitized.replace(pattern, '');
        }
      });
    }

    return {
      isValid: threats.length === 0,
      sanitized,
      threats
    };
  }

  /**
   * Rate limiting check
   */
  checkRateLimit(identifier: string): boolean {
    if (!this.config.rateLimit.enabled) return true;

    const now = Date.now();
    const windowStart = now - this.config.rateLimit.windowMs;
    
    const current = this.requestCounts.get(identifier);
    
    if (!current || current.resetTime < windowStart) {
      this.requestCounts.set(identifier, { count: 1, resetTime: now });
      return true;
    }

    if (current.count >= this.config.rateLimit.maxRequests) {
      this.logThreat({
        type: 'brute_force',
        severity: 'medium',
        description: 'Rate limit exceeded',
        source: 'rate-limiter',
        details: { identifier, count: current.count }
      });
      return false;
    }

    current.count++;
    return true;
  }

  /**
   * Log security threat
   */
  private logThreat(threat: Omit<SecurityThreat, 'id' | 'timestamp' | 'blocked'>): void {
    const fullThreat: SecurityThreat = {
      ...threat,
      id: `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      blocked: true
    };

    this.threats.push(fullThreat);

    // Keep only last 1000 threats
    if (this.threats.length > 1000) {
      this.threats = this.threats.slice(-1000);
    }

    // Log to console based on severity
    const logLevel = this.config.monitoring.logLevel;
    if (
      (logLevel === 'info') ||
      (logLevel === 'warn' && ['medium', 'high', 'critical'].includes(threat.severity)) ||
      (logLevel === 'error' && ['high', 'critical'].includes(threat.severity))
    ) {
      console.warn('🔒 Security Threat Detected:', fullThreat);
    }

    // Send to backend if monitoring enabled
    if (this.config.monitoring.enabled) {
      this.reportThreatToBackend(fullThreat);
    }

    // Check alert threshold
    const recentThreats = this.threats.filter(
      t => Date.now() - new Date(t.timestamp).getTime() < 300000 // 5 minutes
    );

    if (recentThreats.length >= this.config.monitoring.alertThreshold) {
      this.triggerSecurityAlert(recentThreats);
    }
  }

  /**
   * Report threat to backend
   */
  private async reportThreatToBackend(threat: SecurityThreat): Promise<void> {
    try {
      await api.post('/api/security/threats/', threat);
    } catch (error) {
      console.error('Failed to report security threat:', error);
    }
  }

  /**
   * Trigger security alert
   */
  private triggerSecurityAlert(threats: SecurityThreat[]): void {
    console.error('🚨 SECURITY ALERT: Multiple threats detected!', threats);
    
    // Could trigger notifications, disable features, etc.
    if (threats.some(t => t.severity === 'critical')) {
      // Critical threat response
      this.enableLockdownMode();
    }
  }

  /**
   * Enable lockdown mode
   */
  private enableLockdownMode(): void {
    console.warn('🔒 Security lockdown mode enabled');
    
    // Disable dangerous features
    this.config.xss.enabled = true;
    this.config.csrf.enabled = true;
    this.config.rateLimit.maxRequests = Math.floor(this.config.rateLimit.maxRequests / 2);
  }

  /**
   * Validate security headers
   */
  private validateSecurityHeaders(expectedHeaders: Record<string, string>): void {
    // This would be called on API responses
    // Implementation depends on how you intercept responses
  }

  /**
   * Setup event listeners
   */
  private setupEventListeners(): void {
    // Monitor for suspicious events
    document.addEventListener('click', (e) => {
      const target = e.target as Element;
      if (target.tagName === 'A') {
        const href = target.getAttribute('href');
        if (href && this.isSuspiciousURL(href)) {
          e.preventDefault();
          this.logThreat({
            type: 'xss',
            severity: 'medium',
            description: 'Suspicious link click blocked',
            source: 'click-monitor',
            details: { href }
          });
        }
      }
    });
  }

  /**
   * Get security status
   */
  getSecurityStatus(): {
    threatsDetected: number;
    recentThreats: SecurityThreat[];
    config: SecurityConfig;
    isSecure: boolean;
  } {
    const recentThreats = this.threats.filter(
      t => Date.now() - new Date(t.timestamp).getTime() < 3600000 // 1 hour
    );

    return {
      threatsDetected: this.threats.length,
      recentThreats,
      config: this.config,
      isSecure: recentThreats.filter(t => ['high', 'critical'].includes(t.severity)).length === 0
    };
  }

  /**
   * Update security configuration
   */
  updateConfig(newConfig: Partial<SecurityConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Clear threat history
   */
  clearThreats(): void {
    this.threats = [];
  }
}

export const securityService = new SecurityService();
