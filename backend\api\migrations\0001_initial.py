# Generated by Django 5.2.1 on 2025-08-05 14:09

import api.storage
import django.db.models.deletion
import django.utils.timezone
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="Tag",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=50, unique=True)),
                ("slug", models.SlugField(max_length=100, unique=True)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="Event",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("date", models.DateTimeField()),
                ("location", models.CharField(max_length=200)),
                ("is_virtual", models.BooleanField(default=False)),
                ("virtual_link", models.URLField(blank=True, null=True)),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="event_images/",
                    ),
                ),
                (
                    "moderation_status",
                    models.CharField(
                        choices=[
                            ("approved", "Approved"),
                            ("pending", "Pending Review"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("moderation_comment", models.TextField(blank=True, null=True)),
                ("moderated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "attendees",
                    models.ManyToManyField(
                        blank=True,
                        related_name="attending_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "moderated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderated_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "organizer",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="organized_events",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="MembershipApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("full_name", models.CharField(max_length=100)),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.CharField(blank=True, max_length=20, null=True)),
                ("location", models.CharField(max_length=100)),
                (
                    "expertise_areas",
                    models.TextField(help_text="Areas of expertise in AI/Data Science"),
                ),
                (
                    "expertise_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "background",
                    models.TextField(
                        help_text="Educational and professional background"
                    ),
                ),
                (
                    "motivation",
                    models.TextField(help_text="Motivation for joining the community"),
                ),
                ("linkedin_profile", models.URLField(blank=True, null=True)),
                ("github_profile", models.URLField(blank=True, null=True)),
                ("portfolio_url", models.URLField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("reviewed_at", models.DateTimeField(blank=True, null=True)),
                ("review_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "reviewed_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="reviewed_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="membership_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Post",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                (
                    "excerpt",
                    models.TextField(
                        help_text="Brief summary of the post", max_length=500
                    ),
                ),
                ("content", models.TextField()),
                (
                    "post_type",
                    models.CharField(
                        choices=[
                            ("article", "Article"),
                            ("discussion", "Discussion"),
                            ("question", "Question"),
                            ("announcement", "Announcement"),
                            ("tutorial", "Tutorial"),
                            ("news", "News"),
                        ],
                        default="article",
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("general", "General"),
                            ("business", "Business"),
                            ("technology", "Technology"),
                            ("marketing", "Marketing"),
                            ("finance", "Finance"),
                            ("legal", "Legal"),
                            ("operations", "Operations"),
                            ("product_development", "Product Development"),
                            ("strategy", "Strategy"),
                            ("networking", "Networking"),
                        ],
                        default="general",
                        max_length=30,
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="post_images/",
                    ),
                ),
                (
                    "featured_image",
                    models.URLField(
                        blank=True, help_text="URL to featured image", null=True
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False, help_text="Featured posts appear prominently"
                    ),
                ),
                (
                    "is_published",
                    models.BooleanField(
                        default=True,
                        help_text="Only published posts are visible to users",
                    ),
                ),
                (
                    "allow_comments",
                    models.BooleanField(
                        default=True, help_text="Allow users to comment on this post"
                    ),
                ),
                (
                    "moderation_status",
                    models.CharField(
                        choices=[
                            ("approved", "Approved"),
                            ("pending", "Pending Review"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("moderation_comment", models.TextField(blank=True, null=True)),
                ("moderated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "likes",
                    models.ManyToManyField(
                        blank=True,
                        related_name="liked_posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "moderated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderated_posts",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, related_name="posts", to="api.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Comment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content", models.TextField()),
                (
                    "moderation_status",
                    models.CharField(
                        choices=[
                            ("approved", "Approved"),
                            ("pending", "Pending Review"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("moderation_comment", models.TextField(blank=True, null=True)),
                ("moderated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "moderated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderated_comments",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "post",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comments",
                        to="api.post",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Resource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "resource_type",
                    models.CharField(
                        choices=[
                            ("article", "Article"),
                            ("video", "Video"),
                            ("course", "Course"),
                            ("book", "Book"),
                            ("tool", "Tool"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("url", models.URLField()),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="resource_images/",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="shared_resources",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, related_name="resources", to="api.tag"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="SystemLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "timestamp",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("success", "Success"),
                        ],
                        db_index=True,
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("authentication", "Authentication"),
                            ("database", "Database"),
                            ("api", "API"),
                            ("backup", "Backup"),
                            ("system", "System"),
                            ("user_action", "User Action"),
                            ("security", "Security"),
                            ("performance", "Performance"),
                            ("maintenance", "Maintenance"),
                        ],
                        db_index=True,
                        max_length=50,
                    ),
                ),
                ("message", models.TextField()),
                ("details", models.TextField(blank=True, null=True)),
                ("source", models.CharField(blank=True, max_length=100, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True, null=True)),
                ("request_id", models.CharField(blank=True, max_length=100, null=True)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-timestamp"],
                "indexes": [
                    models.Index(
                        fields=["timestamp", "level"],
                        name="api_systeml_timesta_c32fa9_idx",
                    ),
                    models.Index(
                        fields=["category", "timestamp"],
                        name="api_systeml_categor_784cbc_idx",
                    ),
                    models.Index(
                        fields=["user", "timestamp"],
                        name="api_systeml_user_id_5e46ed_idx",
                    ),
                ],
            },
        ),
    ]
