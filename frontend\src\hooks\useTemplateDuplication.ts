/**
 * 🔄 UNIFIED TEMPLATE DUPLICATION HOOK
 * Consolidates all template duplication logic into a single, reusable hook
 * Replaces duplicate implementations across TemplateSelector, TemplateManagement, and TemplatePreview
 */

import { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { businessPlanTemplatesAPI } from '../services/businessPlanApi';

export interface TemplateDuplicationOptions {
  namePrefix?: string;
  nameSuffix?: string;
  makePrivate?: boolean;
  onSuccess?: (duplicatedTemplate: any) => void;
  onError?: (error: Error) => void;
  debugMode?: boolean;
}

export interface TemplateDuplicationResult {
  duplicateTemplate: (templateId: string, options?: TemplateDuplicationOptions) => Promise<boolean>;
  duplicateTemplateFromObject: (template: any, options?: TemplateDuplicationOptions) => Promise<boolean>;
  isDuplicating: boolean;
  error: string | null;
  clearError: () => void;
}

/**
 * Unified hook for template duplication functionality
 */
export const useTemplateDuplication = (): TemplateDuplicationResult => {
  const { t } = useTranslation();
  const [isDuplicating, setIsDuplicating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Generate a unique name for the duplicated template
   */
  const generateDuplicateName = useCallback((originalName: string, options?: TemplateDuplicationOptions): string => {
    const prefix = options?.namePrefix || '';
    const suffix = options?.nameSuffix || ' (Copy)';
    
    return `${prefix}${originalName}${suffix}`.trim();
  }, []);

  /**
   * Log debug information if debug mode is enabled
   */
  const debugLog = useCallback((message: string, data?: any, options?: TemplateDuplicationOptions) => {
    if (options?.debugMode && import.meta.env.DEV) {
      console.log(`🔄 Template Duplication: ${message}`, data || '');
    }
  }, []);

  /**
   * Duplicate a template by ID
   */
  const duplicateTemplate = useCallback(async (
    templateId: string, 
    options?: TemplateDuplicationOptions
  ): Promise<boolean> => {
    try {
      setIsDuplicating(true);
      setError(null);
      
      debugLog(`Starting duplication for template ID: ${templateId}`, null, options);

      // Get the template details first
      const template = await businessPlanTemplatesAPI.getTemplate(templateId);
      
      if (!template) {
        throw new Error(t('templates.errors.notFound', 'Template not found'));
      }

      debugLog(`Found template: ${template.name}`, template, options);

      // Create the duplicate
      const duplicatedTemplate = await businessPlanTemplatesAPI.createCustomTemplate({
        name: generateDuplicateName(template.name, options),
        description: template.description,
        base_template: template.id,
        sections: template.sections || {},
        is_public: options?.makePrivate === false ? true : false
      });

      debugLog(`Successfully duplicated template`, duplicatedTemplate, options);

      // Call success callback if provided
      options?.onSuccess?.(duplicatedTemplate);

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('templates.errors.duplicationFailed', 'Failed to duplicate template');
      setError(errorMessage);
      
      debugLog(`Duplication failed: ${errorMessage}`, err, options);
      
      // Call error callback if provided
      options?.onError?.(err instanceof Error ? err : new Error(errorMessage));
      
      return false;
    } finally {
      setIsDuplicating(false);
    }
  }, [t, generateDuplicateName, debugLog]);

  /**
   * Duplicate a template from a template object (for cases where you already have the template data)
   */
  const duplicateTemplateFromObject = useCallback(async (
    template: any, 
    options?: TemplateDuplicationOptions
  ): Promise<boolean> => {
    try {
      setIsDuplicating(true);
      setError(null);
      
      debugLog(`Starting duplication from template object: ${template.name}`, template, options);

      // Create the duplicate directly from the template object
      const duplicateData = {
        name: generateDuplicateName(template.name, options),
        description: template.description,
        base_template: 'id' in template ? template.id : 1,
        sections: template.sections || {},
        is_public: options?.makePrivate === false ? true : false
      };

      const duplicatedTemplate = await businessPlanTemplatesAPI.createCustomTemplate(duplicateData);

      debugLog(`Successfully duplicated template from object`, duplicatedTemplate, options);

      // Call success callback if provided
      options?.onSuccess?.(duplicatedTemplate);

      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : t('templates.errors.duplicationFailed', 'Failed to duplicate template');
      setError(errorMessage);
      
      debugLog(`Duplication failed: ${errorMessage}`, err, options);
      
      // Call error callback if provided
      options?.onError?.(err instanceof Error ? err : new Error(errorMessage));
      
      return false;
    } finally {
      setIsDuplicating(false);
    }
  }, [t, generateDuplicateName, debugLog]);

  return {
    duplicateTemplate,
    duplicateTemplateFromObject,
    isDuplicating,
    error,
    clearError
  };
};

/**
 * Convenience hook for simple template duplication with default options
 */
export const useSimpleTemplateDuplication = () => {
  const { duplicateTemplate, duplicateTemplateFromObject, isDuplicating, error } = useTemplateDuplication();
  
  const simpleDuplicate = useCallback(async (templateIdOrObject: string | any): Promise<boolean> => {
    if (typeof templateIdOrObject === 'string') {
      return duplicateTemplate(templateIdOrObject, { debugMode: true });
    } else {
      return duplicateTemplateFromObject(templateIdOrObject, { debugMode: true });
    }
  }, [duplicateTemplate, duplicateTemplateFromObject]);

  return {
    duplicateTemplate: simpleDuplicate,
    isDuplicating,
    error
  };
};

export default useTemplateDuplication;
