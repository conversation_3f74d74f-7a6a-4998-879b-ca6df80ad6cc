# Generated by Django 5.2.1 on 2025-08-05 14:09

import django.core.validators
import django.db.models.deletion
import uuid
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="ExternalServiceIntegration",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=200)),
                (
                    "service_type",
                    models.CharField(
                        choices=[
                            ("payment", "Payment Gateway"),
                            ("email", "Email Service"),
                            ("sms", "SMS Service"),
                            ("video", "Video Conferencing"),
                            ("storage", "Cloud Storage"),
                            ("analytics", "Analytics Service"),
                            ("crm", "CRM System"),
                            ("social", "Social Media"),
                            ("ai", "AI/ML Service"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "provider",
                    models.CharField(
                        help_text="Service provider name (e.g., Stripe, SendGrid)",
                        max_length=100,
                    ),
                ),
                ("base_url", models.URLField(blank=True, max_length=500)),
                ("api_version", models.CharField(blank=True, max_length=50)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("error", "Error"),
                            ("maintenance", "Maintenance"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "config",
                    models.JSONField(
                        default=dict, help_text="Service-specific configuration"
                    ),
                ),
                (
                    "credentials",
                    models.JSONField(
                        default=dict, help_text="Encrypted credentials and API keys"
                    ),
                ),
                (
                    "rate_limits",
                    models.JSONField(
                        default=dict, help_text="Rate limiting configuration"
                    ),
                ),
                ("last_health_check", models.DateTimeField(blank=True, null=True)),
                (
                    "health_check_interval",
                    models.PositiveIntegerField(
                        default=300, help_text="Health check interval in seconds"
                    ),
                ),
                ("uptime_percentage", models.FloatField(default=100.0)),
                (
                    "average_response_time",
                    models.FloatField(
                        default=0.0, help_text="Average response time in milliseconds"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "external_service_integrations",
                "ordering": ["name"],
                "unique_together": {("name", "provider")},
            },
        ),
        migrations.CreateModel(
            name="WebhookEndpoint",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "url",
                    models.URLField(
                        max_length=500,
                        validators=[django.core.validators.URLValidator()],
                    ),
                ),
                ("name", models.CharField(blank=True, max_length=200)),
                ("description", models.TextField(blank=True)),
                (
                    "events",
                    models.JSONField(
                        default=list,
                        help_text="List of event types this endpoint subscribes to",
                    ),
                ),
                (
                    "secret",
                    models.CharField(
                        help_text="Secret key for webhook signature verification",
                        max_length=255,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("last_delivery_at", models.DateTimeField(blank=True, null=True)),
                ("timeout_seconds", models.PositiveIntegerField(default=30)),
                ("max_retries", models.PositiveIntegerField(default=3)),
                (
                    "retry_delays",
                    models.JSONField(
                        default=list, help_text="Retry delay intervals in seconds"
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "webhook_endpoints",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="WebhookDelivery",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("endpoint_url", models.URLField(max_length=500)),
                ("event_type", models.CharField(max_length=100)),
                ("payload", models.JSONField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("delivered", "Delivered"),
                            ("failed", "Failed"),
                            ("retrying", "Retrying"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("status_code", models.PositiveIntegerField(blank=True, null=True)),
                ("response_body", models.TextField(blank=True)),
                ("error_message", models.TextField(blank=True)),
                ("attempt_count", models.PositiveIntegerField(default=1)),
                ("delivered_at", models.DateTimeField(auto_now_add=True)),
                (
                    "duration_ms",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Delivery duration in milliseconds",
                        null=True,
                    ),
                ),
                (
                    "endpoint",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="deliveries",
                        to="integrations.webhookendpoint",
                    ),
                ),
            ],
            options={
                "db_table": "webhook_deliveries",
                "ordering": ["-delivered_at"],
            },
        ),
        migrations.CreateModel(
            name="WebhookRetry",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("event_type", models.CharField(max_length=100)),
                ("payload", models.JSONField()),
                ("attempt", models.PositiveIntegerField()),
                ("retry_at", models.DateTimeField()),
                ("processed", models.BooleanField(default=False)),
                ("processed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "endpoint",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="retries",
                        to="integrations.webhookendpoint",
                    ),
                ),
                (
                    "original_delivery",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="retries",
                        to="integrations.webhookdelivery",
                    ),
                ),
            ],
            options={
                "db_table": "webhook_retries",
                "ordering": ["retry_at"],
            },
        ),
        migrations.CreateModel(
            name="APIRateLimit",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "endpoint",
                    models.CharField(
                        help_text="API endpoint or resource", max_length=200
                    ),
                ),
                (
                    "limit_type",
                    models.CharField(
                        help_text="Type of limit (requests_per_minute, requests_per_hour, etc.)",
                        max_length=50,
                    ),
                ),
                (
                    "limit_value",
                    models.PositiveIntegerField(help_text="Maximum allowed requests"),
                ),
                ("current_usage", models.PositiveIntegerField(default=0)),
                (
                    "reset_at",
                    models.DateTimeField(help_text="When the rate limit resets"),
                ),
                ("last_request_at", models.DateTimeField(blank=True, null=True)),
                ("total_requests", models.PositiveBigIntegerField(default=0)),
                ("blocked_requests", models.PositiveBigIntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="api_rate_limits",
                        to="integrations.externalserviceintegration",
                    ),
                ),
            ],
            options={
                "db_table": "api_rate_limits",
                "ordering": ["-updated_at"],
                "unique_together": {("integration", "endpoint", "limit_type")},
            },
        ),
        migrations.CreateModel(
            name="IntegrationHealthCheck",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("healthy", "Healthy"),
                            ("degraded", "Degraded"),
                            ("unhealthy", "Unhealthy"),
                            ("unknown", "Unknown"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "response_time",
                    models.FloatField(help_text="Response time in milliseconds"),
                ),
                ("status_code", models.PositiveIntegerField(blank=True, null=True)),
                ("error_message", models.TextField(blank=True)),
                ("details", models.JSONField(blank=True, default=dict)),
                ("checked_at", models.DateTimeField(auto_now_add=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="health_checks",
                        to="integrations.externalserviceintegration",
                    ),
                ),
            ],
            options={
                "db_table": "integration_health_checks",
                "ordering": ["-checked_at"],
                "indexes": [
                    models.Index(
                        fields=["integration", "status", "checked_at"],
                        name="integration_integra_6ed5c4_idx",
                    ),
                    models.Index(
                        fields=["status", "checked_at"],
                        name="integration_status_1fdc66_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="IntegrationLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("debug", "Debug"),
                            ("info", "Info"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                            ("critical", "Critical"),
                        ],
                        max_length=10,
                    ),
                ),
                ("message", models.TextField()),
                ("details", models.JSONField(blank=True, default=dict)),
                ("request_method", models.CharField(blank=True, max_length=10)),
                ("request_url", models.URLField(blank=True, max_length=500)),
                ("request_headers", models.JSONField(blank=True, default=dict)),
                ("request_body", models.TextField(blank=True)),
                ("response_status", models.PositiveIntegerField(blank=True, null=True)),
                ("response_headers", models.JSONField(blank=True, default=dict)),
                ("response_body", models.TextField(blank=True)),
                (
                    "response_time",
                    models.FloatField(
                        blank=True, help_text="Response time in milliseconds", null=True
                    ),
                ),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "integration",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="logs",
                        to="integrations.externalserviceintegration",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "integration_logs",
                "ordering": ["-created_at"],
                "indexes": [
                    models.Index(
                        fields=["integration", "level", "created_at"],
                        name="integration_integra_86a7d3_idx",
                    ),
                    models.Index(
                        fields=["level", "created_at"],
                        name="integration_level_5e5cb2_idx",
                    ),
                    models.Index(
                        fields=["created_at"], name="integration_created_b8d84c_idx"
                    ),
                ],
            },
        ),
        migrations.AddIndex(
            model_name="webhookdelivery",
            index=models.Index(
                fields=["status", "delivered_at"], name="webhook_del_status_bf05f5_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="webhookdelivery",
            index=models.Index(
                fields=["event_type", "delivered_at"],
                name="webhook_del_event_t_606d88_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="webhookdelivery",
            index=models.Index(
                fields=["endpoint", "status"], name="webhook_del_endpoin_4ffb63_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="webhookretry",
            index=models.Index(
                fields=["processed", "retry_at"], name="webhook_ret_process_5ba6f6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="webhookretry",
            index=models.Index(
                fields=["endpoint", "processed"], name="webhook_ret_endpoin_628d74_idx"
            ),
        ),
    ]
