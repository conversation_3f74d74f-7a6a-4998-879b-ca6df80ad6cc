/**
 * 🎯 AI Analytics Dashboard
 * Comprehensive monitoring and analytics for AI interactions
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON><PERSON>s, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell, AreaChart, Area
} from 'recharts';
import {
  Activity, Users, MessageSquare, DollarSign, AlertTriangle,
  Download, RefreshCw
} from 'lucide-react';
import { aiAnalyticsService, DashboardStats } from '@/services/aiAnalyticsService';
import { useUnifiedRoles } from '@/hooks/useUnifiedRoles';

// ========================================
// DASHBOARD OVERVIEW COMPONENT
// ========================================

const DashboardOverview: React.FC<{ stats: DashboardStats }> = ({ stats }) => {
  const formatCurrency = (amount: number) => `$${amount.toFixed(4)}`;
  const formatNumber = (num: number) => num.toLocaleString();
  const formatTime = (ms: number) => `${(ms / 1000).toFixed(2)}s`;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Sessions */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الجلسات</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_sessions)}</div>
          <p className="text-xs text-muted-foreground">
            {stats.active_sessions} جلسة نشطة
          </p>
        </CardContent>
      </Card>

      {/* Total Messages */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الرسائل</CardTitle>
          <MessageSquare className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(stats.total_messages)}</div>
          <p className="text-xs text-muted-foreground">
            {formatNumber(stats.unique_users)} مستخدم فريد
          </p>
        </CardContent>
      </Card>

      {/* Token Usage & Cost */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">التكلفة الإجمالية</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(stats.total_cost_usd)}</div>
          <p className="text-xs text-muted-foreground">
            {formatNumber(stats.total_tokens)} tokens
          </p>
        </CardContent>
      </Card>

      {/* Performance */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">الأداء</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatTime(stats.average_response_time)}</div>
          <div className="flex items-center space-x-2">
            <Badge variant={stats.success_rate > 95 ? "default" : "destructive"}>
              {stats.success_rate.toFixed(1)}% نجاح
            </Badge>
            {stats.recent_errors > 0 && (
              <Badge variant="destructive">
                {stats.recent_errors} خطأ
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// REGIONAL BREAKDOWN COMPONENT
// ========================================

const RegionalBreakdown: React.FC<{ stats: DashboardStats }> = ({ stats }) => {
  const regionalData = [
    { name: 'دمشق', value: stats.regional_stats.damascus, color: '#8884d8' },
    { name: 'حلب', value: stats.regional_stats.aleppo, color: '#82ca9d' },
    { name: 'حمص', value: stats.regional_stats.homs, color: '#ffc658' },
    { name: 'اللاذقية', value: stats.regional_stats.latakia, color: '#ff7300' }
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>التوزيع الإقليمي</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={regionalData}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
            >
              {regionalData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// ========================================
// USAGE TRENDS COMPONENT
// ========================================

const UsageTrends: React.FC<{ stats: DashboardStats }> = ({ stats }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>اتجاهات الاستخدام</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <AreaChart data={stats.daily_usage}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area 
              type="monotone" 
              dataKey="sessions" 
              stackId="1" 
              stroke="#8884d8" 
              fill="#8884d8" 
              name="الجلسات"
            />
            <Area 
              type="monotone" 
              dataKey="messages" 
              stackId="2" 
              stroke="#82ca9d" 
              fill="#82ca9d" 
              name="الرسائل"
            />
          </AreaChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// ========================================
// MAIN DASHBOARD COMPONENT
// ========================================

const AIAnalyticsDashboard: React.FC = () => {
  const { canAccess } = useUnifiedRoles();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [timeRange, setTimeRange] = useState(30);

  // Load dashboard data
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await aiAnalyticsService.getDashboardStats(timeRange);
      setStats(data);
    } catch (err) {
      setError('فشل في تحميل بيانات التحليلات');
      console.error('Dashboard loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  // Export data
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      const blob = await aiAnalyticsService.exportData({
        type: 'dashboard',
        format,
        days: timeRange
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `ai-analytics-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  useEffect(() => {
    loadDashboardData();
  }, [timeRange]);

  // Check admin permissions
  if (!canAccess('canAccessSystemSettings')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold">غير مصرح</h3>
          <p className="text-muted-foreground">تحتاج صلاحيات المدير لعرض هذه الصفحة</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل التحليلات...</p>
        </div>
      </div>
    );
  }

  if (error || !stats) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold">خطأ في التحميل</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadDashboardData}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">📊 تحليلات الذكاء الاصطناعي</h1>
          <p className="text-muted-foreground">مراقبة شاملة لأداء ياسمين الذكية</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Time Range Selector */}
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={7}>آخر 7 أيام</option>
            <option value={30}>آخر 30 يوم</option>
            <option value={90}>آخر 3 أشهر</option>
          </select>

          {/* Action Buttons */}
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            تحديث
          </Button>
          
          <Button 
            variant="outline" 
            onClick={() => handleExport('csv')}
          >
            <Download className="h-4 w-4 mr-2" />
            تصدير CSV
          </Button>
        </div>
      </div>

      {/* Dashboard Overview */}
      <DashboardOverview stats={stats} />

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RegionalBreakdown stats={stats} />
        <UsageTrends stats={stats} />
      </div>
    </div>
  );
};

export default AIAnalyticsDashboard;
