/**
 * 🎯 ADMIN DASHBOARD PAGE
 * Modern admin dashboard using login page styling and real API data only
 * No mock data - 100% API integration
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  BarChart3, Users, TrendingUp, Activity, RefreshCw,
  AlertTriangle, CheckCircle, Clock, Server
} from 'lucide-react';
import { RTLIcon, RTLText, RTLComponent } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import {
  ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid,
  Tooltip, PieChart, Pie, Cell, BarChart, Bar, Legend
} from 'recharts';
import {
  EnhancedButton, EnhancedCard, designSystem
} from '../../components/ui/DesignSystem';
import { useAdminDashboard } from '../../hooks/useAdminData';
import { formatNumber, formatPercentage, getStatusInfo } from '../../utils/adminUtils';

// Types for API responses
interface DashboardStats {
  users: {
    total: number;
    active: number;
    newToday: number;
    growth: number;
  };
  approvals: {
    pending: number;
    approved: number;
    rejected: number;
    dailyCount: number;
  };
  system: {
    uptime: number;
    responseTime: number;
    errorRate: number;
    activeConnections: number;
  };
}

interface ChartData {
  registrations: any[];
  approvals: any[];
  userTypes: any[];
}

interface RecentActivity {
  id: string;
  type: string;
  user: string;
  action: string;
  timestamp: string;
  status: string;
}

const AdminDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { isRTL, language } = useLanguage();
  const { t } = useTranslation();

  // ✅ CONSOLIDATED: Use unified admin dashboard hook instead of duplicate state
  const {
    data: adminData,
    loading,
    error,
    refreshing,
    refresh
  } = useAdminDashboard();

  // Local state for UI-specific data
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  // ✅ CONSOLIDATED: Extract data from unified hook
  const dashboardStats = adminData?.dashboardStats;
  const systemHealth = adminData?.systemHealth;
  const activityLogs = adminData?.activityLogs || [];

  // ✅ CONSOLIDATED: Process data using utility functions
  const stats = dashboardStats ? {
    users: {
      total: dashboardStats.users?.total || 0,
      active: dashboardStats.users?.active || 0,
      newToday: dashboardStats.users?.new_today || 0,
      growth: dashboardStats.users?.growth_rate || 0
    },
    approvals: {
      pending: dashboardStats.approvals?.pending || 0,
      approved: dashboardStats.approvals?.approved_today || 0,
      rejected: dashboardStats.approvals?.rejected_today || 0,
      dailyCount: (dashboardStats.approvals?.approved_today || 0) + (dashboardStats.approvals?.rejected_today || 0)
    },
    system: {
      uptime: systemHealth?.uptime || 0,
      responseTime: systemHealth?.response_time || 0,
      errorRate: systemHealth?.error_rate || 0,
      activeConnections: systemHealth?.active_connections || 0
    }
  } : null;

  // ✅ CONSOLIDATED: Process chart data
  const chartData = dashboardStats ? {
    registrations: Array.isArray(dashboardStats.activity?.last_7_days) ? dashboardStats.activity.last_7_days : [],
    approvals: Array.isArray(dashboardStats.activity?.last_7_days) ? dashboardStats.activity.last_7_days : [],
    userTypes: Array.isArray(dashboardStats.activity?.user_types) ? dashboardStats.activity.user_types : []
  } : null;

  // ✅ CONSOLIDATED: Loading state from unified hook
  if (loading) {
    return (
      <div className={`min-h-screen ${designSystem.backgrounds.main} flex items-center justify-center`}>
        <EnhancedCard className="p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <RTLText className="text-white">{t('admin.dashboard.loading', 'Loading dashboard...')}</RTLText>
          </div>
        </EnhancedCard>
      </div>
    );
  }

  // ✅ CONSOLIDATED: Error state from unified hook
  if (error) {
    return (
      <div className={`min-h-screen ${designSystem.backgrounds.main} flex items-center justify-center`}>
        <EnhancedCard className="p-8 max-w-md w-full">
          <div className="text-center">
            <RTLIcon icon={AlertTriangle} size={48} className="text-red-400 mx-auto mb-4" />
            <RTLText className="text-red-400 text-lg mb-4">{error}</RTLText>
            <EnhancedButton onClick={refresh} variant="primary">
              <RTLText>{t('admin.dashboard.tryAgain', 'Try Again')}</RTLText>
            </EnhancedButton>
          </div>
        </EnhancedCard>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${designSystem.backgrounds.main}`}>
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <EnhancedCard className="mb-6">
          <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mr-4">
                <RTLIcon icon={BarChart3} size={24} className="text-purple-400" />
              </div>
              <div>
                <RTLText as="h1" className="text-3xl font-bold text-white">
                  {t('admin.dashboard.title', 'Admin Dashboard')}
                </RTLText>
                <RTLText className="text-gray-300">
                  {t('admin.dashboard.subtitle', 'System overview and management')}
                </RTLText>
              </div>
            </div>

            <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {/* Time Range Selector */}
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}
                className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              >
                <option value="7d">{t('admin.dashboard.last7days', 'Last 7 days')}</option>
                <option value="30d">{t('admin.dashboard.last30days', 'Last 30 days')}</option>
                <option value="90d">{t('admin.dashboard.last90days', 'Last 90 days')}</option>
              </select>

              {/* Refresh Button */}
              <EnhancedButton
                onClick={refresh}
                disabled={refreshing}
                variant="primary"
              >
                <RTLIcon
                  icon={RefreshCw}
                  size={16}
                  className={refreshing ? 'animate-spin' : ''}
                />
                <RTLText>{refreshing ? t('admin.dashboard.refreshing', 'Refreshing...') : t('admin.dashboard.refresh', 'Refresh')}</RTLText>
              </EnhancedButton>
            </div>
          </div>
        </EnhancedCard>

        {stats && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* Total Users */}
              <EnhancedCard>
                <div className="flex items-center justify-between mb-4">
                  <RTLIcon icon={Users} size={24} className="text-blue-400" />
                  <span className={`text-xs px-2 py-1 rounded-full ${stats.users.growth > 0 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                    {stats.users.growth > 0 ? '+' : ''}{stats.users.growth}%
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {formatNumber(stats.users.total)}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.dashboard.totalUsers', 'Total Users')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {formatNumber(stats.users.newToday)} {t('admin.dashboard.newToday', 'new today')}
                </RTLText>
              </EnhancedCard>

              {/* Active Users */}
              <EnhancedCard>
                <div className="flex items-center justify-between mb-4">
                  <RTLIcon icon={TrendingUp} size={24} className="text-green-400" />
                  <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400">
                    {t('admin.dashboard.active', 'Active')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {formatNumber(stats.users.active)}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.dashboard.activeUsers', 'Active Users')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {formatPercentage((stats.users.active / stats.users.total) * 100)} {t('admin.dashboard.ofTotal', 'of total')}
                </RTLText>
              </EnhancedCard>

              {/* Pending Approvals */}
              <EnhancedCard>
                <div className="flex items-center justify-between mb-4">
                  <RTLIcon icon={Clock} size={24} className="text-yellow-400" />
                  <span className="text-xs px-2 py-1 rounded-full bg-yellow-500/20 text-yellow-400">
                    {t('admin.dashboard.pending', 'Pending')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {stats.approvals.pending}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.dashboard.pendingApprovals', 'Pending Approvals')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {stats.approvals.dailyCount} {t('admin.dashboard.processedToday', 'processed today')}
                </RTLText>
              </EnhancedCard>

              {/* System Health */}
              <EnhancedCard>
                <div className="flex items-center justify-between mb-4">
                  <RTLIcon icon={Server} size={24} className="text-purple-400" />
                  <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400">
                    {t('admin.dashboard.healthy', 'Healthy')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {stats.system.uptime}%
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.dashboard.systemUptime', 'System Uptime')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {stats.system.responseTime}ms {t('admin.dashboard.avgResponse', 'avg response')}
                </RTLText>
              </EnhancedCard>
            </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* User Registrations Chart */}
            <EnhancedCard>
              <RTLText className="text-xl font-bold text-white mb-4">{t('admin.dashboard.userRegistrations', 'User Registrations')}</RTLText>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData.registrations}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="date" stroke="#9CA3AF" />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1F2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#F3F4F6'
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="count"
                        stroke="#8B5CF6"
                        fill="#8B5CF6"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
              </div>
            </EnhancedCard>

            {/* User Types Distribution */}
            <EnhancedCard>
              <RTLText className="text-xl font-bold text-white mb-4">{t('admin.dashboard.userDistribution', 'User Distribution')}</RTLText>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={chartData.userTypes}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {Array.isArray(chartData.userTypes) ? chartData.userTypes.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color || '#8884d8'} />
                        )) : null}
                      </Pie>
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#1F2937',
                          border: '1px solid #374151',
                          borderRadius: '8px',
                          color: '#F3F4F6'
                        }}
                      />
                    </PieChart>
                  </ResponsiveContainer>
              </div>
            </EnhancedCard>
          </div>

          {/* Recent Activity */}
          <EnhancedCard>
            <div className="flex items-center justify-between mb-6">
              <RTLText className="text-xl font-bold text-white">{t('admin.dashboard.recentActivity', 'Recent Activity')}</RTLText>
              <RTLIcon icon={Activity} size={20} className="text-purple-400" />
            </div>

              <div className="space-y-4">
                {Array.isArray(recentActivity) && recentActivity.length > 0 ? recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center justify-between p-4 bg-white/5 rounded-lg border border-white/10">
                    <div className="flex items-center space-x-3">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.status === 'success' ? 'bg-green-400' :
                        activity.status === 'warning' ? 'bg-yellow-400' :
                        activity.status === 'error' ? 'bg-red-400' : 'bg-gray-400'
                      }`}></div>
                      <div>
                        <RTLText className="text-white font-medium">{activity.action}</RTLText>
                        <RTLText className="text-gray-400 text-sm">{activity.user}</RTLText>
                      </div>
                    </div>
                    <RTLText className="text-gray-400 text-sm">
                      {new Date(activity.timestamp).toLocaleTimeString()}
                    </RTLText>
                  </div>
                )) : (
                  <div className="text-center py-8">
                    <RTLText className={designSystem.text.muted}>{t('admin.dashboard.noActivity', 'No recent activity')}</RTLText>
                  </div>
                )}
              </div>
            </EnhancedCard>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminDashboardPage;
