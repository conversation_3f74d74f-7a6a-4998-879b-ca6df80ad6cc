// A/B Testing Types and Interfaces

export interface ABTestVariant {
  id: string;
  name: string;
  weight: number; // Percentage of traffic (0-100)
  config: Record<string, string | number | boolean>; // Variant-specific configuration
}

export interface ABTestExperiment {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'paused' | 'completed';
  startDate: Date;
  endDate?: Date;
  targetMetric: string;
  variants: ABTestVariant[];
  trafficAllocation: number; // Percentage of users to include (0-100)
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ABTestAssignment {
  experimentId: string;
  variantId: string;
  userId: string;
  assignedAt: Date;
  sessionId: string;
}

export interface ABTestEvent {
  id: string;
  experimentId: string;
  variantId: string;
  userId: string;
  sessionId: string;
  eventType: 'view' | 'click' | 'conversion' | 'custom';
  eventName: string;
  properties?: Record<string, string | number | boolean>;
  timestamp: Date;
}

export interface ABTestMetrics {
  experimentId: string;
  variantId: string;
  totalUsers: number;
  totalEvents: number;
  conversions: number;
  conversionRate: number;
  confidence: number;
  statisticalSignificance: boolean;
  uplift?: number; // Compared to control
}

export interface ABTestResults {
  experiment: ABTestExperiment;
  metrics: ABTestMetrics[];
  winner?: string; // Variant ID
  confidence: number;
  duration: number; // Days
  totalParticipants: number;
}

export interface ABTestConfig {
  // Registration flow experiments
  registrationStepDesign?: 'original' | 'modern' | 'minimal';
  registrationButtonText?: string;
  registrationButtonColor?: 'blue' | 'green' | 'purple';
  registrationFormLayout?: 'single-column' | 'two-column' | 'wizard';
  
  // Step 3 experiments
  step3Design?: 'card' | 'inline' | 'modal';
  step3HeaderText?: string;
  step3TrustBadge?: boolean;
  
  // General UI experiments
  primaryColor?: string;
  buttonStyle?: 'rounded' | 'square' | 'pill';
  fontFamily?: 'inter' | 'roboto' | 'poppins';
  
  // Content experiments
  heroText?: string;
  ctaText?: string;
  benefitsOrder?: string[];
  
  // Feature experiments
  socialLogin?: boolean;
  passwordStrengthMeter?: boolean;
  realTimeValidation?: boolean;
}

export interface ABTestHookResult {
  variant: ABTestVariant | null;
  isLoading: boolean;
  trackEvent: (eventName: string, properties?: Record<string, string | number | boolean>) => void;
  trackConversion: (conversionValue?: number) => void;
}

export interface ABTestProviderProps {
  children: React.ReactNode;
  userId?: string;
  sessionId?: string;
}

export interface ABTestContextValue {
  experiments: ABTestExperiment[];
  assignments: Record<string, ABTestAssignment>;
  getVariant: (experimentId: string) => ABTestVariant | null;
  trackEvent: (experimentId: string, eventName: string, properties?: Record<string, string | number | boolean>) => void;
  trackConversion: (experimentId: string, conversionValue?: number) => void;
  isLoading: boolean;
}

// Statistical analysis types
export interface StatisticalTest {
  pValue: number;
  confidence: number;
  isSignificant: boolean;
  sampleSize: number;
  effect: number;
  standardError: number;
}

export interface ABTestStatistics {
  controlMetrics: ABTestMetrics;
  variantMetrics: ABTestMetrics[];
  tests: Record<string, StatisticalTest>;
  recommendations: string[];
  minSampleSize: number;
  estimatedDuration: number; // Days to reach significance
}

// Admin dashboard types
export interface ExperimentFormData {
  name: string;
  description: string;
  targetMetric: string;
  trafficAllocation: number;
  variants: Omit<ABTestVariant, 'id'>[];
  startDate: Date;
  endDate?: Date;
}

export interface AnalyticsDashboardData {
  totalExperiments: number;
  activeExperiments: number;
  completedExperiments: number;
  totalParticipants: number;
  averageConversionRate: number;
  topPerformingVariants: Array<{
    experimentName: string;
    variantName: string;
    conversionRate: number;
    uplift: number;
  }>;
  recentEvents: ABTestEvent[];
}

// Export utility types
export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx';
  dateRange: {
    start: Date;
    end: Date;
  };
  experiments: string[];
  includeRawData: boolean;
}

export interface ExportData {
  experiments: ABTestExperiment[];
  results: ABTestResults[];
  events: ABTestEvent[];
  metadata: {
    exportedAt: Date;
    exportedBy: string;
    totalRecords: number;
  };
}
