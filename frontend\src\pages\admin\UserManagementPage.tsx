/**
 * 👥 UNIFIED USER MANAGEMENT PAGE
 * Comprehensive admin interface for complete user management
 * Includes: User approvals, Role applications, User listing, Bulk operations
 */

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { RTLIcon, RTLText } from '../../components/rtl';
import { EnhancedCard, EnhancedButton, designSystem } from '../../components/ui/DesignSystem';
import { AdminButton } from '../../components/admin/layout/AdminPageLayout';
import { userAPI, adminAPI } from '../../services/api';
import { roleApplicationAPI, type RoleApplication as APIRoleApplication } from '../../services/roleApplicationApi';
import { useUserManagement } from '../../hooks/useAdminData';
import { formatNumber, formatTimestamp, getStatusInfo } from '../../utils/adminUtils';
import {
  Users,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  Mail,
  UserCheck,
  UserX,
  Clock,
  AlertTriangle,
  Edit,
  Trash2,
  Shield,
  DollarSign,
  Settings,
  MoreHorizontal,
  Plus,
  Save,
  X,
  MapPin,
  Phone,
  Globe,
  Linkedin,
  Building,
  Target,
  Award,
  MessageSquare,
  FileText,
  ExternalLink
} from 'lucide-react';

interface UserApproval {
  id: string;
  user: {
    id: string;
    username: string;
    email: string;
    first_name: string;
    last_name: string;
    date_joined: string;
    is_active: boolean;
    is_staff: boolean;
  };
  status: 'pending' | 'approved' | 'rejected';
  reviewed_by?: {
    username: string;
    first_name: string;
    last_name: string;
  };
  reviewed_at?: string;
  rejection_reason?: string;
  admin_notes?: string;
  created_at: string;
}

interface RoleApplication extends APIRoleApplication {
  priority?: 'high' | 'medium' | 'low';
}

interface User {
  id: string;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  date_joined: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  last_login?: string;
  roles?: string[];
}

type TabType = 'approvals' | 'applications' | 'users' | 'bulk';

const UserManagementPage: React.FC = () => {
  const { t } = useTranslation();
  const { language, isRTL } = useLanguage();
  const navigate = useNavigate();
  
  // Tab management
  const [activeTab, setActiveTab] = useState<TabType>('approvals');
  
  // State management for approvals
  const [approvals, setApprovals] = useState<UserApproval[]>([]);
  const [filteredApprovals, setFilteredApprovals] = useState<UserApproval[]>([]);
  
  // State management for role applications
  const [applications, setApplications] = useState<RoleApplication[]>([]);
  const [filteredApplications, setFilteredApplications] = useState<RoleApplication[]>([]);
  
  // State management for users
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  
  // Common state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('pending');
  const [roleFilter, setRoleFilter] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Add User Modal State
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [newUserData, setNewUserData] = useState({
    username: '',
    email: '',
    first_name: '',
    last_name: '',
    password: '',
    confirm_password: '',
    is_active: true,
    role: 'user'
  });

  // Detail View Modal State
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedItemForDetail, setSelectedItemForDetail] = useState<any>(null);
  const [detailLoading, setDetailLoading] = useState(false);

  // Load data on mount and tab change
  useEffect(() => {
    loadData();
  }, [activeTab]);

  // Load all data initially for statistics
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setLoading(true);
        // Load all data types for proper statistics
        await Promise.all([
          loadApprovals(),
          loadApplications(),
          loadUsers()
        ]);
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load initial data');
      } finally {
        setLoading(false);
      }
    };

    loadInitialData();
  }, []); // Only run once on mount

  // Apply filters when data or filters change
  useEffect(() => {
    applyFilters();
  }, [approvals, applications, users, statusFilter, roleFilter, searchQuery, activeTab]);

  // Unified data loading function
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      switch (activeTab) {
        case 'approvals':
          await loadApprovals();
          break;
        case 'applications':
          await loadApplications();
          break;
        case 'users':
          await loadUsers();
          break;
        case 'bulk':
          // Load all data for bulk operations
          await Promise.all([loadApprovals(), loadApplications(), loadUsers()]);
          break;
      }
    } catch (err) {
      console.error('Error loading data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // Load user approvals from API
  const loadApprovals = async () => {
    try {
      // Use the correct API endpoint for user approvals
      const approvalsResponse: any = await userAPI.getUserApprovals();
      console.log('🔍 User approvals response:', approvalsResponse);

      // Debug: Log first approval item structure
      if (Array.isArray(approvalsResponse) && approvalsResponse.length > 0) {
        console.log('📋 First approval item structure:', approvalsResponse[0]);
        console.log('📋 Available fields:', Object.keys(approvalsResponse[0]));
      }

      // Handle the response structure properly
      let approvalsData: any[] = [];
      if (Array.isArray(approvalsResponse)) {
        approvalsData = approvalsResponse;
      } else if (approvalsResponse?.results) {
        approvalsData = approvalsResponse.results;
      } else if (approvalsResponse?.data) {
        approvalsData = approvalsResponse.data;
      }

      // Transform data to match expected structure using backend enhanced fields
      const transformedApprovals = approvalsData.map(approval => {
        // Use the enhanced fields provided by UserApprovalSerializer
        const userInfo = {
          id: approval.user, // This is the user ID from backend
          username: approval.user_username || 'Unknown User',
          email: approval.user_email || 'No email',
          first_name: approval.user_full_name ? approval.user_full_name.split(' ')[0] : '',
          last_name: approval.user_full_name ? approval.user_full_name.split(' ').slice(1).join(' ') : '',
          full_name: approval.user_full_name || 'Unknown User'
        };

        return {
          id: approval.id,
          user: userInfo,
          status: approval.status || 'pending',
          created_at: approval.created_at || approval.submitted_at,
          reviewed_at: approval.reviewed_at,
          reviewed_by: approval.reviewed_by,
          reason: approval.rejection_reason || '',
          priority: 'medium' as const,
          // Include additional fields from backend
          days_pending: approval.days_pending,
          requested_role_info: approval.requested_role_info,
          profile_summary: approval.profile_summary
        };
      });

      setApprovals(transformedApprovals);
    } catch (err) {
      console.error('Error loading user approvals:', err);
      throw err;
    }
  };

  // Load role applications from API
  const loadApplications = async () => {
    try {
      const applicationsResponse: any = await roleApplicationAPI.getAllApplications();
      console.log('Role applications response:', applicationsResponse);

      let applicationsData: any[] = [];
      if (Array.isArray(applicationsResponse)) {
        applicationsData = applicationsResponse;
      } else if (applicationsResponse?.results) {
        applicationsData = applicationsResponse.results;
      } else if (applicationsResponse?.data) {
        applicationsData = applicationsResponse.data;
      }

      // Transform data to match expected structure using backend enhanced fields
      const transformedApplications: RoleApplication[] = applicationsData.map(app => {
        // Use the enhanced fields provided by RoleApplicationSerializer
        const userInfo = {
          id: app.user_id || app.user, // Use user_id if available, fallback to user
          username: app.user_username || 'Unknown User',
          email: app.user_email || 'No email',
          first_name: app.user_full_name ? app.user_full_name.split(' ')[0] : '',
          last_name: app.user_full_name ? app.user_full_name.split(' ').slice(1).join(' ') : '',
          full_name: app.user_full_name || 'Unknown User'
        };

        return {
          id: app.id,
          user: userInfo,
          requested_role: app.requested_role || {
            id: app.role_id,
            name: app.role_name || 'user',
            display_name: app.role_display_name || 'User',
            description: app.role_description || ''
          },
          motivation: app.motivation || '',
          qualifications: app.qualifications || '',
          experience: app.experience || '',
          portfolio_url: app.portfolio_url || '',
          status: app.status || 'pending',
          reviewed_by: app.reviewed_by,
          reviewed_at: app.reviewed_at,
          admin_notes: app.admin_notes || '',
          created_at: app.created_at || app.submitted_at,
          priority: 'medium' as const,
          // Include additional fields from backend
          days_pending: app.days_pending,
          // Role-specific fields
          company_name: app.company_name,
          project_stage: app.project_stage,
          industry: app.industry,
          project_description: app.project_description,
          funding_needed: app.funding_needed,
          team_size: app.team_size,
          support_needed: app.support_needed,
          previous_experience: app.previous_experience,
          expertise_areas: app.expertise_areas,
          mentoring_experience: app.mentoring_experience,
          availability: app.availability,
          preferred_communication: app.preferred_communication,
          investment_range: app.investment_range,
          investment_focus: app.investment_focus,
          investment_stage: app.investment_stage,
          portfolio_companies: app.portfolio_companies,
          due_diligence_requirements: app.due_diligence_requirements,
          interests: app.interests,
          goals: app.goals
        };
      });

      setApplications(transformedApplications);
    } catch (err) {
      console.error('Error loading role applications:', err);
      throw err;
    }
  };

  // Load all users from API
  const loadUsers = async () => {
    try {
      const usersResponse: any = await adminAPI.getUsers();
      console.log('Users response:', usersResponse);

      let usersData: any[] = [];
      if (Array.isArray(usersResponse)) {
        usersData = usersResponse;
      } else if (usersResponse?.results) {
        usersData = usersResponse.results;
      } else if (usersResponse?.data) {
        usersData = usersResponse.data;
      }

      // Transform data to match expected User interface
      const transformedUsers = usersData.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        full_name: user.full_name || `${user.first_name || ''} ${user.last_name || ''}`.trim(),
        is_active: user.is_active ?? true,
        is_staff: user.is_staff ?? false,
        is_superuser: user.is_superuser ?? false,
        date_joined: user.date_joined,
        last_login: user.last_login,
        user_role: user.user_role || user.role || 'user',
        role_assignments: user.role_assignments || [],
        profile: user.profile || {
          id: user.profile_id,
          bio: user.bio || '',
          location: user.location || '',
          phone_number: user.phone_number || '',
          is_active: user.profile_is_active ?? true,
          active_roles: user.active_roles || [],
          highest_permission_level: user.highest_permission_level || 'read'
        }
      }));

      setUsers(transformedUsers);
    } catch (err) {
      console.error('Error loading users:', err);
      throw err;
    }
  };

  // Apply filters based on active tab
  const applyFilters = () => {
    switch (activeTab) {
      case 'approvals':
        applyApprovalsFilters();
        break;
      case 'applications':
        applyApplicationsFilters();
        break;
      case 'users':
        applyUsersFilters();
        break;
      case 'bulk':
        // Apply filters to all data types
        applyApprovalsFilters();
        applyApplicationsFilters();
        applyUsersFilters();
        break;
    }
  };

  // Apply filters to approvals
  const applyApprovalsFilters = () => {
    let filtered = [...approvals];

    if (statusFilter) {
      filtered = filtered.filter(approval => approval.status === statusFilter);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(approval =>
        approval.user.username.toLowerCase().includes(query) ||
        approval.user.email.toLowerCase().includes(query) ||
        approval.user.first_name.toLowerCase().includes(query) ||
        approval.user.last_name.toLowerCase().includes(query)
      );
    }

    setFilteredApprovals(filtered);
  };

  // Apply filters to role applications
  const applyApplicationsFilters = () => {
    let filtered = [...applications];

    if (statusFilter) {
      filtered = filtered.filter(app => app.status === statusFilter);
    }

    if (roleFilter) {
      filtered = filtered.filter(app => app.requested_role.name === roleFilter);
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(app =>
        app.user.username.toLowerCase().includes(query) ||
        app.user.email.toLowerCase().includes(query) ||
        app.user.first_name.toLowerCase().includes(query) ||
        app.user.last_name.toLowerCase().includes(query) ||
        app.requested_role.display_name.toLowerCase().includes(query)
      );
    }

    setFilteredApplications(filtered);
  };

  // Apply filters to users
  const applyUsersFilters = () => {
    let filtered = [...users];

    if (statusFilter) {
      if (statusFilter === 'active') {
        filtered = filtered.filter(user => user.is_active);
      } else if (statusFilter === 'inactive') {
        filtered = filtered.filter(user => !user.is_active);
      } else if (statusFilter === 'staff') {
        filtered = filtered.filter(user => user.is_staff);
      }
    }

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(user =>
        user.username.toLowerCase().includes(query) ||
        user.email.toLowerCase().includes(query) ||
        user.first_name.toLowerCase().includes(query) ||
        user.last_name.toLowerCase().includes(query)
      );
    }

    setFilteredUsers(filtered);
  };

  // Handle bulk operations based on active tab
  const handleBulkApprove = async () => {
    if (selectedItems.length === 0) return;

    try {
      setActionLoading('approve');

      if (activeTab === 'approvals') {
        // Use correct user approval API
        const approvalPromises = selectedItems.map(id => userAPI.approveUser(id));
        await Promise.all(approvalPromises);
        await loadApprovals();
      } else if (activeTab === 'applications') {
        // Use correct role application API
        const applicationPromises = selectedItems.map(id => roleApplicationAPI.approveApplication(id));
        await Promise.all(applicationPromises);
        await loadApplications();
      }

      setSelectedItems([]);
      console.log('✅ Bulk approve completed');
    } catch (err) {
      console.error('❌ Error bulk approving:', err);
      setError(err instanceof Error ? err.message : 'Failed to approve items');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBulkReject = async () => {
    if (selectedItems.length === 0) return;

    // Get rejection reason from user
    const reason = prompt(t('admin.enterRejectionReason', 'Enter rejection reason:')) || 'Bulk rejection';

    try {
      setActionLoading('reject');

      if (activeTab === 'approvals') {
        // Use correct user approval API
        const rejectionPromises = selectedItems.map(id => userAPI.rejectUser(id, reason));
        await Promise.all(rejectionPromises);
        await loadApprovals();
      } else if (activeTab === 'applications') {
        // Use correct role application API
        const rejectionPromises = selectedItems.map(id => roleApplicationAPI.rejectApplication(id, reason));
        await Promise.all(rejectionPromises);
        await loadApplications();
      }

      setSelectedItems([]);
      console.log('✅ Bulk reject completed');
    } catch (err) {
      console.error('❌ Error bulk rejecting:', err);
      setError(err instanceof Error ? err.message : 'Failed to reject items');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0 || activeTab !== 'users') return;

    // Confirm bulk deletion
    if (!window.confirm(t('admin.confirmBulkDelete', `Are you sure you want to delete ${selectedItems.length} users? This action cannot be undone.`))) {
      return;
    }

    try {
      setActionLoading('delete');

      // Use correct admin API for user deletion
      const deletePromises = selectedItems.map(id => adminAPI.deleteUser(parseInt(id)));
      await Promise.all(deletePromises);
      await loadUsers();
      setSelectedItems([]);
      console.log('✅ Bulk delete completed');
    } catch (err) {
      console.error('❌ Error bulk deleting:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete users');
    } finally {
      setActionLoading(null);
    }
  };

  // Add new user
  const handleAddUser = async () => {
    try {
      // Validate form
      if (!newUserData.username || !newUserData.email || !newUserData.first_name || !newUserData.last_name) {
        setError('Please fill in all required fields');
        return;
      }

      if (!newUserData.password || newUserData.password.length < 6) {
        setError('Password must be at least 6 characters long');
        return;
      }

      if (newUserData.password !== newUserData.confirm_password) {
        setError('Passwords do not match');
        return;
      }

      setActionLoading('add-user');

      // Create user via admin API with proper data structure
      const userData = {
        username: newUserData.username,
        email: newUserData.email,
        first_name: newUserData.first_name,
        last_name: newUserData.last_name,
        password: newUserData.password,
        password_confirm: newUserData.confirm_password,  // Backend expects this
        is_active: newUserData.is_active,
        selected_role: newUserData.role,  // Backend expects 'selected_role' for role assignment
      };

      console.log('🚀 Creating user with data:', userData);
      const newUser = await adminAPI.createUser(userData);
      console.log('✅ User created successfully:', newUser);

      // Note: Backend should handle role assignment automatically based on selected_role

      // Reset form and close modal
      setNewUserData({
        username: '',
        email: '',
        first_name: '',
        last_name: '',
        password: '',
        confirm_password: '',
        is_active: true,
        role: 'user'
      });
      setShowAddUserModal(false);

      // Reload data
      await loadData();

    } catch (err) {
      console.error('Error adding user:', err);
      setError(err instanceof Error ? err.message : 'Failed to add user');
    } finally {
      setActionLoading(null);
    }
  };

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-500/20 text-green-400';
      case 'rejected': return 'bg-red-500/20 text-red-400';
      case 'pending': return 'bg-yellow-500/20 text-yellow-400';
      case 'active': return 'bg-green-500/20 text-green-400';
      case 'inactive': return 'bg-gray-500/20 text-gray-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'active': return CheckCircle;
      case 'rejected':
      case 'inactive': return XCircle;
      case 'pending': return Clock;
      default: return AlertTriangle;
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'mentor': return Users;
      case 'investor': return DollarSign;
      case 'moderator':
      case 'admin': return Shield;
      case 'entrepreneur': return Users;
      default: return Users;
    }
  };

  // Helper function to handle API errors consistently
  const handleApiError = (error: any, operation: string) => {
    console.error(`❌ Error ${operation}:`, error);

    let errorMessage = `Failed to ${operation}`;
    if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    } else if (error?.response?.data?.error) {
      errorMessage = error.response.data.error;
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }

    setError(errorMessage);
    return errorMessage;
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString(isRTL ? 'ar-SA' : 'en-US');
  };

  // Open detail view for an item
  const openDetailView = async (item: any, type: 'approval' | 'application' | 'user') => {
    try {
      console.log('🔍 Opening detail view for:', type, item);

      // Set initial state immediately to show modal
      setDetailLoading(true);
      setSelectedItemForDetail({ ...item, type });
      setShowDetailModal(true);

      // Prevent any navigation during this process
      const currentPath = window.location.pathname;

      // If it's a user approval or application, try to get more detailed data
      if (type === 'approval' && item.id) {
        try {
          const detailedData = await userAPI.getUserApproval(item.id);
          console.log('📋 Detailed approval data:', detailedData);

          // Only update if we're still on the same page and modal is still open
          if (window.location.pathname === currentPath && showDetailModal) {
            setSelectedItemForDetail({ ...detailedData, type });
          }
        } catch (err) {
          console.warn('Could not load detailed approval data:', err);
          // Keep the modal open with basic data even if detailed fetch fails
        }
      } else if (type === 'application' && item.id) {
        try {
          const detailedData = await roleApplicationAPI.getApplication(item.id);
          console.log('📋 Detailed application data:', detailedData);

          // Only update if we're still on the same page and modal is still open
          if (window.location.pathname === currentPath && showDetailModal) {
            setSelectedItemForDetail({ ...detailedData, type });
          }
        } catch (err) {
          console.warn('Could not load detailed application data:', err);
          // Keep the modal open with basic data even if detailed fetch fails
        }
      }
    } catch (err) {
      console.error('Error opening detail view:', err);
      // Don't set error that might cause redirect, just log it
      console.warn('Detail view error handled gracefully');
    } finally {
      setDetailLoading(false);
    }
  };

  const getTabTitle = (tab: TabType): string => {
    switch (tab) {
      case 'approvals': return String(t('admin.userApprovals.title', 'User Approvals'));
      case 'applications': return String(t('admin.roleApplications', 'Role Applications'));
      case 'users': return String(t('admin.allUsers', 'All Users'));
      case 'bulk': return String(t('admin.bulkOperations', 'Bulk Operations'));
      default: return '';
    }
  };

  const getTabTitleAr = (tab: TabType) => {
    switch (tab) {
      case 'approvals': return 'موافقات المستخدمين';
      case 'applications': return 'طلبات الأدوار';
      case 'users': return 'جميع المستخدمين';
      case 'bulk': return 'العمليات المجمعة';
      default: return '';
    }
  };

  const getCurrentData = () => {
    switch (activeTab) {
      case 'approvals': return filteredApprovals;
      case 'applications': return filteredApplications;
      case 'users': return filteredUsers;
      case 'bulk': return [...filteredApprovals, ...filteredApplications, ...filteredUsers];
      default: return [];
    }
  };

  const getStatsData = () => {
    return {
      totalUsers: users.length,
      pendingApprovals: approvals.filter(a => a.status === 'pending').length,
      pendingApplications: applications.filter(a => a.status === 'pending').length,
      activeUsers: users.filter(u => u.is_active).length,
    };
  };

  const stats = getStatsData();

  return (
    <div className={`min-h-screen ${designSystem.backgrounds.main}`}>
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <EnhancedCard className="mb-6">
          <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mr-4">
                <RTLIcon icon={Users} size={24} className="text-purple-400" />
              </div>
              <div>
                <RTLText as="h1" className="text-3xl font-bold text-white">
                  {isRTL ? 'إدارة المستخدمين' : t('admin.userManagement', 'User Management')}
                </RTLText>
                <RTLText className="text-gray-300">
                  {isRTL ? 'إدارة شاملة للمستخدمين والموافقات وطلبات الأدوار' : t('admin.comprehensiveUserManagement', 'Comprehensive user management, approvals, and role applications')}
                </RTLText>
              </div>
            </div>

            <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {/* Add User Button */}
              <EnhancedButton
                variant="primary"
                onClick={() => setShowAddUserModal(true)}
              >
                <RTLIcon icon={Plus} size={16} />
                <RTLText>{t('admin.addUser', 'Add User')}</RTLText>
              </EnhancedButton>

              {selectedItems.length > 0 && (
                <>
                  {(activeTab === 'approvals' || activeTab === 'applications') && (
                    <>
                      <EnhancedButton
                        variant="primary"
                        onClick={handleBulkApprove}
                        disabled={actionLoading === 'approve'}
                      >
                        <RTLIcon icon={UserCheck} size={16} />
                        <RTLText>{t('admin.approveSelected', 'Approve Selected')} ({selectedItems.length})</RTLText>
                      </EnhancedButton>
                      <EnhancedButton
                        variant="danger"
                        onClick={handleBulkReject}
                        disabled={actionLoading === 'reject'}
                      >
                        <RTLIcon icon={UserX} size={16} />
                        <RTLText>{t('admin.rejectSelected', 'Reject Selected')} ({selectedItems.length})</RTLText>
                      </EnhancedButton>
                    </>
                  )}
                  {activeTab === 'users' && (
                    <EnhancedButton
                      variant="danger"
                      onClick={handleBulkDelete}
                      disabled={actionLoading === 'delete'}
                    >
                      <RTLIcon icon={Trash2} size={16} />
                      <RTLText>{t('admin.deleteSelected', 'Delete Selected')} ({selectedItems.length})</RTLText>
                    </EnhancedButton>
                  )}
                </>
              )}
            </div>
          </div>
        </EnhancedCard>
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
          <EnhancedCard>
            <div className="flex items-center justify-between mb-4">
              <RTLIcon icon={Users} size={24} className="text-blue-400" />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">{stats.totalUsers}</div>
              <RTLText className="text-gray-300 text-sm">{isRTL ? 'إجمالي المستخدمين' : t('admin.totalUsers', 'Total Users')}</RTLText>
              <RTLText className="text-gray-400 text-xs mt-2">{t('admin.registeredUsers', 'Registered Users')}</RTLText>
            </div>
          </EnhancedCard>

          <EnhancedCard>
            <div className="flex items-center justify-between mb-4">
              <RTLIcon icon={Clock} size={24} className="text-yellow-400" />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400 mb-2">{stats.pendingApprovals}</div>
              <RTLText className="text-gray-300 text-sm">{isRTL ? 'الموافقات المعلقة' : t('admin.pendingApprovals', 'Pending Approvals')}</RTLText>
              <RTLText className="text-gray-400 text-xs mt-2">{t('admin.awaitingApproval', 'Awaiting Approval')}</RTLText>
            </div>
          </EnhancedCard>

          <EnhancedCard>
            <div className="flex items-center justify-between mb-4">
              <RTLIcon icon={Shield} size={24} className="text-purple-400" />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">{stats.pendingApplications}</div>
              <RTLText className="text-gray-300 text-sm">{isRTL ? 'طلبات الأدوار' : t('admin.roleApplications', 'Role Applications')}</RTLText>
              <RTLText className="text-gray-400 text-xs mt-2">{t('admin.pendingRoles', 'Pending Roles')}</RTLText>
            </div>
          </EnhancedCard>

          <EnhancedCard>
            <div className="flex items-center justify-between mb-4">
              <RTLIcon icon={UserCheck} size={24} className="text-green-400" />
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{stats.activeUsers}</div>
              <RTLText className="text-gray-300 text-sm">{isRTL ? 'المستخدمون النشطون' : t('admin.activeUsers', 'Active Users')}</RTLText>
              <RTLText className="text-gray-400 text-xs mt-2">{t('admin.currentlyActive', 'Currently Active')}</RTLText>
            </div>
          </EnhancedCard>
        </div>

        {/* Tab Navigation */}
        <EnhancedCard className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <RTLText className="text-xl font-bold text-white">
              {isRTL ? 'علامات التبويب الإدارية' : t('admin.managementTabs', 'Management Tabs')}
            </RTLText>
          </div>
        <div className={`flex flex-wrap gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {(['approvals', 'applications', 'users', 'bulk'] as TabType[]).map((tab) => (
            <button
              key={tab}
              onClick={() => {
                setActiveTab(tab);
                setSelectedItems([]);
                setStatusFilter('');
                setRoleFilter('');
                setSearchQuery('');
              }}
              className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                activeTab === tab
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-glow'
                  : 'bg-white/20 text-gray-300 hover:bg-white/30 border border-white/30'
              }`}
            >
              <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <RTLIcon
                  icon={tab === 'approvals' ? UserCheck :
                        tab === 'applications' ? Users :
                        tab === 'users' ? Settings :
                        MoreHorizontal}
                  size={16}
                  className={isRTL ? 'ml-2' : 'mr-2'}
                />
                <RTLText>{isRTL ? getTabTitleAr(tab) : getTabTitle(tab)}</RTLText>
              </div>
            </button>
          ))}
        </div>
        </EnhancedCard>

        {/* Filters and Search */}
        <EnhancedCard className="mb-6">
          <div className="flex items-center justify-between mb-6">
            <RTLText className="text-xl font-bold text-white">
              {isRTL ? 'المرشحات والبحث' : t('admin.filtersAndSearch', 'Filters & Search')}
            </RTLText>
          </div>
          <div className={`grid grid-cols-1 md:grid-cols-4 gap-4 ${isRTL ? 'md:grid-flow-col-dense' : ''}`}>
            {/* Search Input */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {isRTL ? 'البحث' : t('admin.search', 'Search')}
              </label>
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={
                  activeTab === 'approvals' ? (isRTL ? 'البحث في الموافقات...' : t('admin.searchApprovals', 'Search approvals...')) :
                  activeTab === 'applications' ? (isRTL ? 'البحث في الطلبات...' : t('admin.searchApplications', 'Search applications...')) :
                  activeTab === 'users' ? (isRTL ? 'البحث في المستخدمين...' : t('admin.searchUsers', 'Search users...')) :
                  (isRTL ? 'البحث في الكل...' : t('admin.searchAll', 'Search all...'))
                }
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400"
                dir={isRTL ? 'rtl' : 'ltr'}
              />
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {isRTL ? 'الحالة' : t('admin.status', 'Status')}
              </label>
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                dir={isRTL ? 'rtl' : 'ltr'}
              >
              <option value="">{t('admin.allStatuses', 'All Statuses')}</option>
              {activeTab === 'users' ? (
                <>
                  <option value="active">{t('admin.active', 'Active')}</option>
                  <option value="inactive">{t('admin.inactive', 'Inactive')}</option>
                  <option value="staff">{t('admin.staff', 'Staff')}</option>
                </>
              ) : (
                <>
                  <option value="pending">{t('admin.pending', 'Pending')}</option>
                  <option value="approved">{t('admin.approved', 'Approved')}</option>
                  <option value="rejected">{t('admin.rejected', 'Rejected')}</option>
                </>
              )}
            </select>
          </div>

          {/* Role Filter (for applications tab) */}
          {activeTab === 'applications' && (
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-1">
                {t('admin.role', 'Role')}
              </label>
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value)}
                className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                dir={isRTL ? 'rtl' : 'ltr'}
              >
                <option value="">{t('admin.allRoles', 'All Roles')}</option>
                <option value="mentor">{t('roles.mentor', 'Mentor')}</option>
                <option value="investor">{t('roles.investor', 'Investor')}</option>
                <option value="entrepreneur">{t('roles.entrepreneur', 'Entrepreneur')}</option>
                <option value="moderator">{t('roles.moderator', 'Moderator')}</option>
              </select>
            </div>
          )}

          {/* Quick Stats */}
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{getCurrentData().length}</div>
              <RTLText className="text-gray-300 text-sm">
                {activeTab === 'approvals' ? t('admin.totalApprovals', 'Total Approvals') :
                 activeTab === 'applications' ? t('admin.totalApplications', 'Total Applications') :
                 activeTab === 'users' ? t('admin.totalUsers', 'Total Users') :
                 t('admin.totalItems', 'Total Items')
                }
              </RTLText>
            </div>
          </div>
          </div>
        </EnhancedCard>

        {/* Dynamic Content Based on Active Tab */}
        <EnhancedCard>
          <div className="flex items-center justify-between mb-6">
            <RTLText className="text-xl font-bold text-white">
              {isRTL ? getTabTitleAr(activeTab) : getTabTitle(activeTab)}
            </RTLText>
            <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              {/* Add User button for Users tab */}
              {activeTab === 'users' && (
                <EnhancedButton
                  variant="primary"
                  onClick={() => setShowAddUserModal(true)}
                >
                  <RTLIcon icon={Plus} size={16} />
                  <RTLText>{t('admin.addUser', 'Add User')}</RTLText>
                </EnhancedButton>
              )}

              {getCurrentData().length > 0 && (
                <button
                  onClick={() => {
                    const allIds = getCurrentData().map((item: any) => item.id);
                    setSelectedItems(
                      selectedItems.length === allIds.length ? [] : allIds
                    );
                  }}
                  className="text-sm text-purple-400 hover:text-purple-300 transition-colors"
                >
                  <RTLText>
                    {selectedItems.length === getCurrentData().length
                      ? t('admin.deselectAll', 'Deselect All')
                      : t('admin.selectAll', 'Select All')
                    }
                  </RTLText>
                </button>
              )}
            </div>
          </div>
        {/* Special content for Bulk Operations tab */}
        {activeTab === 'bulk' ? (
          <div className="space-y-8">
            {/* Quick Actions */}
            <div className="text-center py-8">
              <RTLIcon icon={MoreHorizontal} size={48} className="text-gray-500 mx-auto mb-4" />
              <RTLText className="text-gray-400 text-lg mb-6">
                {t('admin.bulkOperations', 'Bulk Operations & Quick Actions')}
              </RTLText>

              <div className={`flex flex-wrap gap-4 justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AdminButton
                  variant="primary"
                  onClick={() => setShowAddUserModal(true)}
                  icon={Plus}
                >
                  {t('admin.addUser', 'Add User')}
                </AdminButton>

                <AdminButton
                  variant="secondary"
                  onClick={() => setActiveTab('users')}
                  icon={Users}
                >
                  {t('admin.manageUsers', 'Manage Users')} ({users.length})
                </AdminButton>

                <AdminButton
                  variant="secondary"
                  onClick={() => setActiveTab('approvals')}
                  icon={UserCheck}
                >
                  {t('admin.reviewApprovals', 'Review Approvals')} ({approvals.filter(a => a.status === 'pending').length})
                </AdminButton>

                <AdminButton
                  variant="secondary"
                  onClick={() => setActiveTab('applications')}
                  icon={Shield}
                >
                  {t('admin.reviewApplications', 'Review Applications')} ({applications.filter(a => a.status === 'pending').length})
                </AdminButton>
              </div>

              <RTLText className="text-gray-500 text-sm mt-6">
                {t('admin.bulkOperationsDesc', 'Use the tabs above to select items for bulk operations')}
              </RTLText>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center">
                <div className="text-2xl font-bold text-yellow-400 mb-2">{approvals.filter(a => a.status === 'pending').length}</div>
                <RTLText className="text-gray-300 text-sm">{t('admin.pendingApprovals', 'Pending Approvals')}</RTLText>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center">
                <div className="text-2xl font-bold text-purple-400 mb-2">{applications.filter(a => a.status === 'pending').length}</div>
                <RTLText className="text-gray-300 text-sm">{t('admin.pendingApplications', 'Pending Applications')}</RTLText>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 border border-white/20 text-center">
                <div className="text-2xl font-bold text-green-400 mb-2">{users.filter(u => u.is_active).length}</div>
                <RTLText className="text-gray-300 text-sm">{t('admin.activeUsers', 'Active Users')}</RTLText>
              </div>
            </div>
          </div>
        ) : getCurrentData().length === 0 ? (
          <div className="text-center py-12">
            <RTLIcon icon={Users} size={48} className="text-gray-500 mx-auto mb-4" />
            <RTLText className="text-gray-400 text-lg">
              {activeTab === 'approvals' ? t('admin.noApprovals', 'No user approvals found') :
               activeTab === 'applications' ? t('admin.noApplications', 'No role applications found') :
               activeTab === 'users' ? t('admin.noUsers', 'No users found') :
               t('admin.noData', 'No data found')
              }
            </RTLText>
            <RTLText className="text-gray-500 text-sm mt-2">
              {t('admin.tryAdjustingFilters', 'Try adjusting your filters or check back later')}
            </RTLText>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Simple unified item display */}
            {getCurrentData().map((item: any) => (
              <div
                key={item.id}
                className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20 hover:bg-white/20 transition-all duration-300"
              >
                <div className={`flex items-start justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-start ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <input
                      type="checkbox"
                      checked={selectedItems.includes(item.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedItems([...selectedItems, item.id]);
                        } else {
                          setSelectedItems(selectedItems.filter(id => id !== item.id));
                        }
                      }}
                      className={`mt-1 ${isRTL ? 'ml-4' : 'mr-4'} w-4 h-4 text-purple-600 bg-white/20 border-white/30 rounded focus:ring-purple-500`}
                    />

                    <div className={`w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center ${isRTL ? 'ml-4' : 'mr-4'}`}>
                      <span className="text-sm font-bold text-white">
                        {(() => {
                          // Get full name from different possible sources
                          const fullName = item.user_full_name ||
                                         (item.user ? `${item.user.first_name || ''} ${item.user.last_name || ''}`.trim() : '') ||
                                         `${item.first_name || ''} ${item.last_name || ''}`.trim() ||
                                         item.user_username || item.user?.username || item.username || 'U';

                          // Get initials from full name
                          return fullName.split(' ').map((n: string) => n.charAt(0)).join('').toUpperCase() || 'U';
                        })()}
                      </span>
                    </div>

                    <div className="flex-1">
                      <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <RTLText className="font-semibold text-white text-lg">
                          {(() => {
                            // Get display name from different possible sources
                            const displayName = item.user_full_name ||
                                              (item.user ? `${item.user.first_name || ''} ${item.user.last_name || ''}`.trim() : '') ||
                                              `${item.first_name || ''} ${item.last_name || ''}`.trim() ||
                                              item.user_username || item.user?.username || item.username || 'Unknown User';

                            return displayName;
                          })()}
                        </RTLText>

                        <span className={`px-2 py-1 rounded-full text-xs font-semibold ${getStatusColor(item.status || (item.is_active ? 'active' : 'inactive'))} ${isRTL ? 'mr-2' : 'ml-2'}`}>
                          {item.requested_role ? item.requested_role.display_name :
                           item.status ? (isRTL
                            ? (item.status === 'pending' ? 'معلق' :
                               item.status === 'approved' ? 'موافق عليه' : 'مرفوض')
                            : item.status) :
                           item.is_active ? (isRTL ? 'نشط' : 'Active') : (isRTL ? 'غير نشط' : 'Inactive')
                          }
                        </span>
                      </div>

                      <div className={`flex items-center mt-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <RTLIcon icon={Mail} size={14} className={`text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <RTLText className="text-gray-300 text-sm">
                          {item.user_email || item.user?.email || item.email || 'No email'}
                        </RTLText>
                      </div>

                      <div className={`flex items-center mt-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                        <RTLIcon icon={Calendar} size={14} className={`text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <RTLText className="text-gray-300 text-sm">
                          {item.created_at ?
                            `${isRTL ? 'تقدم في:' : 'Applied:'} ${new Date(item.created_at).toLocaleDateString()}` :
                            `${isRTL ? 'انضم في:' : 'Joined:'} ${new Date(item.date_joined).toLocaleDateString()}`
                          }
                        </RTLText>
                      </div>
                    </div>
                  </div>

                  <div className={`flex items-center space-x-2 ${isRTL ? 'space-x-reverse flex-row-reverse' : ''}`}>
                    <AdminButton
                      variant="secondary"
                      onClick={() => openDetailView(item, activeTab === 'approvals' ? 'approval' : activeTab === 'applications' ? 'application' : 'user')}
                      loading={detailLoading}
                      icon={Eye}
                    >
                      {t('admin.viewDetails', 'View Details')}
                    </AdminButton>

                    {/* Action buttons based on item type and status */}
                    {(item.status === 'pending' || (activeTab === 'users' && !item.status)) && (
                      <>
                        {activeTab !== 'users' && (
                          <>
                            <AdminButton
                              variant="primary"
                              onClick={async () => {
                                try {
                                  setActionLoading(`approve-${item.id}`);
                                  if (activeTab === 'approvals') {
                                    await userAPI.approveUser(item.id);
                                  } else if (activeTab === 'applications') {
                                    await roleApplicationAPI.approveApplication(item.id);
                                  }
                                  await loadData();
                                } catch (err) {
                                  setError(err instanceof Error ? err.message : 'Failed to approve');
                                } finally {
                                  setActionLoading(null);
                                }
                              }}
                              loading={actionLoading === `approve-${item.id}`}
                              icon={CheckCircle}
                            >
                              {t('admin.approve', 'Approve')}
                            </AdminButton>

                            <AdminButton
                              variant="danger"
                              onClick={async () => {
                                try {
                                  setActionLoading(`reject-${item.id}`);
                                  if (activeTab === 'approvals') {
                                    await userAPI.rejectUser(item.id, 'Quick rejection');
                                  } else if (activeTab === 'applications') {
                                    await roleApplicationAPI.rejectApplication(item.id, 'Quick rejection');
                                  }
                                  await loadData();
                                } catch (err) {
                                  setError(err instanceof Error ? err.message : 'Failed to reject');
                                } finally {
                                  setActionLoading(null);
                                }
                              }}
                              loading={actionLoading === `reject-${item.id}`}
                              icon={XCircle}
                            >
                              {t('admin.reject', 'Reject')}
                            </AdminButton>
                          </>
                        )}

                        {activeTab === 'users' && (
                          <>
                            <AdminButton
                              variant="secondary"
                              onClick={() => navigate(`/admin/users/${item.id}/edit`)}
                              icon={Edit}
                            >
                              {t('admin.edit', 'Edit')}
                            </AdminButton>

                            <AdminButton
                              variant="danger"
                              onClick={async () => {
                                if (window.confirm(t('admin.confirmDeleteUser', 'Are you sure you want to delete this user?'))) {
                                  try {
                                    setActionLoading(`delete-${item.id}`);
                                    await adminAPI.deleteUser(parseInt(item.id));
                                    await loadData();
                                  } catch (err) {
                                    setError(err instanceof Error ? err.message : 'Failed to delete user');
                                  } finally {
                                    setActionLoading(null);
                                  }
                                }
                              }}
                              loading={actionLoading === `delete-${item.id}`}
                              icon={Trash2}
                            >
                              {t('admin.userManagement.delete', 'Delete')}
                            </AdminButton>
                          </>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
        </EnhancedCard>

      {/* Add User Modal */}
      {showAddUserModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm rounded-xl border border-white/20 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className={`flex items-center justify-between p-6 border-b border-white/20 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {t('admin.addNewUser', 'Add New User')}
                </h2>
                <p className="text-gray-400 mt-1">
                  {t('admin.addUserDesc', 'Create a new user account')}
                </p>
              </div>
              <button
                onClick={() => setShowAddUserModal(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <RTLIcon icon={X} size={24} className="text-gray-400" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.basicInformation', 'Basic Information')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <AdminInput
                    label={t('admin.username', 'Username')}
                    labelAr="اسم المستخدم"
                    value={newUserData.username}
                    onChange={(value) => setNewUserData({...newUserData, username: value})}
                    placeholder={t('admin.enterUsername', 'Enter username')}
                    placeholderAr="أدخل اسم المستخدم"
                    required
                  />

                  <AdminInput
                    label={t('admin.email', 'Email')}
                    labelAr="البريد الإلكتروني"
                    value={newUserData.email}
                    onChange={(value) => setNewUserData({...newUserData, email: value})}
                    placeholder={t('admin.enterEmail', 'Enter email')}
                    placeholderAr="أدخل البريد الإلكتروني"
                    type="email"
                    required
                  />

                  <AdminInput
                    label={t('admin.firstName', 'First Name')}
                    labelAr="الاسم الأول"
                    value={newUserData.first_name}
                    onChange={(value) => setNewUserData({...newUserData, first_name: value})}
                    placeholder={t('admin.enterFirstName', 'Enter first name')}
                    placeholderAr="أدخل الاسم الأول"
                    required
                  />

                  <AdminInput
                    label={t('admin.lastName', 'Last Name')}
                    labelAr="الاسم الأخير"
                    value={newUserData.last_name}
                    onChange={(value) => setNewUserData({...newUserData, last_name: value})}
                    placeholder={t('admin.enterLastName', 'Enter last name')}
                    placeholderAr="أدخل الاسم الأخير"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.password', 'Password')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <AdminInput
                    label={t('admin.password', 'Password')}
                    labelAr="كلمة المرور"
                    value={newUserData.password}
                    onChange={(value) => setNewUserData({...newUserData, password: value})}
                    placeholder={t('admin.enterPassword', 'Enter password')}
                    placeholderAr="أدخل كلمة المرور"
                    type="password"
                    required
                  />

                  <AdminInput
                    label={t('admin.confirmPassword', 'Confirm Password')}
                    labelAr="تأكيد كلمة المرور"
                    value={newUserData.confirm_password}
                    onChange={(value) => setNewUserData({...newUserData, confirm_password: value})}
                    placeholder={t('admin.confirmPassword', 'Confirm password')}
                    placeholderAr="تأكيد كلمة المرور"
                    type="password"
                    required
                  />
                </div>
              </div>

              {/* User Settings */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4">
                  {t('admin.userSettings', 'User Settings')}
                </h3>
                <div className="space-y-4">
                  {/* Active Status */}
                  <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <input
                      type="checkbox"
                      id="is_active"
                      checked={newUserData.is_active}
                      onChange={(e) => setNewUserData({...newUserData, is_active: e.target.checked})}
                      className={`w-4 h-4 text-purple-600 bg-white/20 border-white/30 rounded focus:ring-purple-500 ${isRTL ? 'ml-3' : 'mr-3'}`}
                    />
                    <label htmlFor="is_active" className="text-white">
                      {t('admin.activeUser', 'Active User')}
                    </label>
                  </div>

                  {/* Role Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      {t('admin.role', 'Role')}
                    </label>
                    <select
                      value={newUserData.role}
                      onChange={(e) => setNewUserData({...newUserData, role: e.target.value})}
                      className="w-full px-4 py-2 bg-white/20 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white"
                      dir={isRTL ? 'rtl' : 'ltr'}
                    >
                      <option value="user">{t('roles.user', 'User')}</option>
                      <option value="entrepreneur">{t('roles.entrepreneur', 'Entrepreneur')}</option>
                      <option value="mentor">{t('roles.mentor', 'Mentor')}</option>
                      <option value="investor">{t('roles.investor', 'Investor')}</option>
                      <option value="moderator">{t('roles.moderator', 'Moderator')}</option>
                      <option value="admin">{t('roles.admin', 'Admin')}</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className={`flex items-center justify-end gap-3 p-6 border-t border-white/20 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <AdminButton
                variant="secondary"
                onClick={() => setShowAddUserModal(false)}
                icon={X}
              >
                {t('admin.cancel', 'Cancel')}
              </AdminButton>

              <AdminButton
                variant="primary"
                onClick={handleAddUser}
                loading={actionLoading === 'add-user'}
                icon={Save}
              >
                {t('admin.createUser', 'Create User')}
              </AdminButton>
            </div>
          </div>
        </div>
      )}

      {/* Detail View Modal */}
      {showDetailModal && selectedItemForDetail && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-900/95 backdrop-blur-sm rounded-xl border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className={`flex items-center justify-between p-6 border-b border-white/20 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div>
                <h2 className="text-2xl font-bold text-white">
                  {selectedItemForDetail.type === 'approval' ? t('admin.userApprovalDetails', 'User Approval Details') :
                   selectedItemForDetail.type === 'application' ? t('admin.roleApplicationDetails', 'Role Application Details') :
                   t('admin.userDetails', 'User Details')
                  }
                </h2>
                <p className="text-gray-400 mt-1">
                  {selectedItemForDetail.user?.username ||
                   selectedItemForDetail.user_username ||
                   selectedItemForDetail.username ||
                   'Unknown User'}
                </p>
              </div>
              <button
                onClick={() => setShowDetailModal(false)}
                className="p-2 hover:bg-white/10 rounded-lg transition-colors"
              >
                <RTLIcon icon={X} size={24} className="text-gray-400" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-6">
              {/* Basic User Information */}
              <div>
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                  <RTLIcon icon={Users} size={20} className={`text-purple-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                  {t('admin.basicInformation', 'Basic Information')}
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="bg-white/10 rounded-lg p-4">
                    <label className="text-sm text-gray-400">{t('admin.fullName', 'Full Name')}</label>
                    <p className="text-white font-medium">
                      {selectedItemForDetail.user_full_name ||
                       (selectedItemForDetail.user ?
                         `${selectedItemForDetail.user.first_name} ${selectedItemForDetail.user.last_name}` :
                         `${selectedItemForDetail.first_name || ''} ${selectedItemForDetail.last_name || ''}`.trim()) ||
                       'N/A'
                      }
                    </p>
                  </div>

                  <div className="bg-white/10 rounded-lg p-4">
                    <label className="text-sm text-gray-400">{t('admin.username', 'Username')}</label>
                    <p className="text-white font-medium">
                      {selectedItemForDetail.user_username ||
                       selectedItemForDetail.user?.username ||
                       selectedItemForDetail.username ||
                       'N/A'}
                    </p>
                  </div>

                  <div className="bg-white/10 rounded-lg p-4">
                    <label className="text-sm text-gray-400">{t('admin.email', 'Email')}</label>
                    <p className="text-white font-medium flex items-center">
                      <RTLIcon icon={Mail} size={16} className={`text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                      {selectedItemForDetail.user_email ||
                       selectedItemForDetail.user?.email ||
                       selectedItemForDetail.email ||
                       'N/A'}
                    </p>
                  </div>

                  <div className="bg-white/10 rounded-lg p-4">
                    <label className="text-sm text-gray-400">{t('admin.status', 'Status')}</label>
                    <div className="flex items-center mt-1">
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(selectedItemForDetail.status || 'active')}`}>
                        {selectedItemForDetail.status || 'Active'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Profile Information */}
              {selectedItemForDetail.profile_summary && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <RTLIcon icon={Users} size={20} className={`text-green-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('admin.profileInformation', 'Profile Information')}
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {selectedItemForDetail.profile_summary.location && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={MapPin} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.location', 'Location')}
                        </label>
                        <p className="text-white mt-1">{selectedItemForDetail.profile_summary.location}</p>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.company && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Building} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.company', 'Company')}
                        </label>
                        <p className="text-white mt-1">{selectedItemForDetail.profile_summary.company}</p>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.job_title && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.jobTitle', 'Job Title')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.profile_summary.job_title}</p>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.industry && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.industry', 'Industry')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.profile_summary.industry}</p>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.phone_number && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Phone} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.phoneNumber', 'Phone Number')}
                        </label>
                        <p className="text-white mt-1">{selectedItemForDetail.profile_summary.phone_number}</p>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.website && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Globe} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.website', 'Website')}
                        </label>
                        <a
                          href={selectedItemForDetail.profile_summary.website}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 mt-1 flex items-center"
                        >
                          {selectedItemForDetail.profile_summary.website}
                          <RTLIcon icon={ExternalLink} size={14} className={`${isRTL ? 'mr-1' : 'ml-1'}`} />
                        </a>
                      </div>
                    )}

                    {selectedItemForDetail.profile_summary.linkedin_url && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Linkedin} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.linkedinUrl', 'LinkedIn')}
                        </label>
                        <a
                          href={selectedItemForDetail.profile_summary.linkedin_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 mt-1 flex items-center"
                        >
                          {selectedItemForDetail.profile_summary.linkedin_url}
                          <RTLIcon icon={ExternalLink} size={14} className={`${isRTL ? 'mr-1' : 'ml-1'}`} />
                        </a>
                      </div>
                    )}
                  </div>

                  {selectedItemForDetail.profile_summary.bio && (
                    <div className="bg-white/10 rounded-lg p-4 mb-4">
                      <label className="text-sm text-gray-400">{t('admin.bio', 'Bio')}</label>
                      <p className="text-white mt-1">{selectedItemForDetail.profile_summary.bio}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Registration/Application Data */}
              {(selectedItemForDetail.type === 'approval' || selectedItemForDetail.type === 'application') && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <RTLIcon icon={FileText} size={20} className={`text-blue-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {selectedItemForDetail.type === 'approval' ?
                      t('admin.registrationData', 'Registration Data') :
                      t('admin.applicationData', 'Application Data')
                    }
                  </h3>

                  {/* Role Information */}
                  {(selectedItemForDetail.requested_role || selectedItemForDetail.requested_role_info) && (
                    <div className="bg-white/10 rounded-lg p-4 mb-4">
                      <label className="text-sm text-gray-400">{t('admin.requestedRole', 'Requested Role')}</label>
                      <div className="flex items-center mt-1">
                        <RTLIcon icon={getRoleIcon(
                          selectedItemForDetail.requested_role?.name ||
                          selectedItemForDetail.requested_role_info?.role_name ||
                          'user'
                        )} size={16} className={`text-purple-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        <span className="text-white font-medium">
                          {selectedItemForDetail.requested_role?.display_name ||
                           selectedItemForDetail.requested_role?.name ||
                           selectedItemForDetail.requested_role_info?.role_display_name ||
                           selectedItemForDetail.requested_role_info?.role_name ||
                           'User'}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Motivation & Qualifications */}
                  {(selectedItemForDetail.motivation || selectedItemForDetail.requested_role_info?.motivation) && (
                    <div className="bg-white/10 rounded-lg p-4 mb-4">
                      <label className="text-sm text-gray-400">{t('admin.motivation', 'Motivation')}</label>
                      <p className="text-white mt-1">
                        {selectedItemForDetail.motivation || selectedItemForDetail.requested_role_info?.motivation}
                      </p>
                    </div>
                  )}

                  {(selectedItemForDetail.qualifications || selectedItemForDetail.requested_role_info?.qualifications) && (
                    <div className="bg-white/10 rounded-lg p-4 mb-4">
                      <label className="text-sm text-gray-400">{t('admin.qualifications', 'Qualifications')}</label>
                      <p className="text-white mt-1">
                        {selectedItemForDetail.qualifications || selectedItemForDetail.requested_role_info?.qualifications}
                      </p>
                    </div>
                  )}

                  {(selectedItemForDetail.experience || selectedItemForDetail.requested_role_info?.experience) && (
                    <div className="bg-white/10 rounded-lg p-4 mb-4">
                      <label className="text-sm text-gray-400">{t('admin.experience', 'Experience')}</label>
                      <p className="text-white mt-1">
                        {selectedItemForDetail.experience || selectedItemForDetail.requested_role_info?.experience}
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Role-Specific Data */}
              {(selectedItemForDetail.type === 'application' ||
                (selectedItemForDetail.type === 'approval' && selectedItemForDetail.role_specific_data)) && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <RTLIcon icon={Award} size={20} className={`text-green-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('admin.roleSpecificData', 'Role-Specific Information')}
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Entrepreneur Fields */}
                    {selectedItemForDetail.company_name && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Building} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.companyName', 'Company Name')}
                        </label>
                        <p className="text-white mt-1">{selectedItemForDetail.company_name}</p>
                      </div>
                    )}

                    {selectedItemForDetail.industry && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.industry', 'Industry')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.industry}</p>
                      </div>
                    )}

                    {selectedItemForDetail.funding_needed && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={DollarSign} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.fundingNeeded', 'Funding Needed')}
                        </label>
                        <p className="text-white mt-1">{selectedItemForDetail.funding_needed}</p>
                      </div>
                    )}

                    {selectedItemForDetail.team_size && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.teamSize', 'Team Size')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.team_size}</p>
                      </div>
                    )}

                    {/* Mentor Fields */}
                    {(selectedItemForDetail.expertise_areas || selectedItemForDetail.role_specific_data?.expertise) && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.expertiseAreas', 'Expertise Areas')}</label>
                        <p className="text-white mt-1">
                          {selectedItemForDetail.expertise_areas || selectedItemForDetail.role_specific_data?.expertise}
                        </p>
                      </div>
                    )}

                    {(selectedItemForDetail.availability || selectedItemForDetail.role_specific_data?.availability) && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Clock} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.availability', 'Availability')}
                        </label>
                        <p className="text-white mt-1">
                          {selectedItemForDetail.availability || selectedItemForDetail.role_specific_data?.availability}
                        </p>
                      </div>
                    )}

                    {/* Investor Fields */}
                    {selectedItemForDetail.investment_range && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.investmentRange', 'Investment Range')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.investment_range}</p>
                      </div>
                    )}

                    {selectedItemForDetail.investment_focus && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.investmentFocus', 'Investment Focus')}</label>
                        <p className="text-white mt-1">{selectedItemForDetail.investment_focus}</p>
                      </div>
                    )}

                    {/* Contact Information */}
                    {selectedItemForDetail.portfolio_url && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400 flex items-center">
                          <RTLIcon icon={Globe} size={14} className={`${isRTL ? 'ml-1' : 'mr-1'}`} />
                          {t('admin.portfolioUrl', 'Portfolio URL')}
                        </label>
                        <a
                          href={selectedItemForDetail.portfolio_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-400 hover:text-blue-300 mt-1 flex items-center"
                        >
                          {selectedItemForDetail.portfolio_url}
                          <RTLIcon icon={ExternalLink} size={14} className={`${isRTL ? 'mr-1' : 'ml-1'}`} />
                        </a>
                      </div>
                    )}
                  </div>

                  {/* Long text fields */}
                  {selectedItemForDetail.project_description && (
                    <div className="bg-white/10 rounded-lg p-4 mt-4">
                      <label className="text-sm text-gray-400">{t('admin.projectDescription', 'Project Description')}</label>
                      <p className="text-white mt-1">{selectedItemForDetail.project_description}</p>
                    </div>
                  )}

                  {selectedItemForDetail.support_needed && (
                    <div className="bg-white/10 rounded-lg p-4 mt-4">
                      <label className="text-sm text-gray-400">{t('admin.supportNeeded', 'Support Needed')}</label>
                      <p className="text-white mt-1">{selectedItemForDetail.support_needed}</p>
                    </div>
                  )}
                </div>
              )}

              {/* Admin Notes & Review Information */}
              {(selectedItemForDetail.type === 'approval' || selectedItemForDetail.type === 'application') && (
                <div>
                  <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                    <RTLIcon icon={MessageSquare} size={20} className={`text-yellow-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                    {t('admin.reviewInformation', 'Review Information')}
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="bg-white/10 rounded-lg p-4">
                      <label className="text-sm text-gray-400">{t('admin.submittedAt', 'Submitted At')}</label>
                      <p className="text-white mt-1 flex items-center">
                        <RTLIcon icon={Calendar} size={16} className={`text-gray-400 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {formatDate(selectedItemForDetail.created_at)}
                      </p>
                    </div>

                    {selectedItemForDetail.reviewed_by && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.reviewedBy', 'Reviewed By')}</label>
                        <p className="text-white mt-1">
                          {selectedItemForDetail.reviewed_by.first_name} {selectedItemForDetail.reviewed_by.last_name}
                        </p>
                      </div>
                    )}

                    {selectedItemForDetail.reviewed_at && (
                      <div className="bg-white/10 rounded-lg p-4">
                        <label className="text-sm text-gray-400">{t('admin.reviewedAt', 'Reviewed At')}</label>
                        <p className="text-white mt-1">{formatDate(selectedItemForDetail.reviewed_at)}</p>
                      </div>
                    )}
                  </div>

                  {selectedItemForDetail.admin_notes && (
                    <div className="bg-white/10 rounded-lg p-4 mt-4">
                      <label className="text-sm text-gray-400">{t('admin.adminNotes', 'Admin Notes')}</label>
                      <p className="text-white mt-1">{selectedItemForDetail.admin_notes}</p>
                    </div>
                  )}

                  {selectedItemForDetail.rejection_reason && (
                    <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-4 mt-4">
                      <label className="text-sm text-red-300">{t('admin.rejectionReason', 'Rejection Reason')}</label>
                      <p className="text-red-100 mt-1">{selectedItemForDetail.rejection_reason}</p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Modal Footer with Actions */}
            <div className={`flex items-center justify-between gap-3 p-6 border-t border-white/20 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <AdminButton
                  variant="secondary"
                  onClick={() => setShowDetailModal(false)}
                  icon={X}
                >
                  {t('admin.close', 'Close')}
                </AdminButton>

                {/* Redirect Buttons */}
                <AdminButton
                  variant="outline"
                  onClick={() => {
                    const userId = selectedItemForDetail.user?.id || selectedItemForDetail.id;
                    setShowDetailModal(false);
                    navigate(`/admin/users/${userId}/edit`);
                  }}
                  icon={Edit}
                >
                  {t('admin.editUser', 'Edit User')}
                </AdminButton>

                <AdminButton
                  variant="outline"
                  onClick={() => {
                    const userId = selectedItemForDetail.user?.id || selectedItemForDetail.id;
                    setShowDetailModal(false);
                    navigate(`/profile/${userId}`);
                  }}
                  icon={Users}
                >
                  {t('admin.viewProfile', 'View Profile')}
                </AdminButton>

                {selectedItemForDetail.type === 'application' && (
                  <AdminButton
                    variant="outline"
                    onClick={() => {
                      setShowDetailModal(false);
                      navigate('/admin/role-applications');
                    }}
                    icon={Award}
                  >
                    {t('admin.manageApplications', 'Manage Applications')}
                  </AdminButton>
                )}
              </div>

              {/* Action buttons for pending items */}
              {selectedItemForDetail.status === 'pending' && selectedItemForDetail.type !== 'user' && (
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <AdminButton
                    variant="primary"
                    onClick={async () => {
                      try {
                        setActionLoading('approve');
                        if (selectedItemForDetail.type === 'approval') {
                          await userAPI.approveUser(selectedItemForDetail.id);
                        } else if (selectedItemForDetail.type === 'application') {
                          await roleApplicationAPI.approveApplication(selectedItemForDetail.id);
                        }
                        setShowDetailModal(false);
                        await loadData();
                      } catch (err) {
                        setError(err instanceof Error ? err.message : 'Failed to approve');
                      } finally {
                        setActionLoading(null);
                      }
                    }}
                    loading={actionLoading === 'approve'}
                    icon={CheckCircle}
                  >
                    {t('admin.approve', 'Approve')}
                  </AdminButton>

                  <AdminButton
                    variant="danger"
                    onClick={async () => {
                      const reason = prompt(t('admin.enterRejectionReason', 'Enter rejection reason:'));
                      if (!reason) return;

                      try {
                        setActionLoading('reject');
                        if (selectedItemForDetail.type === 'approval') {
                          await userAPI.rejectUser(selectedItemForDetail.id, reason);
                        } else if (selectedItemForDetail.type === 'application') {
                          await roleApplicationAPI.rejectApplication(selectedItemForDetail.id, reason);
                        }
                        setShowDetailModal(false);
                        await loadData();
                      } catch (err) {
                        setError(err instanceof Error ? err.message : 'Failed to reject');
                      } finally {
                        setActionLoading(null);
                      }
                    }}
                    loading={actionLoading === 'reject'}
                    icon={XCircle}
                  >
                    {t('admin.reject', 'Reject')}
                  </AdminButton>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      </div>
    </div>
  );
};

export default UserManagementPage;
