"""
Protected API root views for admin and super admin endpoints
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.reverse import reverse
from rest_framework import status


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def admin_api_root(request, format=None):
    """
    Protected admin API root - requires authentication and admin permissions
    """
    # Check if user has admin permissions
    if not hasattr(request.user, 'profile'):
        return Response(
            {'error': 'User profile not found'},
            status=status.HTTP_403_FORBIDDEN
        )
    
    # Check admin permissions using the same logic as AdminPermissionMixin
    from users.models import UserRoleAssignment
    
    try:
        admin_assignments = UserRoleAssignment.objects.filter(
            user_profile=request.user.profile,
            role__name='admin',  # ✅ CONSOLIDATED: Single admin role
            is_active=True,
            is_approved=True
        )
        
        if not admin_assignments.exists() and not request.user.is_staff:
            return Response(
                {'error': 'Admin permissions required'},
                status=status.HTTP_403_FORBIDDEN
            )
    except Exception as e:
        return Response(
            {'error': 'Permission check failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    return Response({
        'approvals': reverse('admin-approvals-list', request=request, format=format),
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def super_admin_api_root(request, format=None):
    """
    Legacy super admin API root - now redirects to admin (consolidated role system)
    """
    # Redirect to admin API root since super_admin is now consolidated into admin
    return admin_api_root(request, format)
    
    # ✅ CONSOLIDATED: Check admin permissions (single admin system)
    from users.models import UserRoleAssignment

    try:
        admin_assignments = UserRoleAssignment.objects.filter(
            user_profile=request.user.profile,
            role__name='admin',
            is_active=True,
            is_approved=True
        )

        if not admin_assignments.exists() and not (request.user.is_superuser or request.user.is_staff):
            return Response(
                {'error': 'Admin permissions required'},
                status=status.HTTP_403_FORBIDDEN
            )
    except Exception as e:
        return Response(
            {'error': 'Permission check failed'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
    
    return Response({
        'approvals': reverse('admin-approvals-list', request=request, format=format),  # ✅ CONSOLIDATED: Admin endpoints
        'users': reverse('admin-users-list', request=request, format=format),  # ✅ CONSOLIDATED: Admin endpoints
    })
