/**
 * 🎨 ADMIN PAGE LAYOUT
 * Reusable layout component with consistent styling for all admin pages
 * Uses the same beautiful design as the login page
 */

import React from 'react';
import { LucideIcon, AlertTriangle, RefreshCw } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../../hooks/useLanguage';
import { RTLIcon, RTLText } from '../../rtl';

interface AdminPageLayoutProps {
  title: string;
  titleAr?: string;
  subtitle?: string;
  subtitleAr?: string;
  icon: LucideIcon;
  children: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
}

const AdminPageLayout: React.FC<AdminPageLayoutProps> = ({
  title,
  titleAr,
  subtitle,
  subtitleAr,
  icon,
  children,
  actions,
  loading = false,
  error = null,
  onRefresh
}) => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const displayTitle = isRTL && titleAr ? titleAr : title;
  const displaySubtitle = isRTL && subtitleAr ? subtitleAr : subtitle;

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-4 ${isRTL ? "flex-row-reverse" : ""}`}>
      <div className="max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="mb-8">
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20">
            <div className={`flex items-center justify-between ${isRTL ? "flex-row-reverse" : ""}`}>
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 ${isRTL ? "ml-4" : "mr-4"}`}>
                  <RTLIcon icon={icon} size={24} className="text-purple-400" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-white">{displayTitle}</h1>
                  {displaySubtitle && (
                    <RTLText as="p" className="text-gray-300 mt-1">
                      {displaySubtitle}
                    </RTLText>
                  )}
                </div>
              </div>
              
              {/* Actions */}
              <div className={`flex items-center space-x-3 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
                {onRefresh && (
                  <button
                    onClick={onRefresh}
                    disabled={loading}
                    className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg border border-white/30 text-white transition-all duration-300 flex items-center"
                  >
                    <RTLIcon
                      icon={RefreshCw}
                      size={16}
                      className={`${loading ? 'animate-spin' : ''} ${isRTL ? 'ml-2' : 'mr-2'}`}
                    />
                    {t('common.refresh')}
                  </button>
                )}
                {actions}
              </div>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6">
            <div className="bg-red-500/20 backdrop-blur-sm rounded-lg p-4 shadow-lg border border-red-500/30">
              <div className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}>
                <RTLIcon icon={AlertTriangle} size={20} className={`text-red-400 ${isRTL ? "ml-3" : "mr-3"}`} />
                <RTLText className="text-red-300">{error}</RTLText>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="mb-6">
            <div className="bg-black/30 backdrop-blur-sm rounded-lg p-8 shadow-lg border border-white/20">
              <div className="flex items-center justify-center">
                <div className="inline-block w-8 h-8 border-4 border-purple-400 border-t-transparent rounded-full animate-spin mr-3"></div>
                <RTLText className="text-gray-300">{t('common.loading')}</RTLText>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="space-y-6">
          {children}
        </div>
      </div>
    </div>
  );
};

export default AdminPageLayout;

// Reusable Card Component for Admin Pages
export const AdminCard: React.FC<{
  title?: string;
  titleAr?: string;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}> = ({ title, titleAr, children, className = '', actions }) => {
  const { isRTL } = useLanguage();
  const displayTitle = isRTL && titleAr ? titleAr : title;

  return (
    <div className={`bg-black/30 backdrop-blur-sm rounded-lg p-6 shadow-lg border border-white/20 ${className}`}>
      {(title || actions) && (
        <div className={`flex items-center justify-between mb-4 ${isRTL ? "flex-row-reverse" : ""}`}>
          {displayTitle && (
            <h3 className="text-lg font-semibold text-white">{displayTitle}</h3>
          )}
          {actions && (
            <div className={`flex items-center space-x-2 ${isRTL ? "flex-row-reverse space-x-reverse" : ""}`}>
              {actions}
            </div>
          )}
        </div>
      )}
      {children}
    </div>
  );
};

// Reusable Button Component for Admin Pages
export const AdminButton: React.FC<{
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  loading?: boolean;
  icon?: LucideIcon;
  className?: string;
}> = ({ 
  children, 
  onClick, 
  variant = 'primary', 
  disabled = false, 
  loading = false, 
  icon,
  className = '' 
}) => {
  const { isRTL } = useLanguage();

  const baseClasses = "px-4 py-2 rounded-lg font-medium transition-all duration-300 flex items-center justify-center";
  
  const variantClasses = {
    primary: "bg-gradient-to-r from-purple-600 to-blue-600 hover:shadow-glow text-white",
    secondary: "bg-white/20 hover:bg-white/30 border border-white/30 text-white",
    danger: "bg-gradient-to-r from-red-600 to-red-700 hover:shadow-glow text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
    >
      {loading && (
        <div className={`inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin ${isRTL ? 'ml-2' : 'mr-2'}`}></div>
      )}
      {icon && !loading && (
        <RTLIcon icon={icon} size={16} className={isRTL ? 'ml-2' : 'mr-2'} />
      )}
      {children}
    </button>
  );
};

// Reusable Input Component for Admin Pages
export const AdminInput: React.FC<{
  label: string;
  labelAr?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  placeholderAr?: string;
  type?: string;
  required?: boolean;
  error?: string;
  className?: string;
}> = ({ 
  label, 
  labelAr, 
  value, 
  onChange, 
  placeholder, 
  placeholderAr, 
  type = 'text', 
  required = false, 
  error,
  className = '' 
}) => {
  const { isRTL } = useLanguage();
  const displayLabel = isRTL && labelAr ? labelAr : label;
  const displayPlaceholder = isRTL && placeholderAr ? placeholderAr : placeholder;

  return (
    <div className={className}>
      <label className="block text-sm font-medium text-gray-300 mb-1">
        {displayLabel}
        {required && <span className="text-red-400 ml-1">*</span>}
      </label>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={displayPlaceholder}
        className={`w-full px-4 py-2 bg-white/20 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 text-white placeholder-gray-400 ${
          error ? 'border-red-500/50' : 'border-white/30'
        }`}
        dir={isRTL ? 'rtl' : 'ltr'}
      />
      {error && (
        <RTLText className="text-red-400 text-sm mt-1">{error}</RTLText>
      )}
    </div>
  );
};
