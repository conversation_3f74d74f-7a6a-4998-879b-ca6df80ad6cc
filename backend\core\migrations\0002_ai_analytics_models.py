# Generated by Django 5.2.1 on 2025-08-07 08:08

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    dependencies = [
        ("core", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AIConversationSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_id",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("general", "General Chat"),
                            ("business", "Business Consultation"),
                            ("syrian_business", "Syrian Business"),
                            ("recommendation", "Recommendations"),
                            ("mentorship", "Mentorship"),
                            ("idea_builder", "Idea Building"),
                        ],
                        default="general",
                        max_length=20,
                    ),
                ),
                (
                    "user_region",
                    models.CharField(
                        choices=[
                            ("damascus_dialect", "Damascus"),
                            ("aleppo_dialect", "Aleppo"),
                            ("homs_dialect", "Homs"),
                            ("latakia_dialect", "Latakia"),
                        ],
                        default="damascus_dialect",
                        max_length=20,
                    ),
                ),
                (
                    "started_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                ("duration_seconds", models.IntegerField(default=0)),
                (
                    "model_used",
                    models.CharField(default="gemini-2.0-flash", max_length=50),
                ),
                ("langgraph_enabled", models.BooleanField(default=True)),
                (
                    "authenticity_level",
                    models.CharField(default="street_level", max_length=20),
                ),
                ("total_messages", models.IntegerField(default=0)),
                ("total_tokens_used", models.IntegerField(default=0)),
                (
                    "total_cost_usd",
                    models.DecimalField(decimal_places=6, default=0, max_digits=10),
                ),
                ("average_response_time", models.FloatField(default=0)),
                (
                    "user_satisfaction",
                    models.IntegerField(
                        blank=True,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(1),
                            django.core.validators.MaxValueValidator(5),
                        ],
                    ),
                ),
                ("personality_established", models.BooleanField(default=False)),
                ("workflow_completed", models.BooleanField(default=False)),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "ai_conversation_sessions",
                "ordering": ["-started_at"],
            },
        ),
        migrations.CreateModel(
            name="AIMessage",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "message_id",
                    models.CharField(db_index=True, max_length=100, unique=True),
                ),
                (
                    "message_type",
                    models.CharField(
                        choices=[
                            ("user", "User Message"),
                            ("ai", "AI Response"),
                            ("system", "System Message"),
                        ],
                        max_length=10,
                    ),
                ),
                ("content", models.TextField()),
                ("content_length", models.IntegerField(default=0)),
                ("language_detected", models.CharField(default="ar", max_length=10)),
                ("tokens_input", models.IntegerField(default=0)),
                ("tokens_output", models.IntegerField(default=0)),
                ("processing_time_ms", models.IntegerField(default=0)),
                (
                    "cost_usd",
                    models.DecimalField(decimal_places=6, default=0, max_digits=8),
                ),
                ("workflow_state", models.CharField(blank=True, max_length=50)),
                ("nodes_executed", models.JSONField(default=list)),
                ("analysis_results", models.JSONField(default=dict)),
                (
                    "created_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="core.aiconversationsession",
                    ),
                ),
            ],
            options={
                "db_table": "ai_messages",
                "ordering": ["created_at"],
            },
        ),
        migrations.CreateModel(
            name="AIErrorLog",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "error_type",
                    models.CharField(
                        choices=[
                            ("api_error", "API Error"),
                            ("timeout", "Timeout"),
                            ("quota_exceeded", "Quota Exceeded"),
                            ("invalid_request", "Invalid Request"),
                            ("langgraph_error", "LangGraph Error"),
                            ("processing_error", "Processing Error"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("error_message", models.TextField()),
                ("error_details", models.JSONField(default=dict)),
                ("stack_trace", models.TextField(blank=True)),
                ("user_input", models.TextField(blank=True)),
                ("model_used", models.CharField(blank=True, max_length=50)),
                ("request_data", models.JSONField(default=dict)),
                (
                    "occurred_at",
                    models.DateTimeField(
                        db_index=True, default=django.utils.timezone.now
                    ),
                ),
                ("resolved", models.BooleanField(default=False)),
                ("resolution_notes", models.TextField(blank=True)),
                (
                    "session",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.aiconversationsession",
                    ),
                ),
                (
                    "message",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to="core.aimessage",
                    ),
                ),
            ],
            options={
                "db_table": "ai_error_logs",
                "ordering": ["-occurred_at"],
            },
        ),
        migrations.CreateModel(
            name="AITopicAnalysis",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "topic_category",
                    models.CharField(
                        choices=[
                            ("business_planning", "Business Planning"),
                            ("investment", "Investment"),
                            ("market_analysis", "Market Analysis"),
                            ("startup_advice", "Startup Advice"),
                            ("damascus_business", "Damascus Business"),
                            ("aleppo_business", "Aleppo Business"),
                            ("technology", "Technology"),
                            ("general_chat", "General Chat"),
                            ("personal_advice", "Personal Advice"),
                            ("other", "Other"),
                        ],
                        max_length=30,
                    ),
                ),
                ("topic_keywords", models.JSONField(default=list)),
                ("topic_confidence", models.FloatField(default=0.0)),
                ("business_intent", models.BooleanField(default=False)),
                ("investment_related", models.BooleanField(default=False)),
                ("regional_context", models.CharField(blank=True, max_length=20)),
                (
                    "analyzed_at",
                    models.DateTimeField(default=django.utils.timezone.now),
                ),
                (
                    "analysis_method",
                    models.CharField(default="ai_classification", max_length=20),
                ),
                (
                    "message",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics",
                        to="core.aimessage",
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="topics",
                        to="core.aiconversationsession",
                    ),
                ),
            ],
            options={
                "db_table": "ai_topic_analysis",
                "ordering": ["-analyzed_at"],
            },
        ),
        migrations.CreateModel(
            name="AIUsageStatistics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "period_type",
                    models.CharField(
                        choices=[
                            ("hourly", "Hourly"),
                            ("daily", "Daily"),
                            ("weekly", "Weekly"),
                            ("monthly", "Monthly"),
                        ],
                        max_length=10,
                    ),
                ),
                ("period_start", models.DateTimeField(db_index=True)),
                ("period_end", models.DateTimeField()),
                ("total_sessions", models.IntegerField(default=0)),
                ("total_messages", models.IntegerField(default=0)),
                ("total_users", models.IntegerField(default=0)),
                ("unique_users", models.IntegerField(default=0)),
                ("total_tokens", models.IntegerField(default=0)),
                (
                    "total_cost_usd",
                    models.DecimalField(decimal_places=6, default=0, max_digits=12),
                ),
                ("average_tokens_per_message", models.FloatField(default=0)),
                ("average_response_time", models.FloatField(default=0)),
                ("success_rate", models.FloatField(default=0)),
                ("error_count", models.IntegerField(default=0)),
                ("damascus_sessions", models.IntegerField(default=0)),
                ("aleppo_sessions", models.IntegerField(default=0)),
                ("homs_sessions", models.IntegerField(default=0)),
                ("latakia_sessions", models.IntegerField(default=0)),
                ("business_topics", models.IntegerField(default=0)),
                ("general_topics", models.IntegerField(default=0)),
                ("investment_topics", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                "db_table": "ai_usage_statistics",
                "ordering": ["-period_start"],
                "indexes": [
                    models.Index(
                        fields=["period_type", "period_start"],
                        name="ai_usage_st_period__6f45e2_idx",
                    )
                ],
                "unique_together": {("period_type", "period_start")},
            },
        ),
        migrations.AddIndex(
            model_name="aiconversationsession",
            index=models.Index(
                fields=["user", "started_at"], name="ai_conversa_user_id_6cab94_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aiconversationsession",
            index=models.Index(
                fields=["session_type", "started_at"],
                name="ai_conversa_session_165a75_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aiconversationsession",
            index=models.Index(
                fields=["user_region", "started_at"],
                name="ai_conversa_user_re_4f8034_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aimessage",
            index=models.Index(
                fields=["session", "created_at"], name="ai_messages_session_7725d6_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="aimessage",
            index=models.Index(
                fields=["message_type", "created_at"],
                name="ai_messages_message_05bb39_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aierrorlog",
            index=models.Index(
                fields=["error_type", "occurred_at"],
                name="ai_error_lo_error_t_de994a_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aierrorlog",
            index=models.Index(
                fields=["resolved", "occurred_at"],
                name="ai_error_lo_resolve_5bf43b_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aitopicanalysis",
            index=models.Index(
                fields=["topic_category", "analyzed_at"],
                name="ai_topic_an_topic_c_faa000_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="aitopicanalysis",
            index=models.Index(
                fields=["business_intent", "analyzed_at"],
                name="ai_topic_an_busines_651d0c_idx",
            ),
        ),
    ]
