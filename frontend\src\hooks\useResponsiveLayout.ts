/**
 * Responsive Layout Hook
 * Manages layout state and responsive behavior for sidebar and main content
 */

import { useState, useEffect, useCallback } from 'react';

interface ResponsiveLayoutState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  screenHeight: number;
  sidebarWidth: number;
  contentWidth: number;
  orientation: 'portrait' | 'landscape';
}

interface ResponsiveLayoutActions {
  updateLayout: () => void;
  getSidebarWidth: (isCollapsed: boolean) => number;
  getContentWidth: (isCollapsed: boolean) => number;
  shouldShowMobileSidebar: () => boolean;
  shouldShowDesktopSidebar: () => boolean;
}

export const useResponsiveLayout = (): ResponsiveLayoutState & ResponsiveLayoutActions => {
  const [layoutState, setLayoutState] = useState<ResponsiveLayoutState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    screenWidth: 0,
    screenHeight: 0,
    sidebarWidth: 320,
    contentWidth: 0,
    orientation: 'portrait'
  });

  const updateLayout = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    
    const isMobile = width < 768;
    const isTablet = width >= 768 && width < 1024;
    const isDesktop = width >= 1024;
    
    const sidebarWidth = isDesktop ? 320 : 0;
    const contentWidth = isDesktop ? width - sidebarWidth : width;
    
    setLayoutState({
      isMobile,
      isTablet,
      isDesktop,
      screenWidth: width,
      screenHeight: height,
      sidebarWidth,
      contentWidth,
      orientation: width > height ? 'landscape' : 'portrait'
    });

    // Update CSS custom properties for responsive design
    document.documentElement.style.setProperty('--screen-width', `${width}px`);
    document.documentElement.style.setProperty('--screen-height', `${height}px`);
    document.documentElement.style.setProperty('--sidebar-width', `${sidebarWidth}px`);
    document.documentElement.style.setProperty('--content-width', `${contentWidth}px`);
  }, []);

  const getSidebarWidth = useCallback((isCollapsed: boolean): number => {
    if (layoutState.isMobile) return 0;
    return isCollapsed ? 80 : 320;
  }, [layoutState.isMobile]);

  const getContentWidth = useCallback((isCollapsed: boolean): number => {
    if (layoutState.isMobile) return layoutState.screenWidth;
    const sidebarWidth = getSidebarWidth(isCollapsed);
    return layoutState.screenWidth - sidebarWidth;
  }, [layoutState.isMobile, layoutState.screenWidth, getSidebarWidth]);

  const shouldShowMobileSidebar = useCallback((): boolean => {
    return layoutState.isMobile || layoutState.isTablet;
  }, [layoutState.isMobile, layoutState.isTablet]);

  const shouldShowDesktopSidebar = useCallback((): boolean => {
    return layoutState.isDesktop;
  }, [layoutState.isDesktop]);

  useEffect(() => {
    // Initial layout calculation
    updateLayout();

    // Add event listeners
    window.addEventListener('resize', updateLayout);
    window.addEventListener('orientationchange', updateLayout);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateLayout);
      window.removeEventListener('orientationchange', updateLayout);
    };
  }, [updateLayout]);

  return {
    ...layoutState,
    updateLayout,
    getSidebarWidth,
    getContentWidth,
    shouldShowMobileSidebar,
    shouldShowDesktopSidebar
  };
};

export default useResponsiveLayout;
