"""
Management command to populate sample data for testing
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group
from django.utils import timezone
from datetime import timedelta
import random

from users.models import UserProfile, UserRole, RoleApplication
from incubator.models_base import BusinessIdea
from incubator.models_business_plan import BusinessPlan, BusinessPlanTemplate
from community.models import CommunityPost, Category


class Command(BaseCommand):
    help = 'Populate database with sample data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating',
        )

    def handle(self, *args, **options):
        if options['clear']:
            self.stdout.write('Clearing existing data...')
            self.clear_data()

        self.stdout.write('Creating sample data...')
        
        # Create roles
        self.create_roles()
        
        # Create sample users
        self.create_sample_users()
        
        # Create business plan templates
        self.create_business_templates()
        
        # Create sample business ideas
        self.create_business_ideas()
        
        # Create community categories and posts
        self.create_community_data()
        
        self.stdout.write(
            self.style.SUCCESS('Successfully populated sample data!')
        )

    def clear_data(self):
        """Clear existing sample data"""
        # Clear community data
        CommunityPost.objects.all().delete()
        Category.objects.all().delete()
        
        # Clear business data
        BusinessIdea.objects.all().delete()
        BusinessPlan.objects.all().delete()
        
        # Clear user data (except admin)
        User.objects.exclude(username='admin').delete()

    def create_roles(self):
        """Create basic roles"""
        roles_data = [
            {'name': 'entrepreneur', 'display_name': 'Entrepreneur'},
            {'name': 'mentor', 'display_name': 'Mentor'},
            {'name': 'investor', 'display_name': 'Investor'},
            {'name': 'moderator', 'display_name': 'Moderator'},
        ]
        
        for role_data in roles_data:
            role, created = UserRole.objects.get_or_create(
                name=role_data['name'],
                defaults={'display_name': role_data['display_name']}
            )
            if created:
                self.stdout.write(f'Created role: {role.display_name}')

    def create_sample_users(self):
        """Create sample users with different roles"""
        users_data = [
            {
                'username': 'entrepreneur1',
                'email': '<EMAIL>',
                'first_name': 'Ahmed',
                'last_name': 'Hassan',
                'role': 'entrepreneur'
            },
            {
                'username': 'mentor1',
                'email': '<EMAIL>',
                'first_name': 'Sarah',
                'last_name': 'Ali',
                'role': 'mentor'
            },
            {
                'username': 'investor1',
                'email': '<EMAIL>',
                'first_name': 'Omar',
                'last_name': 'Mahmoud',
                'role': 'investor'
            },
        ]
        
        for user_data in users_data:
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults={
                    'email': user_data['email'],
                    'first_name': user_data['first_name'],
                    'last_name': user_data['last_name'],
                    'is_active': True
                }
            )
            
            if created:
                user.set_password('password123')
                user.save()
                
                # Create user profile
                profile, _ = UserProfile.objects.get_or_create(
                    user=user,
                    defaults={
                        'bio': f'Sample {user_data["role"]} user',
                        'location': 'Cairo, Egypt',
                        'phone_number': '+201234567890'
                    }
                )
                
                # Create role application
                role = UserRole.objects.get(name=user_data['role'])
                RoleApplication.objects.get_or_create(
                    user=user,
                    requested_role=role,
                    defaults={
                        'status': 'approved',
                        'motivation': f'Sample motivation for {user_data["role"]} role',
                        'qualifications': f'Sample qualifications for {user_data["role"]}',
                        'experience': 'Sample experience',
                        'admin_notes': 'Sample data'
                    }
                )
                
                self.stdout.write(f'Created user: {user.username} ({user_data["role"]})')

    def create_business_templates(self):
        """Create sample business plan templates"""
        if not BusinessPlanTemplate.objects.exists():
            template = BusinessPlanTemplate.objects.create(
                name="Standard Business Plan",
                description="Comprehensive business plan template suitable for most businesses",
                industry="General",
                template_type="standard",
                difficulty_level="intermediate",
                estimated_time_hours=8,
                is_customizable=True,
                usage_count=0,
                rating=4.5,
                completion_rate=85.0
            )
            self.stdout.write(f'Created business plan template: {template.name}')

    def create_business_ideas(self):
        """Create sample business ideas"""
        ideas_data = [
            {
                'title': 'E-commerce Platform for Local Artisans',
                'description': 'A platform connecting local artisans with customers worldwide',
                'problem_statement': 'Local artisans struggle to reach global customers',
                'solution_description': 'Online marketplace with global shipping and marketing support',
                'target_audience': 'Art lovers and collectors worldwide',
                'current_stage': 'concept'
            },
            {
                'title': 'AI-Powered Language Learning App',
                'description': 'Mobile app using AI to personalize language learning',
                'problem_statement': 'Traditional language learning is not personalized',
                'solution_description': 'AI adapts to individual learning styles and pace',
                'target_audience': 'Language learners of all ages',
                'current_stage': 'concept'
            },
            {
                'title': 'Sustainable Food Delivery Service',
                'description': 'Eco-friendly food delivery using electric vehicles',
                'problem_statement': 'Food delivery contributes to environmental pollution',
                'solution_description': 'Electric vehicle fleet with sustainable packaging',
                'target_audience': 'Environmentally conscious consumers',
                'current_stage': 'validation'
            }
        ]

        entrepreneur = User.objects.filter(username='entrepreneur1').first()
        if entrepreneur:
            for idea_data in ideas_data:
                idea, created = BusinessIdea.objects.get_or_create(
                    title=idea_data['title'],
                    defaults={
                        'owner': entrepreneur,
                        'description': idea_data['description'],
                        'problem_statement': idea_data['problem_statement'],
                        'solution_description': idea_data['solution_description'],
                        'target_audience': idea_data['target_audience'],
                        'current_stage': idea_data['current_stage'],
                        'moderation_status': 'approved'
                    }
                )
                if created:
                    self.stdout.write(f'Created business idea: {idea.title}')

    def create_community_data(self):
        """Create sample community categories and posts"""
        # Create categories
        categories_data = [
            {'name': 'General Discussion', 'description': 'General business discussions'},
            {'name': 'Funding', 'description': 'Funding and investment discussions'},
            {'name': 'Mentorship', 'description': 'Mentorship and guidance'},
            {'name': 'Success Stories', 'description': 'Share your success stories'}
        ]
        
        for cat_data in categories_data:
            category, created = Category.objects.get_or_create(
                name=cat_data['name'],
                defaults={'description': cat_data['description']}
            )
            if created:
                self.stdout.write(f'Created category: {category.name}')

        # Create sample posts
        posts_data = [
            {
                'title': 'Welcome to Yasmeen Community!',
                'content': 'Welcome to our entrepreneurship community. Share your ideas and connect with fellow entrepreneurs.',
                'category': 'General Discussion'
            },
            {
                'title': 'Tips for First-Time Entrepreneurs',
                'content': 'Here are some essential tips for anyone starting their entrepreneurial journey...',
                'category': 'General Discussion'
            },
            {
                'title': 'Funding Options in the Middle East',
                'content': 'A comprehensive guide to funding opportunities available for startups in the region.',
                'category': 'Funding'
            }
        ]
        
        entrepreneur = User.objects.filter(username='entrepreneur1').first()
        if entrepreneur:
            for post_data in posts_data:
                category = Category.objects.get(name=post_data['category'])
                post, created = CommunityPost.objects.get_or_create(
                    title=post_data['title'],
                    defaults={
                        'author': entrepreneur,
                        'content': post_data['content'],
                        'category': category,
                        'visibility': 'public'
                    }
                )
                if created:
                    self.stdout.write(f'Created community post: {post.title}')
