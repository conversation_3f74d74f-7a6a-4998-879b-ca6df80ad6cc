/**
 * 🚀 LAZY ROUTES WITH CODE SPLITTING
 * Optimized route-based code splitting with preloading and error boundaries
 */

import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';
import { createLazyComponent, preloadComponent } from '../../utils/performanceOptimization';
import { EnhancedErrorBoundary } from '../error/EnhancedErrorBoundary';
import { LoadingSpinner } from '../common/LoadingSpinner';

// ========================================
// LAZY LOADED COMPONENTS
// ========================================

// Main Pages
const HomePage = createLazyComponent(
  () => import('../../pages/HomePage'),
  { preload: true }
);

const CommunityPage = createLazyComponent(
  () => import('../../pages/CommunityPage'),
  { preload: true }
);

const ProfilePage = createLazyComponent(
  () => import('../../pages/ProfilePage')
);

const ChatPage = createLazyComponent(
  () => import('../../pages/ChatPage')
);

const NotificationsPage = createLazyComponent(
  () => import('../../pages/NotificationsPage')
);

// Admin Pages (Heavy components)
const AdminDashboard = createLazyComponent(
  () => import('../../pages/admin/AdminDashboard')
);

const AdminUsers = createLazyComponent(
  () => import('../../pages/admin/AdminUsers')
);

const AdminAnalytics = createLazyComponent(
  () => import('../../pages/admin/AnalyticsPage')
);

// Business Pages
const BusinessPlan = createLazyComponent(
  () => import('../../pages/business/BusinessPlan')
);

const Mentorship = createLazyComponent(
  () => import('../../pages/mentorship/Mentorship')
);

const Investment = createLazyComponent(
  () => import('../../pages/investment/Investment')
);

// AI Features (Heavy ML components)
const AIChat = createLazyComponent(
  () => import('../../pages/ai/AIChat')
);

const AIAnalysis = createLazyComponent(
  () => import('../../pages/ai/AIAnalysis')
);

// Settings and Configuration
const Settings = createLazyComponent(
  () => import('../../pages/Settings')
);

const SecuritySettings = createLazyComponent(
  () => import('../../pages/security/SecuritySettings')
);

// ========================================
// PRELOADING STRATEGIES
// ========================================

// Preload critical routes on app start
export const preloadCriticalRoutes = () => {
  const criticalRoutes = [
    { key: 'home', importFn: () => import('../../pages/HomePage') },
    { key: 'community', importFn: () => import('../../pages/CommunityPage') },
    { key: 'profile', importFn: () => import('../../pages/ProfilePage') }
  ];

  return Promise.allSettled(
    criticalRoutes.map(({ key, importFn }) => preloadComponent(key, importFn))
  );
};

// Preload routes based on user role
export const preloadRoleBasedRoutes = (userRole: string) => {
  const roleRoutes: Record<string, Array<{ key: string; importFn: () => Promise<any> }>> = {
    admin: [
      { key: 'admin-dashboard', importFn: () => import('../../pages/admin/AdminDashboard') },
      { key: 'admin-users', importFn: () => import('../../pages/admin/AdminUsers') },
      { key: 'admin-analytics', importFn: () => import('../../pages/admin/AnalyticsPage') }
    ],
    entrepreneur: [
      { key: 'business-plan', importFn: () => import('../../pages/business/BusinessPlan') },
      { key: 'mentorship', importFn: () => import('../../pages/mentorship/Mentorship') },
      { key: 'investment', importFn: () => import('../../pages/investment/Investment') }
    ],
    mentor: [
      { key: 'mentorship', importFn: () => import('../../pages/mentorship/Mentorship') },
      { key: 'ai-analysis', importFn: () => import('../../pages/ai/AIAnalysis') }
    ]
  };

  const routes = roleRoutes[userRole] || [];
  return Promise.allSettled(
    routes.map(({ key, importFn }) => preloadComponent(key, importFn))
  );
};

// Preload on hover (for navigation links)
export const preloadOnHover = (routeKey: string) => {
  const routeImports: Record<string, () => Promise<any>> = {
    'chat': () => import('../../pages/ChatPage'),
    'notifications': () => import('../../pages/NotificationsPage'),
    'ai-chat': () => import('../../pages/ai/AIChat'),
    'settings': () => import('../../pages/Settings'),
    'security': () => import('../../pages/security/SecuritySettings')
  };

  const importFn = routeImports[routeKey];
  if (importFn) {
    preloadComponent(routeKey, importFn);
  }
};

// ========================================
// LOADING COMPONENTS
// ========================================

const RouteLoadingFallback: React.FC<{ route?: string }> = ({ route }) => (
  <div className="flex items-center justify-center min-h-[400px]">
    <div className="text-center">
      <LoadingSpinner size="large" />
      <p className="mt-4 text-white/70">
        Loading {route ? route.charAt(0).toUpperCase() + route.slice(1) : 'page'}...
      </p>
    </div>
  </div>
);

const AdminLoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center min-h-[600px]">
    <div className="text-center">
      <LoadingSpinner size="large" />
      <p className="mt-4 text-white/70">Loading admin dashboard...</p>
      <p className="mt-2 text-sm text-white/50">This may take a moment</p>
    </div>
  </div>
);

const AILoadingFallback: React.FC = () => (
  <div className="flex items-center justify-center min-h-[500px]">
    <div className="text-center">
      <LoadingSpinner size="large" />
      <p className="mt-4 text-white/70">Initializing AI features...</p>
      <p className="mt-2 text-sm text-white/50">Loading machine learning models</p>
    </div>
  </div>
);

// ========================================
// OPTIMIZED ROUTES COMPONENT
// ========================================

export const LazyRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Main Routes */}
      <Route 
        path="/" 
        element={
          <EnhancedErrorBoundary level="page" context="HomePage">
            <Suspense fallback={<RouteLoadingFallback route="home" />}>
              <HomePage />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/community" 
        element={
          <EnhancedErrorBoundary level="page" context="CommunityPage">
            <Suspense fallback={<RouteLoadingFallback route="community" />}>
              <CommunityPage />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/profile" 
        element={
          <EnhancedErrorBoundary level="page" context="ProfilePage">
            <Suspense fallback={<RouteLoadingFallback route="profile" />}>
              <ProfilePage />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/chat" 
        element={
          <EnhancedErrorBoundary level="page" context="ChatPage">
            <Suspense fallback={<RouteLoadingFallback route="chat" />}>
              <ChatPage />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/notifications" 
        element={
          <EnhancedErrorBoundary level="page" context="NotificationsPage">
            <Suspense fallback={<RouteLoadingFallback route="notifications" />}>
              <NotificationsPage />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      {/* Admin Routes */}
      <Route 
        path="/admin" 
        element={
          <EnhancedErrorBoundary level="critical" context="AdminDashboard">
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminDashboard />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/admin/users" 
        element={
          <EnhancedErrorBoundary level="page" context="AdminUsers">
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminUsers />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/admin/analytics" 
        element={
          <EnhancedErrorBoundary level="page" context="AdminAnalytics">
            <Suspense fallback={<AdminLoadingFallback />}>
              <AdminAnalytics />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      {/* Business Routes */}
      <Route 
        path="/business-plan" 
        element={
          <EnhancedErrorBoundary level="page" context="BusinessPlan">
            <Suspense fallback={<RouteLoadingFallback route="business plan" />}>
              <BusinessPlan />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/mentorship" 
        element={
          <EnhancedErrorBoundary level="page" context="Mentorship">
            <Suspense fallback={<RouteLoadingFallback route="mentorship" />}>
              <Mentorship />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/investment" 
        element={
          <EnhancedErrorBoundary level="page" context="Investment">
            <Suspense fallback={<RouteLoadingFallback route="investment" />}>
              <Investment />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      {/* AI Routes */}
      <Route 
        path="/ai/chat" 
        element={
          <EnhancedErrorBoundary level="page" context="AIChat">
            <Suspense fallback={<AILoadingFallback />}>
              <AIChat />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/ai/analysis" 
        element={
          <EnhancedErrorBoundary level="page" context="AIAnalysis">
            <Suspense fallback={<AILoadingFallback />}>
              <AIAnalysis />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      {/* Settings Routes */}
      <Route 
        path="/settings" 
        element={
          <EnhancedErrorBoundary level="page" context="Settings">
            <Suspense fallback={<RouteLoadingFallback route="settings" />}>
              <Settings />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      <Route 
        path="/settings/security" 
        element={
          <EnhancedErrorBoundary level="page" context="SecuritySettings">
            <Suspense fallback={<RouteLoadingFallback route="security settings" />}>
              <SecuritySettings />
            </Suspense>
          </EnhancedErrorBoundary>
        } 
      />

      {/* 404 Route */}
      <Route 
        path="*" 
        element={
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-white mb-4">404</h1>
              <p className="text-white/70">Page not found</p>
            </div>
          </div>
        } 
      />
    </Routes>
  );
};
