/**
 * 📊 MONITORING AND ANALYTICS SERVICE
 * Comprehensive performance monitoring, user analytics, and system health tracking
 */

import { api } from './api';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

export interface UserEvent {
  type: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  timestamp: number;
  userId?: string;
  sessionId: string;
  metadata?: Record<string, any>;
}

export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'critical';
  uptime: number;
  responseTime: number;
  errorRate: number;
  memoryUsage: number;
  activeUsers: number;
  lastCheck: number;
}

export interface AnalyticsConfig {
  enabled: boolean;
  sampleRate: number;
  batchSize: number;
  flushInterval: number;
  trackPageViews: boolean;
  trackUserInteractions: boolean;
  trackPerformance: boolean;
  trackErrors: boolean;
  anonymizeData: boolean;
}

class MonitoringService {
  private sessionId: string;
  private userId?: string;
  private config: AnalyticsConfig;
  private eventQueue: UserEvent[] = [];
  private metricQueue: PerformanceMetric[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private performanceObserver: PerformanceObserver | null = null;
  private startTime = Date.now();

  constructor() {
    this.sessionId = this.generateSessionId();
    this.config = this.getDefaultConfig();
    this.setupPerformanceMonitoring();
    this.setupUserTracking();
    this.setupSystemHealthMonitoring();
    this.startFlushTimer();
  }

  /**
   * Get default analytics configuration
   */
  private getDefaultConfig(): AnalyticsConfig {
    return {
      enabled: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
      sampleRate: parseFloat(import.meta.env.VITE_ANALYTICS_SAMPLE_RATE || '0.1'),
      batchSize: 50,
      flushInterval: 30000, // 30 seconds
      trackPageViews: true,
      trackUserInteractions: true,
      trackPerformance: true,
      trackErrors: true,
      anonymizeData: import.meta.env.PROD
    };
  }

  /**
   * Setup performance monitoring
   */
  private setupPerformanceMonitoring(): void {
    if (!this.config.trackPerformance || !this.config.enabled) return;

    // Web Vitals monitoring
    this.trackWebVitals();

    // Performance Observer
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.trackMetric({
            name: `performance.${entry.entryType}.${entry.name}`,
            value: entry.duration || entry.startTime,
            timestamp: Date.now(),
            tags: {
              entryType: entry.entryType,
              name: entry.name
            }
          });
        });
      });

      this.performanceObserver.observe({
        entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input']
      });
    }

    // Memory monitoring
    this.setupMemoryMonitoring();

    // Network monitoring
    this.setupNetworkMonitoring();
  }

  /**
   * Track Web Vitals
   */
  private trackWebVitals(): void {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        this.trackMetric({
          name: 'web_vitals.fcp',
          value: entry.startTime,
          timestamp: Date.now()
        });
      });
    }).observe({ entryTypes: ['paint'] });

    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        this.trackMetric({
          name: 'web_vitals.lcp',
          value: entry.startTime,
          timestamp: Date.now()
        });
      });
    }).observe({ entryTypes: ['largest-contentful-paint'] });

    // First Input Delay
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry: any) => {
        this.trackMetric({
          name: 'web_vitals.fid',
          value: entry.processingStart - entry.startTime,
          timestamp: Date.now()
        });
      });
    }).observe({ entryTypes: ['first-input'] });

    // Cumulative Layout Shift
    let clsValue = 0;
    new PerformanceObserver((list) => {
      list.getEntries().forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value;
          this.trackMetric({
            name: 'web_vitals.cls',
            value: clsValue,
            timestamp: Date.now()
          });
        }
      });
    }).observe({ entryTypes: ['layout-shift'] });
  }

  /**
   * Setup memory monitoring
   */
  private setupMemoryMonitoring(): void {
    if (!('memory' in performance)) return;

    setInterval(() => {
      const memory = (performance as any).memory;
      
      this.trackMetric({
        name: 'memory.used_heap_size',
        value: memory.usedJSHeapSize,
        timestamp: Date.now()
      });

      this.trackMetric({
        name: 'memory.total_heap_size',
        value: memory.totalJSHeapSize,
        timestamp: Date.now()
      });

      this.trackMetric({
        name: 'memory.heap_size_limit',
        value: memory.jsHeapSizeLimit,
        timestamp: Date.now()
      });
    }, 60000); // Every minute
  }

  /**
   * Setup network monitoring
   */
  private setupNetworkMonitoring(): void {
    if (!('connection' in navigator)) return;

    const connection = (navigator as any).connection;
    
    this.trackMetric({
      name: 'network.effective_type',
      value: this.connectionTypeToNumber(connection.effectiveType),
      timestamp: Date.now(),
      tags: { effectiveType: connection.effectiveType }
    });

    this.trackMetric({
      name: 'network.downlink',
      value: connection.downlink,
      timestamp: Date.now()
    });

    // Monitor connection changes
    connection.addEventListener('change', () => {
      this.trackEvent({
        type: 'network_change',
        category: 'system',
        action: 'connection_change',
        metadata: {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        }
      });
    });
  }

  /**
   * Setup user tracking
   */
  private setupUserTracking(): void {
    if (!this.config.enabled) return;

    // Page view tracking
    if (this.config.trackPageViews) {
      this.trackPageView();
      
      // Track route changes
      window.addEventListener('popstate', () => {
        this.trackPageView();
      });
    }

    // User interaction tracking
    if (this.config.trackUserInteractions) {
      this.setupInteractionTracking();
    }

    // Visibility change tracking
    document.addEventListener('visibilitychange', () => {
      this.trackEvent({
        type: 'visibility_change',
        category: 'engagement',
        action: document.hidden ? 'page_hidden' : 'page_visible'
      });
    });

    // Session tracking
    this.trackSessionStart();
  }

  /**
   * Setup interaction tracking
   */
  private setupInteractionTracking(): void {
    // Click tracking
    document.addEventListener('click', (e) => {
      const target = e.target as Element;
      this.trackEvent({
        type: 'click',
        category: 'interaction',
        action: 'click',
        label: target.tagName.toLowerCase(),
        metadata: {
          className: target.className,
          id: target.id,
          text: target.textContent?.substring(0, 100)
        }
      });
    });

    // Form submission tracking
    document.addEventListener('submit', (e) => {
      const form = e.target as HTMLFormElement;
      this.trackEvent({
        type: 'form_submit',
        category: 'interaction',
        action: 'form_submit',
        label: form.id || form.className,
        metadata: {
          action: form.action,
          method: form.method
        }
      });
    });

    // Scroll tracking
    let scrollTimeout: NodeJS.Timeout;
    document.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout);
      scrollTimeout = setTimeout(() => {
        const scrollPercent = Math.round(
          (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
        );
        
        this.trackEvent({
          type: 'scroll',
          category: 'engagement',
          action: 'scroll',
          value: scrollPercent
        });
      }, 1000);
    });
  }

  /**
   * Setup system health monitoring
   */
  private setupSystemHealthMonitoring(): void {
    setInterval(() => {
      this.checkSystemHealth();
    }, 60000); // Every minute
  }

  /**
   * Track user event
   */
  trackEvent(event: Omit<UserEvent, 'timestamp' | 'sessionId'>): void {
    if (!this.config.enabled || !this.shouldSample()) return;

    const fullEvent: UserEvent = {
      ...event,
      timestamp: Date.now(),
      sessionId: this.sessionId,
      userId: this.userId
    };

    // Anonymize data if required
    if (this.config.anonymizeData) {
      delete fullEvent.userId;
      if (fullEvent.metadata) {
        delete fullEvent.metadata.email;
        delete fullEvent.metadata.phone;
        delete fullEvent.metadata.ip;
      }
    }

    this.eventQueue.push(fullEvent);

    if (this.eventQueue.length >= this.config.batchSize) {
      this.flushEvents();
    }
  }

  /**
   * Track performance metric
   */
  trackMetric(metric: PerformanceMetric): void {
    if (!this.config.enabled || !this.shouldSample()) return;

    this.metricQueue.push(metric);

    if (this.metricQueue.length >= this.config.batchSize) {
      this.flushMetrics();
    }
  }

  /**
   * Track page view
   */
  trackPageView(): void {
    this.trackEvent({
      type: 'page_view',
      category: 'navigation',
      action: 'page_view',
      label: window.location.pathname,
      metadata: {
        url: window.location.href,
        referrer: document.referrer,
        title: document.title
      }
    });
  }

  /**
   * Track session start
   */
  private trackSessionStart(): void {
    this.trackEvent({
      type: 'session_start',
      category: 'session',
      action: 'start',
      metadata: {
        userAgent: navigator.userAgent,
        language: navigator.language,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        screenResolution: `${screen.width}x${screen.height}`,
        viewportSize: `${window.innerWidth}x${window.innerHeight}`
      }
    });
  }

  /**
   * Check system health
   */
  private async checkSystemHealth(): Promise<SystemHealth> {
    const startTime = Date.now();
    
    try {
      // Ping health endpoint
      await api.get('/api/health/');
      const responseTime = Date.now() - startTime;

      const health: SystemHealth = {
        status: 'healthy',
        uptime: Date.now() - this.startTime,
        responseTime,
        errorRate: this.calculateErrorRate(),
        memoryUsage: this.getMemoryUsage(),
        activeUsers: 1, // Would be provided by backend
        lastCheck: Date.now()
      };

      this.trackMetric({
        name: 'system.health.response_time',
        value: responseTime,
        timestamp: Date.now()
      });

      return health;
    } catch (error) {
      const health: SystemHealth = {
        status: 'critical',
        uptime: Date.now() - this.startTime,
        responseTime: Date.now() - startTime,
        errorRate: 1,
        memoryUsage: this.getMemoryUsage(),
        activeUsers: 0,
        lastCheck: Date.now()
      };

      this.trackEvent({
        type: 'system_error',
        category: 'system',
        action: 'health_check_failed',
        metadata: { error: error instanceof Error ? error.message : String(error) }
      });

      return health;
    }
  }

  /**
   * Calculate error rate
   */
  private calculateErrorRate(): number {
    // This would be calculated based on recent events
    const recentEvents = this.eventQueue.filter(
      e => Date.now() - e.timestamp < 300000 // 5 minutes
    );
    
    const errorEvents = recentEvents.filter(e => e.type.includes('error'));
    return recentEvents.length > 0 ? errorEvents.length / recentEvents.length : 0;
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / memory.jsHeapSizeLimit;
    }
    return 0;
  }

  /**
   * Convert connection type to number for metrics
   */
  private connectionTypeToNumber(type: string): number {
    const types: Record<string, number> = {
      'slow-2g': 1,
      '2g': 2,
      '3g': 3,
      '4g': 4,
      '5g': 5
    };
    return types[type] || 0;
  }

  /**
   * Check if event should be sampled
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.sampleRate;
  }

  /**
   * Flush events to backend
   */
  private async flushEvents(): Promise<void> {
    if (this.eventQueue.length === 0) return;

    const events = [...this.eventQueue];
    this.eventQueue = [];

    try {
      await api.post('/api/analytics/events/', { events });
    } catch (error) {
      console.error('Failed to flush events:', error);
      // Re-add events to queue for retry
      this.eventQueue.unshift(...events);
    }
  }

  /**
   * Flush metrics to backend
   */
  private async flushMetrics(): Promise<void> {
    if (this.metricQueue.length === 0) return;

    const metrics = [...this.metricQueue];
    this.metricQueue = [];

    try {
      await api.post('/api/analytics/metrics/', { metrics });
    } catch (error) {
      console.error('Failed to flush metrics:', error);
      // Re-add metrics to queue for retry
      this.metricQueue.unshift(...metrics);
    }
  }

  /**
   * Start flush timer
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushEvents();
      this.flushMetrics();
    }, this.config.flushInterval);
  }

  /**
   * Set user ID
   */
  setUserId(userId: string): void {
    this.userId = userId;
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<AnalyticsConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get analytics summary
   */
  getAnalyticsSummary(): {
    eventsQueued: number;
    metricsQueued: number;
    sessionId: string;
    config: AnalyticsConfig;
  } {
    return {
      eventsQueued: this.eventQueue.length,
      metricsQueued: this.metricQueue.length,
      sessionId: this.sessionId,
      config: this.config
    };
  }

  /**
   * Cleanup
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }

    // Flush remaining data
    this.flushEvents();
    this.flushMetrics();
  }
}

export const monitoringService = new MonitoringService();
