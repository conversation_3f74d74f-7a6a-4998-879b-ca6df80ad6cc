"""
🎯 USERS URLS
User management API endpoints using consolidated router configuration

CONS<PERSON><PERSON>ATION BENEFITS:
- Uses master router instead of duplicate DefaultRouter
- Eliminates router duplication
- Maintains all existing functionality
- Cleaner, more maintainable structure
"""

from django.urls import path, include
from .views.role_management_views import RoleManagementViewSet
from .language_views import LanguageView
from .views.admin_dashboard import (
    AdminDashboardStatsView,
    AdminSystemHealthView,
    AdminActivityLogsView
)

# ========================================
# CONSOLIDATED ROUTER USAGE
# ========================================
from core.urls.master_router import user_router, initialize_all_routers

# Initialize routers early to ensure they're ready before URL patterns are processed
initialize_all_routers()

# ========================================
# URL PATTERNS
# ========================================
urlpatterns = [
    # Include consolidated user router URLs
    path('', include(user_router.urls)),

    # Function-based view endpoints
    path('language/', LanguageView.as_view(), name='language'),

    # Unified role management endpoints
    path('roles/current/', RoleManagementViewSet.as_view({'get': 'current_user_roles'}), name='current-user-roles'),
    path('roles/available/', RoleManagementViewSet.as_view({'get': 'available_roles'}), name='available-roles'),
    path('roles/groups/', RoleManagementViewSet.as_view({'get': 'role_groups'}), name='role-groups'),
    path('roles/permissions/', RoleManagementViewSet.as_view({'get': 'role_permissions'}), name='role-permissions'),
    path('roles/check/', RoleManagementViewSet.as_view({'post': 'check_role'}), name='check-role'),
    path('roles/clear-cache/', RoleManagementViewSet.as_view({'post': 'clear_cache'}), name='clear-role-cache'),

    # Admin Dashboard API endpoints
    path('admin/dashboard/stats/', AdminDashboardStatsView.as_view(), name='admin-dashboard-stats'),
    path('admin/system/health/', AdminSystemHealthView.as_view(), name='admin-system-health'),
    path('admin/activity/logs/', AdminActivityLogsView.as_view(), name='admin-activity-logs'),

    # NOTE: JWT Token endpoints moved to auth_urls.py to avoid duplication
    # NOTE: Role-specific endpoints consolidated into RoleManagementViewSet
]

# Add router URLs
urlpatterns += user_router.urls
