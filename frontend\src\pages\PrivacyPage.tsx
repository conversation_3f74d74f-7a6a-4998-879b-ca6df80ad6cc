import React from 'react';
import { useLanguage } from '../components/LanguageProvider';
import { ArrowLeft, ArrowRight, Shield, Lock, Eye, Database, UserCheck } from 'lucide-react';
import { Link } from 'react-router-dom';

const PrivacyPage: React.FC = () => {
  const { language } = useLanguage();
  const isRTL = language === 'ar';

  return (
    <div className={`min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="bg-black/20 backdrop-blur-sm border-b border-white/10">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <Link 
              to="/register" 
              className="flex items-center text-white hover:text-blue-300 transition-colors"
            >
              {isRTL ? <ArrowRight className="w-5 h-5 ml-2" /> : <ArrowLeft className="w-5 h-5 mr-2" />}
              {language === 'ar' ? 'العودة للتسجيل' : 'Back to Registration'}
            </Link>
            <div className="flex items-center">
              <Shield className="w-6 h-6 text-green-400 mr-2" />
              <h1 className="text-xl font-bold text-white">
                {language === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy'}
              </h1>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-8">
          
          {/* Introduction */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <Lock className="w-8 h-8 text-green-400 mr-3" />
              <h2 className="text-2xl font-bold text-white">
                {language === 'ar' ? 'التزامنا بخصوصيتك' : 'Our Commitment to Your Privacy'}
              </h2>
            </div>
            <p className="text-gray-300 leading-relaxed">
              {language === 'ar' 
                ? 'في ياسمين AI، نحن ملتزمون بحماية خصوصيتك وأمان بياناتك الشخصية. هذه السياسة توضح كيفية جمع واستخدام وحماية معلوماتك.'
                : 'At Yasmeen AI, we are committed to protecting your privacy and the security of your personal data. This policy explains how we collect, use, and protect your information.'
              }
            </p>
          </div>

          {/* Privacy Sections */}
          <div className="space-y-8">
            
            {/* Section 1: Information We Collect */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Database className="w-6 h-6 text-blue-400 mr-3" />
                {language === 'ar' ? '1. المعلومات التي نجمعها' : '1. Information We Collect'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'نجمع المعلومات التالية لتوفير خدماتنا وتحسين تجربتك:'
                    : 'We collect the following information to provide our services and improve your experience:'
                  }
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>
                    {language === 'ar'
                      ? 'معلومات الحساب: الاسم، البريد الإلكتروني، اسم المستخدم'
                      : 'Account Information: Name, email, username'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'معلومات الملف الشخصي: السيرة الذاتية، الخبرات، المهارات'
                      : 'Profile Information: Bio, experience, skills'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'معلومات الاتصال: رقم الهاتف، الموقع (اختياري)'
                      : 'Contact Information: Phone number, location (optional)'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'معلومات الاستخدام: كيفية تفاعلك مع المنصة'
                      : 'Usage Information: How you interact with the platform'
                    }
                  </li>
                </ul>
              </div>
            </section>

            {/* Section 2: How We Use Your Information */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <UserCheck className="w-6 h-6 text-purple-400 mr-3" />
                {language === 'ar' ? '2. كيف نستخدم معلوماتك' : '2. How We Use Your Information'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'نستخدم معلوماتك للأغراض التالية:'
                    : 'We use your information for the following purposes:'
                  }
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>
                    {language === 'ar'
                      ? 'توفير وتحسين خدمات المنصة'
                      : 'Providing and improving platform services'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'ربطك مع المرشدين والمستثمرين المناسبين'
                      : 'Connecting you with suitable mentors and investors'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'إرسال إشعارات مهمة حول حسابك'
                      : 'Sending important notifications about your account'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'تحليل الاستخدام لتحسين التجربة'
                      : 'Analyzing usage to improve the experience'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'ضمان أمان المنصة ومنع الاحتيال'
                      : 'Ensuring platform security and preventing fraud'
                    }
                  </li>
                </ul>
              </div>
            </section>

            {/* Section 3: Information Sharing */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
                <Eye className="w-6 h-6 text-yellow-400 mr-3" />
                {language === 'ar' ? '3. مشاركة المعلومات' : '3. Information Sharing'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'نحن لا نبيع أو نؤجر معلوماتك الشخصية لأطراف ثالثة. قد نشارك معلومات محدودة في الحالات التالية:'
                    : 'We do not sell or rent your personal information to third parties. We may share limited information in the following cases:'
                  }
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>
                    {language === 'ar'
                      ? 'مع موافقتك الصريحة'
                      : 'With your explicit consent'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'لتوفير الخدمات المطلوبة (مثل ربطك بالمرشدين)'
                      : 'To provide requested services (such as connecting you with mentors)'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'للامتثال للقوانين واللوائح'
                      : 'To comply with laws and regulations'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'لحماية حقوقنا وأمان المستخدمين'
                      : 'To protect our rights and user safety'
                    }
                  </li>
                </ul>
              </div>
            </section>

            {/* Section 4: Data Security */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4">
                {language === 'ar' ? '4. أمان البيانات' : '4. Data Security'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'نتخذ تدابير أمنية قوية لحماية معلوماتك:'
                    : 'We implement strong security measures to protect your information:'
                  }
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>
                    {language === 'ar'
                      ? 'تشفير البيانات أثناء النقل والتخزين'
                      : 'Data encryption during transmission and storage'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'مراقبة أمنية مستمرة للأنظمة'
                      : 'Continuous security monitoring of systems'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'وصول محدود للبيانات الشخصية'
                      : 'Limited access to personal data'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'نسخ احتياطية آمنة ومنتظمة'
                      : 'Secure and regular data backups'
                    }
                  </li>
                </ul>
              </div>
            </section>

            {/* Section 5: Your Rights */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4">
                {language === 'ar' ? '5. حقوقك' : '5. Your Rights'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'لديك الحقوق التالية فيما يتعلق ببياناتك الشخصية:'
                    : 'You have the following rights regarding your personal data:'
                  }
                </p>
                <ul className="list-disc list-inside space-y-2 ml-4">
                  <li>
                    {language === 'ar'
                      ? 'الوصول إلى بياناتك الشخصية'
                      : 'Access to your personal data'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'تصحيح المعلومات غير الصحيحة'
                      : 'Correction of inaccurate information'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'حذف بياناتك (الحق في النسيان)'
                      : 'Deletion of your data (right to be forgotten)'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'تقييد معالجة بياناتك'
                      : 'Restriction of data processing'
                    }
                  </li>
                  <li>
                    {language === 'ar'
                      ? 'نقل بياناتك إلى خدمة أخرى'
                      : 'Data portability to another service'
                    }
                  </li>
                </ul>
              </div>
            </section>

            {/* Section 6: Cookies and Tracking */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4">
                {language === 'ar' ? '6. ملفات تعريف الارتباط والتتبع' : '6. Cookies and Tracking'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'نستخدم ملفات تعريف الارتباط وتقنيات مشابهة لتحسين تجربتك وتحليل استخدام المنصة. يمكنك التحكم في هذه الإعدادات من خلال متصفحك.'
                    : 'We use cookies and similar technologies to improve your experience and analyze platform usage. You can control these settings through your browser.'
                  }
                </p>
              </div>
            </section>

            {/* Section 7: Contact Us */}
            <section>
              <h3 className="text-xl font-semibold text-white mb-4">
                {language === 'ar' ? '7. اتصل بنا' : '7. Contact Us'}
              </h3>
              <div className="space-y-4 text-gray-300">
                <p>
                  {language === 'ar'
                    ? 'إذا كان لديك أي أسئلة حول سياسة الخصوصية هذه أو ممارسات البيانات لدينا، يرجى الاتصال بنا:'
                    : 'If you have any questions about this Privacy Policy or our data practices, please contact us:'
                  }
                </p>
                <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                  <p className="text-white font-medium mb-2">
                    {language === 'ar' ? 'معلومات الاتصال:' : 'Contact Information:'}
                  </p>
                  <p>Email: <EMAIL></p>
                  <p>
                    {language === 'ar' 
                      ? 'العنوان: [عنوان الشركة]'
                      : 'Address: [Company Address]'
                    }
                  </p>
                </div>
              </div>
            </section>

          </div>

          {/* Footer */}
          <div className="mt-12 pt-8 border-t border-white/20">
            <div className="text-center">
              <p className="text-gray-400 text-sm mb-4">
                {language === 'ar'
                  ? `آخر تحديث: ${new Date().toLocaleDateString('ar-SA', { year: 'numeric', month: 'long' })}`
                  : `Last updated: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long' })}`
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link 
                  to="/terms" 
                  className="inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors"
                >
                  {language === 'ar' ? 'اقرأ الشروط والأحكام' : 'Read Terms & Conditions'}
                </Link>
                <Link 
                  to="/register" 
                  className="inline-flex items-center bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg transition-colors"
                >
                  {language === 'ar' ? 'العودة للتسجيل' : 'Back to Registration'}
                  {isRTL ? <ArrowLeft className="w-4 h-4 mr-2" /> : <ArrowRight className="w-4 h-4 ml-2" />}
                </Link>
              </div>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default PrivacyPage;
