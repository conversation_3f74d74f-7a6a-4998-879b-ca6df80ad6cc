import React from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useUnifiedRoles } from '../../hooks/useUnifiedRoles';
import { useAppSelector } from '../../store/hooks';

/**
 * Debug panel to show current role and authentication status
 */
export const RoleDebugPanel: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const authState = useAppSelector(state => state.auth);
  const {
    primaryRole,
    capabilities,
    navigation,
    isAuthenticated: roleAuth,
    canAccess
  } = useUnifiedRoles();

  return (
    <div className="fixed bottom-4 right-4 bg-black/90 text-white p-4 rounded-lg shadow-lg z-50 max-w-sm text-xs">
      <h3 className="font-bold text-sm mb-2">🔍 Role Debug Panel</h3>
      
      <div className="space-y-1">
        <div>
          <strong>Auth Status:</strong> {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
        </div>
        
        <div>
          <strong>Role Auth:</strong> {roleAuth ? '✅ Role Auth' : '❌ No Role Auth'}
        </div>
        
        <div>
          <strong>User:</strong> {user?.username || 'None'}
        </div>
        
        <div>
          <strong>User ID:</strong> {user?.id || 'None'}
        </div>
        
        <div>
          <strong>Primary Role:</strong> {primaryRole || 'None'}
        </div>
        
        <div>
          <strong>User Role Field:</strong> {user?.user_role || 'None'}
        </div>
        
        <div>
          <strong>Auth State:</strong>
          <div className="ml-2 text-xs">
            <div>Loading: {authState.isLoading ? 'Yes' : 'No'}</div>
            <div>Error: {authState.error || 'None'}</div>
            <div>Token: {authState.token ? 'Present' : 'None'}</div>
          </div>
        </div>
        
        <div>
          <strong>Key Capabilities:</strong>
          <div className="ml-2">
            {['canAccessDashboard', 'canManageUsers', 'canAccessSystemSettings'].map(cap => (
              <div key={cap} className={canAccess(cap as any) ? 'text-green-400' : 'text-red-400'}>
                {canAccess(cap as any) ? '✅' : '❌'} {cap}
              </div>
            ))}
          </div>
        </div>
        
        <div>
          <strong>Navigation Items:</strong> {navigation?.length || 0}
        </div>
        
        <div>
          <strong>Current URL:</strong> {window.location.pathname}
        </div>
      </div>
    </div>
  );
};

export default RoleDebugPanel;
