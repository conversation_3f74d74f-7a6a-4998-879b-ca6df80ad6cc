"""
AI Analytics Service
Advanced tracking and analysis for AI conversations
"""

import logging
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from django.utils import timezone
from django.db.models import Count, Avg, Sum, Q
from django.contrib.auth.models import User

from ..models import (
    AIConversationSession, AIMessage, AITopicAnalysis,
    AIUsageStatistics, AIErrorLog
)

logger = logging.getLogger(__name__)


class AIAnalyticsService:
    """Service for tracking and analyzing AI usage"""
    
    def __init__(self):
        self.logger = logger
    
    # ========================================
    # SESSION TRACKING
    # ========================================
    
    def start_session(self, user: Optional[User] = None, session_type: str = 'general', 
                     user_region: str = 'damascus_dialect', **kwargs) -> str:
        """Start a new AI conversation session"""
        try:
            session_id = f"session_{uuid.uuid4().hex[:12]}"
            
            session = AIConversationSession.objects.create(
                session_id=session_id,
                user=user,
                session_type=session_type,
                user_region=user_region,
                model_used=kwargs.get('model_used', 'gemini-2.0-flash'),
                langgraph_enabled=kwargs.get('langgraph_enabled', True),
                authenticity_level=kwargs.get('authenticity_level', 'street_level')
            )
            
            self.logger.info(f"✅ Started AI session: {session_id}")
            return session_id
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start session: {e}")
            return f"session_{uuid.uuid4().hex[:12]}"  # Fallback
    
    def end_session(self, session_id: str, user_satisfaction: Optional[int] = None):
        """End an AI conversation session"""
        try:
            session = AIConversationSession.objects.get(session_id=session_id)
            session.ended_at = timezone.now()
            
            if session.started_at:
                duration = (session.ended_at - session.started_at).total_seconds()
                session.duration_seconds = int(duration)
            
            if user_satisfaction:
                session.user_satisfaction = user_satisfaction
            
            # Calculate session metrics
            messages = session.messages.all()
            session.total_messages = messages.count()
            session.total_tokens_used = messages.aggregate(
                total=Sum('tokens_input') + Sum('tokens_output')
            )['total'] or 0
            session.total_cost_usd = messages.aggregate(
                total=Sum('cost_usd')
            )['total'] or 0
            session.average_response_time = messages.filter(
                message_type='ai'
            ).aggregate(avg=Avg('processing_time_ms'))['avg'] or 0
            
            session.save()
            self.logger.info(f"✅ Ended AI session: {session_id}")
            
        except AIConversationSession.DoesNotExist:
            self.logger.warning(f"⚠️ Session not found: {session_id}")
        except Exception as e:
            self.logger.error(f"❌ Failed to end session: {e}")
    
    # ========================================
    # MESSAGE TRACKING
    # ========================================
    
    def track_message(self, session_id: str, message_data: Dict[str, Any]) -> str:
        """Track an AI message or user input"""
        try:
            session = AIConversationSession.objects.get(session_id=session_id)
            message_id = f"msg_{uuid.uuid4().hex[:12]}"
            
            message = AIMessage.objects.create(
                session=session,
                message_id=message_id,
                message_type=message_data.get('message_type', 'user'),
                content=message_data.get('content', ''),
                language_detected=message_data.get('language_detected', 'ar'),
                tokens_input=message_data.get('tokens_input', 0),
                tokens_output=message_data.get('tokens_output', 0),
                processing_time_ms=message_data.get('processing_time_ms', 0),
                cost_usd=message_data.get('cost_usd', 0),
                workflow_state=message_data.get('workflow_state', ''),
                nodes_executed=message_data.get('nodes_executed', []),
                analysis_results=message_data.get('analysis_results', {})
            )
            
            # Auto-analyze topic for user messages
            if message_data.get('message_type') == 'user':
                self._analyze_message_topic(message)
            
            self.logger.info(f"✅ Tracked message: {message_id}")
            return message_id
            
        except AIConversationSession.DoesNotExist:
            self.logger.warning(f"⚠️ Session not found for message tracking: {session_id}")
            return f"msg_{uuid.uuid4().hex[:12]}"
        except Exception as e:
            self.logger.error(f"❌ Failed to track message: {e}")
            return f"msg_{uuid.uuid4().hex[:12]}"
    
    def _analyze_message_topic(self, message: AIMessage):
        """Analyze message topic using AI classification"""
        try:
            content = message.content.lower()
            
            # Simple keyword-based classification
            topic_mapping = {
                'business_planning': ['خطة', 'مشروع', 'عمل', 'business', 'plan', 'startup'],
                'investment': ['استثمار', 'تمويل', 'رأس مال', 'investment', 'funding'],
                'market_analysis': ['سوق', 'تحليل', 'منافسة', 'market', 'analysis'],
                'damascus_business': ['دمشق', 'الشام', 'damascus', 'سوق الحميدية'],
                'aleppo_business': ['حلب', 'aleppo', 'الشهباء'],
                'technology': ['تقنية', 'تكنولوجيا', 'technology', 'ai', 'ذكاء اصطناعي'],
            }
            
            detected_topics = []
            keywords = []
            
            for topic, topic_keywords in topic_mapping.items():
                for keyword in topic_keywords:
                    if keyword in content:
                        detected_topics.append(topic)
                        keywords.append(keyword)
                        break
            
            # Create topic analysis
            for topic in detected_topics:
                AITopicAnalysis.objects.create(
                    session=message.session,
                    message=message,
                    topic_category=topic,
                    topic_keywords=keywords,
                    topic_confidence=0.8,  # Simple confidence score
                    business_intent='business' in topic or 'investment' in topic,
                    investment_related='investment' in topic,
                    regional_context=message.session.user_region,
                    analysis_method='keyword_matching'
                )
            
            # Default to general_chat if no topics detected
            if not detected_topics:
                AITopicAnalysis.objects.create(
                    session=message.session,
                    message=message,
                    topic_category='general_chat',
                    topic_keywords=[],
                    topic_confidence=0.5,
                    analysis_method='default_classification'
                )
                
        except Exception as e:
            self.logger.error(f"❌ Failed to analyze topic: {e}")
    
    # ========================================
    # ERROR TRACKING
    # ========================================
    
    def log_error(self, error_data: Dict[str, Any]):
        """Log an AI error for debugging"""
        try:
            # Get session object if session_id is provided
            session = None
            session_id = error_data.get('session_id')
            if session_id:
                try:
                    session = AIConversationSession.objects.get(session_id=session_id)
                except AIConversationSession.DoesNotExist:
                    self.logger.warning(f"⚠️ Session not found for error logging: {session_id}")

            AIErrorLog.objects.create(
                session=session,  # Use session object, not session_id
                error_type=error_data.get('error_type', 'other'),
                error_message=error_data.get('error_message', ''),
                error_details=error_data.get('error_details', {}),
                stack_trace=error_data.get('stack_trace', ''),
                user_input=error_data.get('user_input', ''),
                model_used=error_data.get('model_used', ''),
                request_data=error_data.get('request_data', {})
            )
            
            self.logger.info(f"✅ Logged AI error: {error_data.get('error_type')}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to log error: {e}")
    
    # ========================================
    # ANALYTICS & REPORTING
    # ========================================
    
    def get_usage_stats(self, period: str = 'daily', days: int = 7) -> Dict[str, Any]:
        """Get usage statistics for admin dashboard"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            sessions = AIConversationSession.objects.filter(
                started_at__gte=start_date,
                started_at__lte=end_date
            )
            
            messages = AIMessage.objects.filter(
                created_at__gte=start_date,
                created_at__lte=end_date
            )
            
            topics = AITopicAnalysis.objects.filter(
                analyzed_at__gte=start_date,
                analyzed_at__lte=end_date
            )
            
            stats = {
                'period': f"{period} - Last {days} days",
                'total_sessions': sessions.count(),
                'total_messages': messages.count(),
                'total_users': sessions.values('user').distinct().count(),
                'total_tokens': messages.aggregate(
                    total=Sum('tokens_input') + Sum('tokens_output')
                )['total'] or 0,
                'total_cost_usd': float(messages.aggregate(
                    total=Sum('cost_usd')
                )['total'] or 0),
                'average_response_time': messages.filter(
                    message_type='ai'
                ).aggregate(avg=Avg('processing_time_ms'))['avg'] or 0,
                
                # Regional breakdown
                'regional_stats': {
                    'damascus': sessions.filter(user_region='damascus_dialect').count(),
                    'aleppo': sessions.filter(user_region='aleppo_dialect').count(),
                    'homs': sessions.filter(user_region='homs_dialect').count(),
                    'latakia': sessions.filter(user_region='latakia_dialect').count(),
                },
                
                # Topic breakdown
                'topic_stats': dict(topics.values_list('topic_category').annotate(
                    count=Count('topic_category')
                )),
                
                # Session type breakdown
                'session_type_stats': dict(sessions.values_list('session_type').annotate(
                    count=Count('session_type')
                )),
                
                # Performance metrics
                'performance': {
                    'avg_session_duration': sessions.aggregate(
                        avg=Avg('duration_seconds')
                    )['avg'] or 0,
                    'avg_messages_per_session': sessions.aggregate(
                        avg=Avg('total_messages')
                    )['avg'] or 0,
                    'langgraph_usage': sessions.filter(
                        langgraph_enabled=True
                    ).count(),
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get usage stats: {e}")
            return {}
    
    def get_top_topics(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get most popular conversation topics"""
        try:
            topics = AITopicAnalysis.objects.values(
                'topic_category'
            ).annotate(
                count=Count('id'),
                avg_confidence=Avg('topic_confidence')
            ).order_by('-count')[:limit]
            
            return list(topics)
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get top topics: {e}")
            return []
    
    def get_user_analytics(self, user_id: int) -> Dict[str, Any]:
        """Get analytics for a specific user"""
        try:
            sessions = AIConversationSession.objects.filter(user_id=user_id)
            messages = AIMessage.objects.filter(session__user_id=user_id)
            
            analytics = {
                'total_sessions': sessions.count(),
                'total_messages': messages.count(),
                'total_tokens_used': messages.aggregate(
                    total=Sum('tokens_input') + Sum('tokens_output')
                )['total'] or 0,
                'total_cost': float(messages.aggregate(
                    total=Sum('cost_usd')
                )['total'] or 0),
                'favorite_region': sessions.values('user_region').annotate(
                    count=Count('user_region')
                ).order_by('-count').first(),
                'favorite_topics': list(AITopicAnalysis.objects.filter(
                    session__user_id=user_id
                ).values('topic_category').annotate(
                    count=Count('topic_category')
                ).order_by('-count')[:5]),
                'avg_satisfaction': sessions.aggregate(
                    avg=Avg('user_satisfaction')
                )['avg'] or 0,
            }
            
            return analytics
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get user analytics: {e}")
            return {}


# Global service instance
ai_analytics_service = AIAnalyticsService()
