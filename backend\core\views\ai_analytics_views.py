"""
AI Analytics API Views
Advanced monitoring and analytics for AI usage
"""

import logging
from datetime import datetime, timedelta
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.response import Response
from django.db.models import Count, Avg, Sum, Q
from django.core.paginator import Paginator
import json

from ..models import (
    AIConversationSession, AIMessage, AITopicAnalysis,
    AIUsageStatistics, AIErrorLog
)
from ..services.ai_analytics_service import ai_analytics_service

logger = logging.getLogger(__name__)


# ========================================
# ADMIN DASHBOARD ANALYTICS
# ========================================

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def admin_dashboard_stats(request):
    """Get comprehensive dashboard statistics for admin"""
    try:
        # Get query parameters
        days = int(request.GET.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        # Calculate basic stats
        total_sessions = AIConversationSession.objects.filter(
            started_at__gte=start_date
        ).count()

        total_messages = AIMessage.objects.filter(
            created_at__gte=start_date
        ).count()

        unique_users = AIConversationSession.objects.filter(
            started_at__gte=start_date,
            user__isnull=False
        ).values('user').distinct().count()

        # Token and cost stats
        token_stats = AIMessage.objects.filter(
            created_at__gte=start_date
        ).aggregate(
            total_tokens=Sum('tokens_input') + Sum('tokens_output'),
            total_cost=Sum('cost_usd'),
            avg_response_time=Avg('processing_time_ms')
        )

        total_tokens = (token_stats['total_tokens'] or 0)
        total_cost_usd = float(token_stats['total_cost'] or 0)
        average_response_time = float(token_stats['avg_response_time'] or 0)

        # Regional breakdown
        regional_stats = AIConversationSession.objects.filter(
            started_at__gte=start_date
        ).values('user_region').annotate(count=Count('id'))

        regional_breakdown = {
            'damascus': 0, 'aleppo': 0, 'homs': 0, 'latakia': 0
        }
        for stat in regional_stats:
            region = stat['user_region'].replace('_dialect', '')
            if region in regional_breakdown:
                regional_breakdown[region] = stat['count']

        # Topic breakdown
        topic_stats = AIConversationSession.objects.filter(
            started_at__gte=start_date
        ).values('session_type').annotate(count=Count('id'))

        topic_breakdown = {
            'business': 0, 'general': 0, 'investment': 0, 'other': 0
        }
        for stat in topic_stats:
            session_type = stat['session_type']
            if session_type in ['business', 'syrian_business']:
                topic_breakdown['business'] += stat['count']
            elif session_type == 'general':
                topic_breakdown['general'] = stat['count']
            elif session_type in ['recommendation', 'idea_builder']:
                topic_breakdown['investment'] += stat['count']
            else:
                topic_breakdown['other'] += stat['count']

        # Active sessions and errors
        active_sessions = AIConversationSession.objects.filter(
            ended_at__isnull=True,
            started_at__gte=timezone.now() - timedelta(hours=1)
        ).count()

        recent_errors = AIErrorLog.objects.filter(
            occurred_at__gte=timezone.now() - timedelta(days=1),
            resolved=False
        ).count()

        # Success rate calculation
        total_requests = AIMessage.objects.filter(
            created_at__gte=start_date,
            message_type='ai'
        ).count()

        error_count = AIErrorLog.objects.filter(
            occurred_at__gte=start_date
        ).count()

        success_rate = ((total_requests - error_count) / max(total_requests, 1)) * 100

        # Daily usage data
        daily_usage = []
        for i in range(days):
            day_start = start_date + timedelta(days=i)
            day_end = day_start + timedelta(days=1)

            day_sessions = AIConversationSession.objects.filter(
                started_at__gte=day_start,
                started_at__lt=day_end
            ).count()

            day_messages = AIMessage.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).count()

            day_tokens = AIMessage.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).aggregate(
                input_sum=Sum('tokens_input'),
                output_sum=Sum('tokens_output')
            )
            total_day_tokens = (day_tokens['input_sum'] or 0) + (day_tokens['output_sum'] or 0)

            day_cost = AIMessage.objects.filter(
                created_at__gte=day_start,
                created_at__lt=day_end
            ).aggregate(total=Sum('cost_usd'))['total'] or 0.0

            daily_usage.append({
                'date': day_start.strftime('%Y-%m-%d'),
                'sessions': day_sessions,
                'messages': day_messages,
                'tokens': total_day_tokens,
                'cost': float(day_cost)
            })

        # Return data in format expected by frontend
        response_data = {
            'total_sessions': total_sessions,
            'total_messages': total_messages,
            'unique_users': unique_users,
            'total_tokens': total_tokens,
            'total_cost_usd': total_cost_usd,
            'average_response_time': average_response_time,
            'active_sessions': active_sessions,
            'recent_errors': recent_errors,
            'success_rate': success_rate,
            'regional_stats': regional_breakdown,
            'topic_stats': topic_breakdown,
            'daily_usage': daily_usage
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"❌ Admin dashboard stats error: {e}")
        return Response({
            'error': 'Failed to get dashboard statistics',
            'details': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def token_usage_analytics(request):
    """Detailed token usage analytics"""
    try:
        # Get query parameters
        days = int(request.GET.get('days', 30))
        group_by = request.GET.get('group_by', 'daily')  # daily, hourly, user
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        messages = AIMessage.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date
        )
        
        # Token usage by time period
        if group_by == 'daily':
            token_usage = messages.extra(
                select={'date': 'DATE(created_at)'}
            ).values('date').annotate(
                total_tokens=Sum('tokens_input') + Sum('tokens_output'),
                total_cost=Sum('cost_usd'),
                message_count=Count('id')
            ).order_by('date')
        
        elif group_by == 'user':
            token_usage = messages.values(
                'session__user__username'
            ).annotate(
                total_tokens=Sum('tokens_input') + Sum('tokens_output'),
                total_cost=Sum('cost_usd'),
                message_count=Count('id')
            ).order_by('-total_tokens')[:20]
        
        else:  # hourly - SQLite compatible
            # Use strftime for SQLite compatibility
            token_usage = messages.extra(
                select={'hour': "strftime('%%Y-%%m-%%d %%H:00:00', created_at)"}
            ).values('hour').annotate(
                total_tokens=Sum('tokens_input') + Sum('tokens_output'),
                total_cost=Sum('cost_usd'),
                message_count=Count('id')
            ).order_by('hour')
        
        # Model usage breakdown
        model_usage = messages.values(
            'session__model_used'
        ).annotate(
            total_tokens=Sum('tokens_input') + Sum('tokens_output'),
            total_cost=Sum('cost_usd'),
            usage_count=Count('id')
        ).order_by('-total_tokens')
        
        # Cost projections
        daily_avg_cost = messages.aggregate(
            avg=Avg('cost_usd')
        )['avg'] or 0
        
        monthly_projection = daily_avg_cost * 30 * messages.filter(
            created_at__date=timezone.now().date()
        ).count()
        
        # Calculate totals
        total_tokens_calc = messages.aggregate(
            input_total=Sum('tokens_input'),
            output_total=Sum('tokens_output'),
            cost_total=Sum('cost_usd')
        )

        total_tokens = (total_tokens_calc['input_total'] or 0) + (total_tokens_calc['output_total'] or 0)
        total_cost = total_tokens_calc['cost_total'] or 0

        # Format model breakdown for frontend
        model_breakdown = []
        for model in model_usage:
            model_breakdown.append({
                'model': model['session__model_used'] or 'unknown',
                'tokens': model['total_tokens'],
                'cost': float(model['total_cost']),
                'percentage': (model['total_tokens'] / total_tokens * 100) if total_tokens > 0 else 0
            })

        # Return data in format expected by frontend
        response_data = {
            'total_tokens': total_tokens,
            'input_tokens': total_tokens_calc['input_total'] or 0,
            'output_tokens': total_tokens_calc['output_total'] or 0,
            'total_cost_usd': float(total_cost),
            'average_tokens_per_message': (total_tokens / messages.count()) if messages.count() > 0 else 0,
            'cost_trend': list(token_usage),
            'model_breakdown': model_breakdown
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"❌ Token analytics error: {e}")
        return Response({
            'error': 'Failed to get token analytics',
            'details': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def conversation_topics_analytics(request):
    """Analyze conversation topics and trends"""
    try:
        # Get query parameters
        days = int(request.GET.get('days', 30))
        region = request.GET.get('region', 'all')
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        topics_query = AITopicAnalysis.objects.filter(
            analyzed_at__gte=start_date,
            analyzed_at__lte=end_date
        )
        
        if region != 'all':
            topics_query = topics_query.filter(regional_context=region)
        
        # Topic distribution
        topic_distribution = topics_query.values(
            'topic_category'
        ).annotate(
            count=Count('id'),
            avg_confidence=Avg('topic_confidence')
        ).order_by('-count')
        
        # Business vs General topics
        business_topics = topics_query.filter(business_intent=True).count()
        general_topics = topics_query.filter(business_intent=False).count()
        
        # Regional topic preferences
        regional_topics = topics_query.values(
            'regional_context', 'topic_category'
        ).annotate(
            count=Count('id')
        ).order_by('regional_context', '-count')
        
        # Topic trends over time
        topic_trends = topics_query.extra(
            select={'date': 'DATE(analyzed_at)'}
        ).values('date', 'topic_category').annotate(
            count=Count('id')
        ).order_by('date')
        
        # Most common keywords
        all_keywords = []
        for topic in topics_query.values_list('topic_keywords', flat=True):
            if topic:
                all_keywords.extend(topic)
        
        keyword_counts = {}
        for keyword in all_keywords:
            keyword_counts[keyword] = keyword_counts.get(keyword, 0) + 1
        
        top_keywords = sorted(keyword_counts.items(), key=lambda x: x[1], reverse=True)[:20]
        
        response_data = {
            'success': True,
            'topic_analytics': {
                'period': f"Last {days} days",
                'region_filter': region,
                'topic_distribution': list(topic_distribution),
                'business_vs_general': {
                    'business_topics': business_topics,
                    'general_topics': general_topics,
                    'business_percentage': (business_topics / (business_topics + general_topics) * 100) if (business_topics + general_topics) > 0 else 0
                },
                'regional_preferences': list(regional_topics),
                'topic_trends': list(topic_trends),
                'top_keywords': top_keywords,
                'summary': {
                    'total_analyzed': topics_query.count(),
                    'unique_topics': topics_query.values('topic_category').distinct().count(),
                    'avg_confidence': topics_query.aggregate(avg=Avg('topic_confidence'))['avg'] or 0,
                }
            }
        }
        
        return Response(response_data)

    except Exception as e:
        logger.error(f"❌ Topic analytics error: {e}")
        return Response({
            'error': 'Failed to get topic analytics',
            'details': str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def user_behavior_analytics(request):
    """Analyze user behavior patterns"""
    try:
        # Get query parameters
        days = int(request.GET.get('days', 30))
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        sessions = AIConversationSession.objects.filter(
            started_at__gte=start_date,
            started_at__lte=end_date
        )
        
        # User engagement metrics
        user_engagement = sessions.values(
            'user__username'
        ).annotate(
            session_count=Count('id'),
            total_messages=Sum('total_messages'),
            avg_duration=Avg('duration_seconds'),
            total_tokens=Sum('total_tokens_used'),
            avg_satisfaction=Avg('user_satisfaction')
        ).order_by('-session_count')[:20]
        
        # Session duration analysis
        duration_ranges = {
            '0-1 min': sessions.filter(duration_seconds__lt=60).count(),
            '1-5 min': sessions.filter(duration_seconds__gte=60, duration_seconds__lt=300).count(),
            '5-15 min': sessions.filter(duration_seconds__gte=300, duration_seconds__lt=900).count(),
            '15+ min': sessions.filter(duration_seconds__gte=900).count(),
        }
        
        # Regional preferences
        regional_usage = sessions.values(
            'user_region'
        ).annotate(
            count=Count('id'),
            avg_duration=Avg('duration_seconds'),
            avg_messages=Avg('total_messages')
        ).order_by('-count')
        
        # Peak usage hours - SQLite compatible
        peak_hours = sessions.extra(
            select={'hour': "CAST(strftime('%%H', started_at) AS INTEGER)"}
        ).values('hour').annotate(
            count=Count('id')
        ).order_by('hour')
        
        # Prepare user activity data for frontend
        user_activity = []
        for user in user_engagement:
            user_activity.append({
                'user_id': user.get('user', 0),
                'username': f"user_{user.get('user', 0)}",
                'total_sessions': user.get('session_count', 0),
                'total_messages': user.get('total_messages', 0),
                'avg_session_duration': user.get('avg_duration', 0),
                'last_active': timezone.now().isoformat(),
                'preferred_topics': ['general'],
                'engagement_score': min(user.get('session_count', 0) * 0.1, 10.0)
            })

        # Prepare session patterns (hour/day patterns)
        session_patterns = []
        for hour_data in peak_hours:
            session_patterns.append({
                'hour': hour_data.get('hour', 0),
                'day_of_week': 1,  # Default to Monday
                'session_count': hour_data.get('count', 0),
                'avg_duration': 300  # Default 5 minutes
            })

        # Calculate engagement metrics
        total_users = sessions.values('user').distinct().count()
        active_users = sessions.filter(
            started_at__gte=timezone.now() - timedelta(days=1)
        ).values('user').distinct().count()

        # Return data in format expected by frontend
        response_data = {
            'user_activity': user_activity,
            'session_patterns': session_patterns,
            'engagement_metrics': {
                'daily_active_users': active_users,
                'weekly_active_users': sessions.filter(
                    started_at__gte=timezone.now() - timedelta(days=7)
                ).values('user').distinct().count(),
                'monthly_active_users': total_users,
                'avg_session_duration': sessions.aggregate(avg=Avg('duration_seconds'))['avg'] or 0,
                'bounce_rate': 0.1,  # Default
                'retention_rate': 0.8  # Default
            },
            'regional_usage': [
                {
                    'region': item.get('user__region', 'unknown'),
                    'user_count': item.get('count', 0),
                    'avg_sessions_per_user': item.get('avg_sessions', 1)
                }
                for item in regional_usage
            ],
            'summary': {
                'total_users': total_users,
                'active_users': active_users,
                'new_users': max(0, total_users - active_users),
                'returning_users': active_users
            }
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"❌ User behavior analytics error: {e}")
        return Response({
            'error': 'Failed to get user behavior analytics',
            'details': str(e)
        }, status=500)


# ========================================
# ERROR MONITORING
# ========================================

@api_view(['GET'])
@permission_classes([IsAuthenticated, IsAdminUser])
def error_monitoring(request):
    """Monitor AI errors and issues"""
    try:
        # Get query parameters
        days = int(request.GET.get('days', 7))
        error_type = request.GET.get('error_type', 'all')
        resolved = request.GET.get('resolved', 'all')
        
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days)
        
        errors_query = AIErrorLog.objects.filter(
            occurred_at__gte=start_date,
            occurred_at__lte=end_date
        )
        
        if error_type != 'all':
            errors_query = errors_query.filter(error_type=error_type)
        
        if resolved == 'true':
            errors_query = errors_query.filter(resolved=True)
        elif resolved == 'false':
            errors_query = errors_query.filter(resolved=False)
        
        # Error distribution
        error_distribution = errors_query.values(
            'error_type'
        ).annotate(
            count=Count('id')
        ).order_by('-count')
        
        # Error timeline
        error_timeline = errors_query.extra(
            select={'date': 'DATE(occurred_at)'}
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')
        
        # Recent errors (paginated)
        page = int(request.GET.get('page', 1))
        recent_errors = errors_query.order_by('-occurred_at')
        paginator = Paginator(recent_errors, 20)
        page_obj = paginator.get_page(page)
        
        errors_list = []
        for error in page_obj:
            errors_list.append({
                'id': error.id,
                'error_type': error.error_type,
                'error_message': error.error_message[:200],
                'occurred_at': error.occurred_at.isoformat(),
                'resolved': error.resolved,
                'session_id': error.session.session_id if error.session else None,
                'model_used': error.model_used,
            })
        
        # Calculate error rate
        total_messages = AIMessage.objects.filter(created_at__gte=start_date).count()
        error_rate = (errors_query.count() / total_messages) if total_messages > 0 else 0

        # Format error types for frontend
        error_types = []
        for error_type_data in error_distribution:
            error_types.append({
                'type': error_type_data['error_type'],
                'count': error_type_data['count'],
                'percentage': (error_type_data['count'] / errors_query.count() * 100) if errors_query.count() > 0 else 0
            })

        # Return data in format expected by frontend
        response_data = {
            'total_errors': errors_query.count(),
            'error_rate': error_rate,
            'error_types': error_types,
            'recent_errors': errors_list,
            'error_trends': list(error_timeline)
        }
        
        return Response(response_data)

    except Exception as e:
        logger.error(f"❌ Error monitoring error: {e}")
        return Response({
            'error': 'Failed to get error monitoring data',
            'details': str(e)
        }, status=500)
