# Generated by Django 5.2.1 on 2025-08-05 14:09

import api.storage
import datetime
import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("api", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="MetricDefinition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "metric_key",
                    models.CharField(
                        help_text="Unique identifier for the metric",
                        max_length=100,
                        unique=True,
                    ),
                ),
                (
                    "display_name",
                    models.CharField(
                        help_text="User-friendly name for the metric", max_length=100
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        help_text="Detailed explanation of what the metric measures"
                    ),
                ),
                (
                    "calculation_method",
                    models.TextField(help_text="How the metric is calculated"),
                ),
                (
                    "low_value_interpretation",
                    models.TextField(help_text="What a low value means"),
                ),
                (
                    "medium_value_interpretation",
                    models.TextField(help_text="What a medium value means"),
                ),
                (
                    "high_value_interpretation",
                    models.TextField(help_text="What a high value means"),
                ),
                (
                    "improvement_suggestions",
                    models.TextField(help_text="Suggestions for improving this metric"),
                ),
                (
                    "visualization_settings",
                    models.JSONField(
                        default=dict, help_text="Settings for visualizing this metric"
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("progress", "Progress Metrics"),
                            ("engagement", "Engagement Metrics"),
                            ("comparative", "Comparative Metrics"),
                            ("predictive", "Predictive Metrics"),
                        ],
                        default="progress",
                        max_length=50,
                    ),
                ),
                (
                    "display_order",
                    models.IntegerField(
                        default=0, help_text="Order in which to display this metric"
                    ),
                ),
            ],
            options={
                "ordering": ["category", "display_order"],
            },
        ),
        migrations.CreateModel(
            name="TemplateSectionDefinition",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                (
                    "key",
                    models.CharField(
                        help_text="Unique identifier for this section type",
                        max_length=50,
                        unique=True,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "section_type",
                    models.CharField(
                        choices=[
                            ("text", "Text"),
                            ("financial", "Financial"),
                            ("chart", "Chart/Graph"),
                            ("table", "Table"),
                            ("image", "Image"),
                            ("swot", "SWOT Analysis"),
                            ("timeline", "Timeline"),
                            ("checklist", "Checklist"),
                            ("canvas", "Business Model Canvas"),
                            ("persona", "Customer Persona"),
                            ("journey_map", "Customer Journey Map"),
                            ("competitive_matrix", "Competitive Analysis Matrix"),
                            ("risk_matrix", "Risk Assessment Matrix"),
                            ("financial_forecast", "Financial Forecast"),
                            ("revenue_model", "Revenue Model"),
                            ("pricing_strategy", "Pricing Strategy"),
                            ("marketing_mix", "Marketing Mix (4Ps)"),
                            ("value_proposition", "Value Proposition Canvas"),
                            ("lean_canvas", "Lean Canvas"),
                            ("pitch_deck", "Pitch Deck Slide"),
                            ("milestone_tracker", "Milestone Tracker"),
                            ("team_structure", "Team Structure"),
                            ("technology_stack", "Technology Stack"),
                            ("supply_chain", "Supply Chain Diagram"),
                            ("process_flow", "Process Flow"),
                            ("market_sizing", "Market Sizing"),
                            ("user_story", "User Stories"),
                            ("feature_roadmap", "Feature Roadmap"),
                            ("kpi_dashboard", "KPI Dashboard"),
                            ("custom", "Custom"),
                        ],
                        default="text",
                        max_length=20,
                    ),
                ),
                ("default_content", models.TextField(blank=True)),
                (
                    "structure_definition",
                    models.JSONField(
                        default=dict,
                        help_text="JSON schema defining the structure of this section type",
                    ),
                ),
                (
                    "ai_prompt_template",
                    models.TextField(
                        blank=True,
                        help_text="Template for AI prompts to generate this section",
                    ),
                ),
                ("is_system", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "ordering": ["title"],
            },
        ),
        migrations.CreateModel(
            name="BusinessIdea",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("slug", models.SlugField(blank=True, max_length=250, unique=True)),
                ("description", models.TextField()),
                (
                    "problem_statement",
                    models.TextField(
                        help_text="What problem does your business idea solve?"
                    ),
                ),
                (
                    "solution_description",
                    models.TextField(help_text="How does your idea solve the problem?"),
                ),
                (
                    "target_audience",
                    models.TextField(help_text="Who is your target audience?"),
                ),
                (
                    "market_opportunity",
                    models.TextField(
                        blank=True,
                        help_text="What is the market opportunity?",
                        null=True,
                    ),
                ),
                (
                    "business_model",
                    models.TextField(
                        blank=True,
                        help_text="How will your business make money?",
                        null=True,
                    ),
                ),
                (
                    "current_stage",
                    models.CharField(
                        choices=[
                            ("concept", "Concept Stage"),
                            ("validation", "Validation Stage"),
                            ("development", "Development Stage"),
                            ("scaling", "Scaling Stage"),
                            ("established", "Established Business"),
                        ],
                        default="concept",
                        max_length=20,
                    ),
                ),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="business_idea_images/",
                    ),
                ),
                (
                    "moderation_status",
                    models.CharField(
                        choices=[
                            ("approved", "Approved"),
                            ("pending", "Pending Review"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("moderation_comment", models.TextField(blank=True, null=True)),
                ("moderated_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "collaborators",
                    models.ManyToManyField(
                        blank=True,
                        related_name="collaborated_ideas",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "moderated_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="moderated_ideas",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_ideas",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, related_name="business_ideas", to="api.tag"
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="BusinessGoal",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "timeframe",
                    models.CharField(
                        choices=[
                            ("short_term", "Short Term (0-3 months)"),
                            ("medium_term", "Medium Term (3-12 months)"),
                            ("long_term", "Long Term (1+ years)"),
                        ],
                        default="medium_term",
                        max_length=20,
                    ),
                ),
                ("target_date", models.DateField(blank=True, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("achieved", "Achieved"),
                            ("revised", "Revised"),
                            ("abandoned", "Abandoned"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("achievement_date", models.DateField(blank=True, null=True)),
                ("achievement_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_goals",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="goals",
                        to="incubator.businessidea",
                    ),
                ),
            ],
            options={
                "ordering": ["target_date", "timeframe"],
            },
        ),
        migrations.CreateModel(
            name="BusinessAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "progress_rate",
                    models.FloatField(
                        default=0.0, help_text="Progress rate (updates per month)"
                    ),
                ),
                (
                    "milestone_completion_rate",
                    models.FloatField(
                        default=0.0,
                        help_text="Percentage of milestones completed on time",
                    ),
                ),
                (
                    "goal_achievement_rate",
                    models.FloatField(
                        default=0.0, help_text="Percentage of goals achieved"
                    ),
                ),
                (
                    "team_size",
                    models.IntegerField(
                        default=1,
                        help_text="Number of team members (owner + collaborators)",
                    ),
                ),
                (
                    "mentor_engagement",
                    models.FloatField(
                        default=0.0, help_text="Mentor engagement score (0-100)"
                    ),
                ),
                (
                    "industry_percentile",
                    models.FloatField(
                        default=0.0, help_text="Percentile rank within same industry"
                    ),
                ),
                (
                    "stage_percentile",
                    models.FloatField(
                        default=0.0, help_text="Percentile rank within same stage"
                    ),
                ),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="incubator.businessidea",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="BusinessMilestone",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                ("due_date", models.DateField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("not_started", "Not Started"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("delayed", "Delayed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="not_started",
                        max_length=20,
                    ),
                ),
                (
                    "priority",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                            ("critical", "Critical"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                ("completion_date", models.DateField(blank=True, null=True)),
                ("completion_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "assigned_to",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="assigned_milestones",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="milestones",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="created_milestones",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["due_date", "-priority"],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlan",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("in_progress", "In Progress"),
                            ("completed", "Completed"),
                            ("archived", "Archived"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                (
                    "content",
                    models.JSONField(
                        default=dict,
                        help_text="JSON structure containing the business plan content",
                    ),
                ),
                (
                    "ai_feedback",
                    models.JSONField(
                        default=dict,
                        help_text="AI-generated feedback on the business plan",
                    ),
                ),
                (
                    "completion_percentage",
                    models.PositiveSmallIntegerField(
                        default=0,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("version", models.PositiveSmallIntegerField(default=1)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_plans",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="business_plans",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-updated_at"],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_time_spent", models.DurationField(default=datetime.timedelta)),
                (
                    "average_session_duration",
                    models.DurationField(default=datetime.timedelta),
                ),
                ("total_sessions", models.IntegerField(default=0)),
                ("total_collaborators", models.IntegerField(default=0)),
                ("total_comments", models.IntegerField(default=0)),
                ("total_edits", models.IntegerField(default=0)),
                ("total_reviews", models.IntegerField(default=0)),
                ("total_exports", models.IntegerField(default=0)),
                ("pdf_exports", models.IntegerField(default=0)),
                ("word_exports", models.IntegerField(default=0)),
                ("excel_exports", models.IntegerField(default=0)),
                ("completion_rate", models.FloatField(default=0.0)),
                ("sections_completed", models.IntegerField(default=0)),
                ("total_sections", models.IntegerField(default=0)),
                ("ai_assistance_sessions", models.IntegerField(default=0)),
                ("ai_content_generated", models.IntegerField(default=0)),
                ("word_count", models.IntegerField(default=0)),
                ("character_count", models.IntegerField(default=0)),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                (
                    "business_plan",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics",
                        to="incubator.businessplan",
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_analytics",
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                ("industry", models.CharField(max_length=100)),
                (
                    "template_type",
                    models.CharField(
                        choices=[
                            ("standard", "Standard"),
                            ("lean", "Lean Canvas"),
                            ("detailed", "Detailed"),
                            ("investor", "Investor-Ready"),
                            ("startup", "Startup"),
                            ("nonprofit", "Non-Profit"),
                            ("service", "Service Business"),
                            ("product", "Product Business"),
                            ("ecommerce", "E-commerce"),
                            ("saas", "SaaS/Software"),
                            ("restaurant", "Restaurant/Food Service"),
                            ("retail", "Retail"),
                            ("consulting", "Consulting"),
                            ("franchise", "Franchise"),
                            ("manufacturing", "Manufacturing"),
                            ("healthcare", "Healthcare"),
                            ("education", "Education"),
                            ("real_estate", "Real Estate"),
                            ("fintech", "FinTech"),
                            ("marketplace", "Marketplace"),
                            ("subscription", "Subscription Business"),
                            ("mobile_app", "Mobile App"),
                            ("social_impact", "Social Impact"),
                            ("green_business", "Green/Sustainable Business"),
                            ("ai_startup", "AI/Tech Startup"),
                            ("blockchain", "Blockchain/Crypto"),
                            ("gaming", "Gaming/Entertainment"),
                            ("fitness", "Fitness & Wellness"),
                            ("food_truck", "Food Truck"),
                            ("beauty_salon", "Beauty Salon"),
                            ("pet_services", "Pet Services"),
                            ("automotive", "Automotive"),
                            ("photography", "Photography"),
                            ("event_planning", "Event Planning"),
                            ("cleaning_services", "Cleaning Services"),
                            ("landscaping", "Landscaping"),
                            ("home_services", "Home Services"),
                            ("digital_marketing", "Digital Marketing"),
                            ("dropshipping", "Dropshipping"),
                            ("affiliate_marketing", "Affiliate Marketing"),
                            ("coaching", "Coaching"),
                            ("tutoring", "Tutoring"),
                            ("travel_tourism", "Travel & Tourism"),
                            ("import_export", "Import/Export"),
                            ("logistics", "Logistics"),
                            ("security_services", "Security Services"),
                            ("legal_services", "Legal Services"),
                            ("accounting_services", "Accounting Services"),
                            ("insurance_services", "Insurance Services"),
                            ("international", "International Expansion"),
                            ("acquisition", "Business Acquisition"),
                            ("pivot", "Business Pivot"),
                            ("custom", "Custom"),
                        ],
                        default="standard",
                        max_length=20,
                    ),
                ),
                (
                    "sections",
                    models.JSONField(
                        help_text="JSON structure defining the sections of this template"
                    ),
                ),
                ("allows_customization", models.BooleanField(default=True)),
                (
                    "customization_options",
                    models.JSONField(
                        default=dict,
                        help_text="JSON structure defining customization options",
                    ),
                ),
                (
                    "difficulty_level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                (
                    "estimated_time",
                    models.PositiveIntegerField(
                        default=8, help_text="Estimated completion time in hours"
                    ),
                ),
                (
                    "usage_count",
                    models.PositiveIntegerField(
                        default=0,
                        help_text="Total number of times this template has been used",
                    ),
                ),
                (
                    "rating",
                    models.FloatField(
                        default=0.0, help_text="Average user rating (0-5)"
                    ),
                ),
                (
                    "rating_count",
                    models.PositiveIntegerField(
                        default=0, help_text="Total number of ratings"
                    ),
                ),
                (
                    "completion_rate",
                    models.FloatField(
                        default=0.0,
                        help_text="Percentage of users who complete this template",
                    ),
                ),
                (
                    "is_premium",
                    models.BooleanField(
                        default=False, help_text="Whether this is a premium template"
                    ),
                ),
                (
                    "is_featured",
                    models.BooleanField(
                        default=False, help_text="Whether this template is featured"
                    ),
                ),
                (
                    "is_bestseller",
                    models.BooleanField(
                        default=False, help_text="Whether this template is a bestseller"
                    ),
                ),
                (
                    "tags",
                    models.JSONField(
                        default=list, help_text="List of tags for this template"
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        default="FileText",
                        help_text="Icon name for this template",
                        max_length=50,
                    ),
                ),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_system",
                    models.BooleanField(
                        default=False,
                        help_text="Whether this is a system-provided template",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        blank=True,
                        help_text="Template author",
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="authored_templates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["industry", "name"],
            },
        ),
        migrations.AddField(
            model_name="businessplan",
            name="template",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="business_plans",
                to="incubator.businessplantemplate",
            ),
        ),
        migrations.CreateModel(
            name="ComparativeAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "similar_ideas",
                    models.JSONField(
                        default=dict,
                        help_text="List of similar business ideas and their metrics",
                    ),
                ),
                (
                    "industry_averages",
                    models.JSONField(
                        default=dict, help_text="Average metrics for the industry"
                    ),
                ),
                (
                    "stage_averages",
                    models.JSONField(
                        default=dict, help_text="Average metrics for the current stage"
                    ),
                ),
                (
                    "percentile_rankings",
                    models.JSONField(
                        default=dict,
                        help_text="Percentile rankings across different metrics",
                    ),
                ),
                (
                    "competitive_advantages",
                    models.JSONField(
                        default=dict, help_text="Identified competitive advantages"
                    ),
                ),
                (
                    "competitive_disadvantages",
                    models.JSONField(
                        default=dict, help_text="Identified competitive disadvantages"
                    ),
                ),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="comparative_analytics",
                        to="incubator.businessidea",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CustomBusinessPlanTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "sections",
                    models.JSONField(
                        help_text="JSON structure defining the sections of this custom template"
                    ),
                ),
                (
                    "custom_prompts",
                    models.JSONField(
                        default=dict, help_text="Custom AI prompts for each section"
                    ),
                ),
                (
                    "custom_instructions",
                    models.JSONField(
                        default=dict, help_text="Custom instructions for each section"
                    ),
                ),
                ("is_public", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "base_template",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="custom_templates",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "owner",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="custom_templates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "shared_with",
                    models.ManyToManyField(
                        blank=True,
                        related_name="shared_templates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-updated_at"],
                "unique_together": {("owner", "name")},
            },
        ),
        migrations.AddField(
            model_name="businessplan",
            name="custom_template",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="business_plans",
                to="incubator.custombusinessplantemplate",
            ),
        ),
        migrations.CreateModel(
            name="FundingOpportunity",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "funding_type",
                    models.CharField(
                        choices=[
                            ("grant", "Grant"),
                            ("equity", "Equity Investment"),
                            ("loan", "Loan"),
                            ("convertible", "Convertible Note"),
                            ("prize", "Competition Prize"),
                            ("crowdfunding", "Crowdfunding"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount in USD",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "eligibility_criteria",
                    models.TextField(help_text="Who can apply for this funding"),
                ),
                (
                    "application_process",
                    models.TextField(help_text="How to apply for this funding"),
                ),
                (
                    "application_deadline",
                    models.DateField(help_text="Deadline for applications"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("closed", "Closed"),
                            ("draft", "Draft"),
                        ],
                        default="draft",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "provider",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="provided_funding_opportunities",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Funding Opportunities",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="FundingApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "pitch",
                    models.TextField(
                        help_text="Pitch for why this business idea deserves funding"
                    ),
                ),
                (
                    "requested_amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount requested in USD",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "use_of_funds",
                    models.TextField(help_text="How the funds will be used"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending Review"),
                            ("shortlisted", "Shortlisted"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                            ("funded", "Funded"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                (
                    "reviewer_notes",
                    models.TextField(
                        blank=True, help_text="Private notes for reviewers", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "applicant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="funding_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="funding_applications",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "funding_opportunity",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="applications",
                        to="incubator.fundingopportunity",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="IncubatorResource",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "resource_type",
                    models.CharField(
                        choices=[
                            ("article", "Article"),
                            ("video", "Video"),
                            ("template", "Template"),
                            ("tool", "Tool"),
                            ("course", "Course"),
                            ("ebook", "E-Book"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("ideation", "Business Ideation"),
                            ("validation", "Market Validation"),
                            ("planning", "Business Planning"),
                            ("finance", "Financial Planning"),
                            ("marketing", "Marketing"),
                            ("legal", "Legal & Compliance"),
                            ("operations", "Operations"),
                            ("technology", "Technology"),
                            ("growth", "Growth & Scaling"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                ("url", models.URLField()),
                (
                    "image",
                    models.ImageField(
                        blank=True,
                        null=True,
                        storage=api.storage.OptimizedImageStorage(),
                        upload_to="incubator_resource_images/",
                    ),
                ),
                (
                    "file",
                    models.FileField(
                        blank=True, null=True, upload_to="incubator_resource_files/"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "author",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="incubator_resources",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "tags",
                    models.ManyToManyField(
                        blank=True, related_name="incubator_resources", to="api.tag"
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="Investment",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "investment_type",
                    models.CharField(
                        choices=[
                            ("equity", "Equity Investment"),
                            ("loan", "Loan"),
                            ("convertible", "Convertible Note"),
                            ("grant", "Grant"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "amount",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Amount in USD",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "equity_percentage",
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        help_text="Percentage of equity",
                        max_digits=5,
                        null=True,
                        validators=[
                            django.core.validators.MinValueValidator(0),
                            django.core.validators.MaxValueValidator(100),
                        ],
                    ),
                ),
                ("terms", models.TextField(help_text="Terms of the investment")),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("proposed", "Proposed"),
                            ("negotiating", "Negotiating"),
                            ("accepted", "Accepted"),
                            ("completed", "Completed"),
                            ("declined", "Declined"),
                        ],
                        default="proposed",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="investments",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "investor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="investments_made",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="InvestorProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "investor_type",
                    models.CharField(
                        choices=[
                            ("angel", "Angel Investor"),
                            ("vc", "Venture Capital"),
                            ("corporate", "Corporate Investor"),
                            ("government", "Government Fund"),
                            ("crowdfunding", "Crowdfunding Platform"),
                            ("other", "Other"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "company_name",
                    models.CharField(blank=True, max_length=200, null=True),
                ),
                (
                    "bio",
                    models.TextField(help_text="Brief description of the investor"),
                ),
                (
                    "investment_focus",
                    models.TextField(help_text="Areas of interest for investment"),
                ),
                (
                    "investment_range_min",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Minimum investment amount in USD",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "investment_range_max",
                    models.DecimalField(
                        decimal_places=2,
                        help_text="Maximum investment amount in USD",
                        max_digits=12,
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                ("linkedin_profile", models.URLField(blank=True, null=True)),
                ("website", models.URLField(blank=True, null=True)),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the investor has been verified by administrators",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="investor_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MentorProfile",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "bio",
                    models.TextField(
                        help_text="Professional background and experience"
                    ),
                ),
                ("company", models.CharField(blank=True, max_length=200, null=True)),
                ("position", models.CharField(blank=True, max_length=200, null=True)),
                ("years_of_experience", models.PositiveIntegerField(default=0)),
                ("linkedin_profile", models.URLField(blank=True, null=True)),
                ("website", models.URLField(blank=True, null=True)),
                (
                    "availability",
                    models.CharField(
                        choices=[
                            ("high", "High - 5+ hours per week"),
                            ("medium", "Medium - 2-5 hours per week"),
                            ("low", "Low - 1-2 hours per week"),
                            ("limited", "Limited - Less than 1 hour per week"),
                        ],
                        default="medium",
                        max_length=20,
                    ),
                ),
                (
                    "max_mentees",
                    models.PositiveIntegerField(
                        default=3,
                        help_text="Maximum number of mentees willing to take on",
                    ),
                ),
                ("is_accepting_mentees", models.BooleanField(default=True)),
                (
                    "is_verified",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the mentor has been verified by administrators",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "user",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentor_profile",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MentorshipApplication",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "goals",
                    models.TextField(
                        help_text="What do you hope to achieve with mentorship?"
                    ),
                ),
                (
                    "specific_areas",
                    models.TextField(
                        help_text="What specific areas do you need help with?"
                    ),
                ),
                (
                    "commitment",
                    models.TextField(
                        help_text="How much time can you commit to working with a mentor?"
                    ),
                ),
                (
                    "preferred_communication",
                    models.CharField(
                        choices=[
                            ("video", "Video Call"),
                            ("phone", "Phone Call"),
                            ("email", "Email"),
                            ("chat", "Chat/Messaging"),
                            ("in_person", "In Person"),
                        ],
                        default="video",
                        max_length=20,
                    ),
                ),
                (
                    "preferred_expertise",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("business_strategy", "Business Strategy"),
                            ("marketing", "Marketing & Sales"),
                            ("finance", "Finance & Accounting"),
                            ("operations", "Operations & Logistics"),
                            ("technology", "Technology & Development"),
                            ("product", "Product Management"),
                            ("legal", "Legal & Compliance"),
                            ("hr", "Human Resources"),
                            ("fundraising", "Fundraising & Investment"),
                            ("international", "International Business"),
                            ("ecommerce", "E-Commerce"),
                            ("social_impact", "Social Impact"),
                            ("other", "Other"),
                        ],
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("approved", "Approved"),
                            ("rejected", "Rejected"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("admin_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "applicant",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_applications",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_applications",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "preferred_mentor",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="preferred_applications",
                        to="incubator.mentorprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MentorshipMatch",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("completed", "Completed"),
                            ("paused", "Paused"),
                            ("terminated", "Terminated"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                ("start_date", models.DateField(auto_now_add=True)),
                ("end_date", models.DateField(blank=True, null=True)),
                ("goals", models.TextField()),
                ("focus_areas", models.TextField()),
                (
                    "mentee_notes",
                    models.TextField(
                        blank=True, help_text="Private notes for the mentee", null=True
                    ),
                ),
                (
                    "mentor_notes",
                    models.TextField(
                        blank=True, help_text="Private notes for the mentor", null=True
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "application",
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="resulting_match",
                        to="incubator.mentorshipapplication",
                    ),
                ),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_matches",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "mentee",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_matches",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentorship_matches",
                        to="incubator.mentorprofile",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Mentorship Matches",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="MentorshipSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField(blank=True, null=True)),
                ("scheduled_at", models.DateTimeField()),
                ("duration_minutes", models.PositiveIntegerField(default=60)),
                (
                    "session_type",
                    models.CharField(
                        choices=[
                            ("video", "Video Call"),
                            ("phone", "Phone Call"),
                            ("in_person", "In Person"),
                            ("chat", "Chat/Messaging"),
                        ],
                        default="video",
                        max_length=20,
                    ),
                ),
                (
                    "location",
                    models.CharField(
                        blank=True,
                        help_text="Physical location or virtual meeting link",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "video_provider",
                    models.CharField(
                        blank=True,
                        choices=[
                            ("zoom", "Zoom"),
                            ("google_meet", "Google Meet"),
                            ("microsoft_teams", "Microsoft Teams"),
                            ("jitsi", "Jitsi Meet"),
                            ("custom", "Custom Link"),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    "meeting_id",
                    models.CharField(
                        blank=True,
                        help_text="Meeting ID for video conferencing",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "meeting_password",
                    models.CharField(
                        blank=True,
                        help_text="Password for video conferencing",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "meeting_link",
                    models.URLField(
                        blank=True,
                        help_text="Direct link to join the meeting",
                        null=True,
                    ),
                ),
                ("reminder_sent", models.BooleanField(default=False)),
                ("reminder_sent_at", models.DateTimeField(blank=True, null=True)),
                (
                    "mentor_calendar_event_id",
                    models.CharField(
                        blank=True,
                        help_text="Calendar event ID for mentor",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "mentee_calendar_event_id",
                    models.CharField(
                        blank=True,
                        help_text="Calendar event ID for mentee",
                        max_length=200,
                        null=True,
                    ),
                ),
                (
                    "calendar_provider",
                    models.CharField(
                        blank=True,
                        help_text="Calendar provider (google_calendar, outlook, etc.)",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("scheduled", "Scheduled"),
                            ("completed", "Completed"),
                            ("cancelled", "Cancelled"),
                            ("rescheduled", "Rescheduled"),
                            ("in_progress", "In Progress"),
                        ],
                        default="scheduled",
                        max_length=20,
                    ),
                ),
                ("mentor_notes", models.TextField(blank=True, null=True)),
                ("mentee_notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("started_at", models.DateTimeField(blank=True, null=True)),
                ("ended_at", models.DateTimeField(blank=True, null=True)),
                (
                    "mentorship_match",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="incubator.mentorshipmatch",
                    ),
                ),
            ],
            options={
                "ordering": ["-scheduled_at"],
            },
        ),
        migrations.CreateModel(
            name="MentorshipFeedback",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("is_from_mentee", models.BooleanField(default=True)),
                (
                    "rating",
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ]
                    ),
                ),
                (
                    "knowledge_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating for knowledge and expertise",
                        null=True,
                    ),
                ),
                (
                    "communication_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating for communication skills",
                        null=True,
                    ),
                ),
                (
                    "helpfulness_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating for helpfulness",
                        null=True,
                    ),
                ),
                (
                    "preparation_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating for preparation and organization",
                        null=True,
                    ),
                ),
                (
                    "responsiveness_rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (1, "1 - Poor"),
                            (2, "2 - Below Average"),
                            (3, "3 - Average"),
                            (4, "4 - Good"),
                            (5, "5 - Excellent"),
                        ],
                        help_text="Rating for responsiveness",
                        null=True,
                    ),
                ),
                ("comments", models.TextField()),
                ("areas_of_improvement", models.TextField(blank=True, null=True)),
                (
                    "highlights",
                    models.TextField(
                        blank=True,
                        help_text="What went particularly well in this session",
                        null=True,
                    ),
                ),
                (
                    "goals_achieved",
                    models.BooleanField(
                        default=False, help_text="Were the session goals achieved?"
                    ),
                ),
                (
                    "follow_up_needed",
                    models.BooleanField(
                        default=False, help_text="Is follow-up needed?"
                    ),
                ),
                (
                    "follow_up_notes",
                    models.TextField(
                        blank=True, help_text="Notes for follow-up", null=True
                    ),
                ),
                (
                    "is_private",
                    models.BooleanField(
                        default=False,
                        help_text="If true, feedback is only visible to administrators",
                    ),
                ),
                (
                    "share_with_mentor",
                    models.BooleanField(
                        default=True, help_text="Share this feedback with the mentor"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "provided_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="provided_feedback",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="feedback",
                        to="incubator.mentorshipsession",
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="PredictiveAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "growth_predictions",
                    models.JSONField(
                        default=dict,
                        help_text="Predicted growth metrics for the next 6 months",
                    ),
                ),
                (
                    "milestone_predictions",
                    models.JSONField(
                        default=dict,
                        help_text="Predicted completion dates for milestones",
                    ),
                ),
                (
                    "success_probability",
                    models.FloatField(
                        default=0.0, help_text="Probability of business success (0-100)"
                    ),
                ),
                (
                    "risk_factors",
                    models.JSONField(
                        default=dict,
                        help_text="Identified risk factors and their severity",
                    ),
                ),
                (
                    "opportunity_areas",
                    models.JSONField(
                        default=dict,
                        help_text="Identified opportunity areas for growth",
                    ),
                ),
                (
                    "prediction_confidence",
                    models.CharField(
                        choices=[
                            ("low", "Low"),
                            ("medium", "Medium"),
                            ("high", "High"),
                        ],
                        default="low",
                        help_text="Confidence level in the predictions",
                        max_length=20,
                    ),
                ),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="predictive_analytics",
                        to="incubator.businessidea",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="ProgressUpdate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=200)),
                ("description", models.TextField()),
                (
                    "achievements",
                    models.TextField(help_text="What milestones have you achieved?"),
                ),
                (
                    "challenges",
                    models.TextField(
                        blank=True,
                        help_text="What challenges are you facing?",
                        null=True,
                    ),
                ),
                ("next_steps", models.TextField(help_text="What are your next steps?")),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="progress_updates",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "created_by",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="progress_updates",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TemplatePerformanceMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_views", models.IntegerField(default=0)),
                ("total_selections", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("unique_users", models.IntegerField(default=0)),
                ("selection_rate", models.FloatField(default=0.0)),
                ("completion_rate", models.FloatField(default=0.0)),
                (
                    "average_completion_time",
                    models.DurationField(blank=True, null=True),
                ),
                ("average_rating", models.FloatField(default=0.0)),
                ("total_ratings", models.IntegerField(default=0)),
                ("net_promoter_score", models.FloatField(default=0.0)),
                ("business_plans_published", models.IntegerField(default=0)),
                ("funding_success_rate", models.FloatField(default=0.0)),
                ("business_launch_rate", models.FloatField(default=0.0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="performance_metrics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_performance_metrics",
            },
        ),
        migrations.CreateModel(
            name="TemplateSuccessMetrics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("total_uses", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("completion_rate", models.FloatField(default=0.0)),
                (
                    "average_completion_time",
                    models.DurationField(default=datetime.timedelta),
                ),
                ("fastest_completion", models.DurationField(blank=True, null=True)),
                ("slowest_completion", models.DurationField(blank=True, null=True)),
                ("plans_published", models.IntegerField(default=0)),
                ("funding_received", models.IntegerField(default=0)),
                ("business_launched", models.IntegerField(default=0)),
                ("average_rating", models.FloatField(default=0.0)),
                ("total_ratings", models.IntegerField(default=0)),
                ("last_calculated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="success_metrics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_success_metrics",
            },
        ),
        migrations.CreateModel(
            name="AnalyticsSnapshot",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("snapshot_date", models.DateField(auto_now_add=True)),
                ("progress_rate", models.FloatField(default=0.0)),
                ("milestone_completion_rate", models.FloatField(default=0.0)),
                ("goal_achievement_rate", models.FloatField(default=0.0)),
                ("team_size", models.IntegerField(default=1)),
                ("mentor_engagement", models.FloatField(default=0.0)),
                ("industry_percentile", models.FloatField(default=0.0)),
                ("stage_percentile", models.FloatField(default=0.0)),
                ("success_probability", models.FloatField(default=0.0)),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="analytics_snapshots",
                        to="incubator.businessidea",
                    ),
                ),
            ],
            options={
                "ordering": ["-snapshot_date"],
                "unique_together": {("business_idea", "snapshot_date")},
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanCollaboration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("view", "View"),
                            ("edit", "Edit"),
                            ("comment", "Comment"),
                            ("review", "Review"),
                            ("approve", "Approve"),
                            ("reject", "Reject"),
                            ("share", "Share"),
                            ("invite", "Invite Collaborator"),
                        ],
                        max_length=20,
                    ),
                ),
                ("section_id", models.IntegerField(blank=True, null=True)),
                ("section_title", models.CharField(blank=True, max_length=200)),
                (
                    "content",
                    models.TextField(
                        blank=True, help_text="Comment content or edit description"
                    ),
                ),
                (
                    "metadata",
                    models.JSONField(
                        default=dict, help_text="Additional action metadata"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="collaborations",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_collaboration",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "created_at"],
                        name="business_pl_busines_31df19_idx",
                    ),
                    models.Index(
                        fields=["user", "action_type"],
                        name="business_pl_user_id_0a4925_idx",
                    ),
                    models.Index(
                        fields=["action_type", "created_at"],
                        name="business_pl_action__f48885_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanExport",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "export_format",
                    models.CharField(
                        choices=[
                            ("pdf", "PDF"),
                            ("word", "Microsoft Word"),
                            ("excel", "Microsoft Excel"),
                            ("powerpoint", "PowerPoint"),
                            ("json", "JSON"),
                            ("html", "HTML"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "export_type",
                    models.CharField(
                        choices=[
                            ("full", "Full Business Plan"),
                            ("summary", "Executive Summary"),
                            ("financial", "Financial Projections"),
                            ("sections", "Selected Sections"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "sections_included",
                    models.JSONField(
                        default=list, help_text="List of section IDs included"
                    ),
                ),
                (
                    "file_size",
                    models.BigIntegerField(
                        blank=True, help_text="File size in bytes", null=True
                    ),
                ),
                ("file_path", models.CharField(blank=True, max_length=500)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("downloaded_at", models.DateTimeField(blank=True, null=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("export_successful", models.BooleanField(default=True)),
                ("error_message", models.TextField(blank=True)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="exports",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_export",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "created_at"],
                        name="business_pl_busines_3b05bf_idx",
                    ),
                    models.Index(
                        fields=["user", "export_format"],
                        name="business_pl_user_id_92adfd_idx",
                    ),
                    models.Index(
                        fields=["export_format", "created_at"],
                        name="business_pl_export__4f17e1_idx",
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("start_time", models.DateTimeField(auto_now_add=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                ("last_activity", models.DateTimeField(auto_now=True)),
                ("session_id", models.CharField(max_length=100, unique=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("user_agent", models.TextField(blank=True)),
                ("device_type", models.CharField(blank=True, max_length=20)),
                (
                    "sections_viewed",
                    models.JSONField(
                        default=list, help_text="List of section IDs viewed"
                    ),
                ),
                (
                    "sections_edited",
                    models.JSONField(
                        default=list, help_text="List of section IDs edited"
                    ),
                ),
                ("ai_assistance_used", models.BooleanField(default=False)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sessions",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "business_plan_session",
                "indexes": [
                    models.Index(
                        fields=["business_plan", "start_time"],
                        name="business_pl_busines_7c1631_idx",
                    ),
                    models.Index(
                        fields=["user", "start_time"],
                        name="business_pl_user_id_787086_idx",
                    ),
                    models.Index(
                        fields=["session_id"], name="business_pl_session_11af1f_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="MentorExpertise",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("business_strategy", "Business Strategy"),
                            ("marketing", "Marketing & Sales"),
                            ("finance", "Finance & Accounting"),
                            ("operations", "Operations & Logistics"),
                            ("technology", "Technology & Development"),
                            ("product", "Product Management"),
                            ("legal", "Legal & Compliance"),
                            ("hr", "Human Resources"),
                            ("fundraising", "Fundraising & Investment"),
                            ("international", "International Business"),
                            ("ecommerce", "E-Commerce"),
                            ("social_impact", "Social Impact"),
                            ("other", "Other"),
                        ],
                        max_length=50,
                    ),
                ),
                (
                    "specific_expertise",
                    models.CharField(
                        help_text="Specific expertise within this category",
                        max_length=200,
                    ),
                ),
                (
                    "level",
                    models.CharField(
                        choices=[
                            ("beginner", "Beginner"),
                            ("intermediate", "Intermediate"),
                            ("advanced", "Advanced"),
                            ("expert", "Expert"),
                        ],
                        default="intermediate",
                        max_length=20,
                    ),
                ),
                ("years_experience", models.PositiveIntegerField(default=0)),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="expertise_areas",
                        to="incubator.mentorprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["category", "-level"],
                "unique_together": {("mentor", "category", "specific_expertise")},
            },
        ),
        migrations.CreateModel(
            name="MentorRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "match_score",
                    models.FloatField(
                        help_text="Score from 0-100 indicating match quality"
                    ),
                ),
                (
                    "match_reason",
                    models.TextField(
                        help_text="Explanation of why this mentor is recommended"
                    ),
                ),
                (
                    "expertise_match",
                    models.TextField(
                        help_text="Specific expertise areas that match the business idea needs"
                    ),
                ),
                (
                    "is_applied",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the user has applied to this mentor",
                    ),
                ),
                (
                    "is_matched",
                    models.BooleanField(
                        default=False,
                        help_text="Whether a mentorship match has been created",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "business_idea",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="mentor_recommendations",
                        to="incubator.businessidea",
                    ),
                ),
                (
                    "mentor",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recommendations",
                        to="incubator.mentorprofile",
                    ),
                ),
            ],
            options={
                "ordering": ["-match_score"],
                "unique_together": {("business_idea", "mentor")},
            },
        ),
        migrations.CreateModel(
            name="TemplateABTest",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("test_name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                (
                    "traffic_split",
                    models.FloatField(
                        default=50.0, help_text="Percentage for variant (0-100)"
                    ),
                ),
                ("start_date", models.DateTimeField()),
                ("end_date", models.DateTimeField()),
                ("is_active", models.BooleanField(default=True)),
                ("original_conversions", models.IntegerField(default=0)),
                ("variant_conversions", models.IntegerField(default=0)),
                ("original_views", models.IntegerField(default=0)),
                ("variant_views", models.IntegerField(default=0)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "original_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_tests_original",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "variant_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="ab_tests_variant",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_ab_test",
                "indexes": [
                    models.Index(
                        fields=["is_active", "start_date"],
                        name="template_ab_is_acti_1f747a_idx",
                    ),
                    models.Index(
                        fields=["test_name"], name="template_ab_test_na_8ecb93_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="TemplateRecommendation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("recommendation_score", models.FloatField(default=0.0)),
                ("recommendation_reason", models.TextField(blank=True)),
                ("viewed", models.BooleanField(default=False)),
                ("selected", models.BooleanField(default=False)),
                ("dismissed", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("viewed_at", models.DateTimeField(blank=True, null=True)),
                ("selected_at", models.DateTimeField(blank=True, null=True)),
                (
                    "recommended_template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="recommendations",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "template_recommendation",
                "indexes": [
                    models.Index(
                        fields=["user", "created_at"],
                        name="template_re_user_id_d561b5_idx",
                    ),
                    models.Index(
                        fields=["recommendation_score"],
                        name="template_re_recomme_0350d1_idx",
                    ),
                ],
                "unique_together": {("user", "recommended_template")},
            },
        ),
        migrations.CreateModel(
            name="TemplateSectionAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("section_key", models.CharField(max_length=100)),
                ("section_title", models.CharField(max_length=200)),
                ("total_views", models.IntegerField(default=0)),
                ("total_completions", models.IntegerField(default=0)),
                ("average_time_spent", models.DurationField(blank=True, null=True)),
                ("completion_rate", models.FloatField(default=0.0)),
                ("ai_assistance_used", models.IntegerField(default=0)),
                ("content_regenerated", models.IntegerField(default=0)),
                ("section_customized", models.IntegerField(default=0)),
                ("average_content_length", models.IntegerField(default=0)),
                ("user_satisfaction_score", models.FloatField(default=0.0)),
                ("last_updated", models.DateTimeField(auto_now=True)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="section_analytics",
                        to="incubator.businessplantemplate",
                    ),
                ),
            ],
            options={
                "db_table": "template_section_analytics",
                "indexes": [
                    models.Index(
                        fields=["template", "completion_rate"],
                        name="template_se_templat_727e10_idx",
                    ),
                    models.Index(
                        fields=["section_key", "completion_rate"],
                        name="template_se_section_46cbc8_idx",
                    ),
                ],
                "unique_together": {("template", "section_key")},
            },
        ),
        migrations.CreateModel(
            name="BusinessPlanSection",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                (
                    "key",
                    models.CharField(
                        help_text="Unique identifier for this section", max_length=50
                    ),
                ),
                ("content", models.TextField(blank=True)),
                ("order", models.PositiveSmallIntegerField(default=0)),
                ("is_required", models.BooleanField(default=True)),
                ("is_completed", models.BooleanField(default=False)),
                (
                    "custom_prompt",
                    models.TextField(
                        blank=True,
                        help_text="Custom AI prompt for this specific section",
                    ),
                ),
                (
                    "custom_instructions",
                    models.TextField(
                        blank=True, help_text="Custom instructions for this section"
                    ),
                ),
                (
                    "additional_data",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Additional data for specialized section types",
                    ),
                ),
                ("ai_suggestions", models.TextField(blank=True, null=True)),
                (
                    "business_plan",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="sections",
                        to="incubator.businessplan",
                    ),
                ),
                (
                    "section_definition",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="incubator.templatesectiondefinition",
                    ),
                ),
            ],
            options={
                "ordering": ["business_plan", "order"],
                "unique_together": {("business_plan", "key")},
            },
        ),
        migrations.CreateModel(
            name="TemplateUsageAnalytics",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("viewed_at", models.DateTimeField(auto_now_add=True)),
                ("selected_at", models.DateTimeField(blank=True, null=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("completion_time", models.DurationField(blank=True, null=True)),
                (
                    "rating",
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)],
                        help_text="Rating from 1-5 stars",
                        null=True,
                    ),
                ),
                ("feedback_text", models.TextField(blank=True)),
                ("business_plan_published", models.BooleanField(default=False)),
                ("funding_received", models.BooleanField(default=False)),
                ("business_launched", models.BooleanField(default=False)),
                ("user_agent", models.TextField(blank=True)),
                ("ip_address", models.GenericIPAddressField(blank=True, null=True)),
                ("referrer", models.URLField(blank=True)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="usage_analytics",
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "template_usage_analytics",
                "indexes": [
                    models.Index(
                        fields=["template", "viewed_at"],
                        name="template_us_templat_8f072f_idx",
                    ),
                    models.Index(
                        fields=["user", "viewed_at"],
                        name="template_us_user_id_ad19f2_idx",
                    ),
                    models.Index(
                        fields=["completed_at"], name="template_us_complet_38ffb3_idx"
                    ),
                ],
            },
        ),
        migrations.CreateModel(
            name="UserTemplateInteraction",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(max_length=100)),
                (
                    "action_type",
                    models.CharField(
                        choices=[
                            ("view", "View Template"),
                            ("select", "Select Template"),
                            ("start", "Start Working"),
                            ("save_section", "Save Section"),
                            ("complete_section", "Complete Section"),
                            ("use_ai", "Use AI Assistance"),
                            ("customize", "Customize Section"),
                            ("export", "Export Business Plan"),
                            ("share", "Share Business Plan"),
                            ("rate", "Rate Template"),
                            ("feedback", "Provide Feedback"),
                        ],
                        max_length=50,
                    ),
                ),
                ("section_key", models.CharField(blank=True, max_length=100)),
                ("action_data", models.JSONField(blank=True, default=dict)),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("duration", models.DurationField(blank=True, null=True)),
                ("device_type", models.CharField(blank=True, max_length=20)),
                ("browser", models.CharField(blank=True, max_length=50)),
                (
                    "template",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="incubator.businessplantemplate",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "db_table": "user_template_interaction",
                "indexes": [
                    models.Index(
                        fields=["user", "timestamp"],
                        name="user_templa_user_id_bd72f3_idx",
                    ),
                    models.Index(
                        fields=["template", "action_type"],
                        name="user_templa_templat_ebb591_idx",
                    ),
                    models.Index(
                        fields=["session_id"], name="user_templa_session_e9cb9d_idx"
                    ),
                ],
            },
        ),
    ]
