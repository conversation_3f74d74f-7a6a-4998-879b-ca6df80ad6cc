"""
🎯 ADVANCED TOPIC ANALYSIS SERVICE
Comprehensive topic analysis and trend detection for AI conversations

This service provides:
- Advanced topic classification using AI
- Trend analysis and pattern detection
- Sentiment analysis for topics
- Regional and temporal insights
- Business intelligence extraction
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from collections import defaultdict, Counter

from django.db.models import Count, Avg, Sum, Q
from django.utils import timezone
from django.contrib.auth.models import User

from ..models import AIConversationSession, AIMessage, AITopicAnalysis


class AdvancedTopicAnalysisService:
    """Advanced service for topic analysis and trend detection"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Enhanced topic mapping with comprehensive keywords
        self.topic_mapping = {
            'business_planning': {
                'keywords': [
                    'خطة', 'مشروع', 'عمل', 'تجارة', 'شركة', 'استراتيجية', 'تخطيط',
                    'إدارة', 'تطوير', 'نمو', 'توسع', 'فرع', 'مكتب', 'مصنع',
                    'business', 'plan', 'strategy', 'company', 'management'
                ],
                'weight': 1.0,
                'category': 'business'
            },
            'investment': {
                'keywords': [
                    'استثمار', 'أسهم', 'مال', 'ربح', 'خسارة', 'محفظة', 'بورصة',
                    'عائد', 'مخاطر', 'سندات', 'صندوق', 'تمويل', 'قرض', 'رأس مال',
                    'investment', 'stocks', 'portfolio', 'funding', 'capital'
                ],
                'weight': 1.2,
                'category': 'finance'
            },
            'market_analysis': {
                'keywords': [
                    'سوق', 'تحليل', 'منافسة', 'عملاء', 'طلب', 'عرض', 'أسعار',
                    'اتجاهات', 'دراسة', 'بحث', 'إحصائيات', 'بيانات', 'تقرير',
                    'market', 'analysis', 'competition', 'customers', 'trends'
                ],
                'weight': 1.1,
                'category': 'business'
            },
            'startup_advice': {
                'keywords': [
                    'ناشئة', 'بداية', 'فكرة', 'تأسيس', 'ريادة', 'مؤسس', 'شريك',
                    'نموذج', 'خطة عمل', 'تمويل أولي', 'حاضنة', 'مسرع',
                    'startup', 'entrepreneur', 'founder', 'incubator', 'accelerator'
                ],
                'weight': 1.3,
                'category': 'business'
            },
            'technology': {
                'keywords': [
                    'تكنولوجيا', 'برمجة', 'تطبيق', 'موقع', 'ذكي', 'رقمي', 'إنترنت',
                    'برنامج', 'نظام', 'قاعدة بيانات', 'خوارزمية', 'ذكاء اصطناعي',
                    'technology', 'programming', 'app', 'website', 'AI', 'digital'
                ],
                'weight': 1.0,
                'category': 'tech'
            },
            'education': {
                'keywords': [
                    'تعليم', 'دراسة', 'جامعة', 'كلية', 'شهادة', 'دورة', 'تدريب',
                    'مهارة', 'خبرة', 'تطوير ذاتي', 'تعلم', 'معرفة',
                    'education', 'university', 'course', 'training', 'skills'
                ],
                'weight': 0.9,
                'category': 'education'
            },
            'health': {
                'keywords': [
                    'صحة', 'طب', 'علاج', 'دواء', 'مرض', 'طبيب', 'مستشفى',
                    'عيادة', 'فحص', 'تحليل', 'وقاية', 'لياقة',
                    'health', 'medicine', 'doctor', 'hospital', 'treatment'
                ],
                'weight': 0.8,
                'category': 'health'
            },
            'damascus_business': {
                'keywords': [
                    'دمشق', 'الشام', 'سوريا', 'العاصمة', 'المزة', 'أبو رمانة',
                    'الصالحية', 'القصاع', 'المالكي', 'كفرسوسة', 'سوق الحميدية',
                    'damascus', 'syria', 'capital'
                ],
                'weight': 1.1,
                'category': 'regional'
            },
            'aleppo_business': {
                'keywords': [
                    'حلب', 'شمال', 'الشهباء', 'العزيزية', 'الفرقان', 'الحمدانية',
                    'صلاح الدين', 'المحافظة', 'الجامعة',
                    'aleppo', 'north'
                ],
                'weight': 1.1,
                'category': 'regional'
            },
            'general_chat': {
                'keywords': [
                    'مرحبا', 'كيف', 'شو', 'ايش', 'أهلا', 'سلام', 'صباح', 'مساء',
                    'شكرا', 'عفوا', 'أسف', 'ممكن', 'لو سمحت',
                    'hello', 'hi', 'how', 'what', 'thanks', 'please'
                ],
                'weight': 0.5,
                'category': 'general'
            }
        }
    
    def analyze_message_topics(self, message: AIMessage) -> List[Dict]:
        """
        Advanced topic analysis for a single message
        Returns list of detected topics with confidence scores
        """
        try:
            content = message.content.lower()
            detected_topics = []
            
            for topic_name, topic_data in self.topic_mapping.items():
                keywords = topic_data['keywords']
                weight = topic_data['weight']
                category = topic_data['category']
                
                # Calculate topic score
                matches = []
                for keyword in keywords:
                    if keyword.lower() in content:
                        matches.append(keyword)
                
                if matches:
                    # Calculate confidence based on matches and weight
                    confidence = min(len(matches) * 0.2 * weight, 1.0)
                    
                    detected_topics.append({
                        'topic': topic_name,
                        'category': category,
                        'confidence': confidence,
                        'keywords': matches,
                        'weight': weight
                    })
            
            # Sort by confidence
            detected_topics.sort(key=lambda x: x['confidence'], reverse=True)
            
            # If no topics detected, default to general_chat
            if not detected_topics:
                detected_topics.append({
                    'topic': 'general_chat',
                    'category': 'general',
                    'confidence': 0.3,
                    'keywords': [],
                    'weight': 0.5
                })
            
            return detected_topics
            
        except Exception as e:
            self.logger.error(f"❌ Failed to analyze message topics: {e}")
            return []
    
    def create_topic_analysis(self, message: AIMessage, topics: List[Dict]):
        """Create AITopicAnalysis records for detected topics"""
        try:
            for topic_data in topics:
                # Determine business and investment flags
                business_intent = topic_data['category'] in ['business', 'finance']
                investment_related = topic_data['topic'] == 'investment'
                
                AITopicAnalysis.objects.create(
                    session=message.session,
                    message=message,
                    topic_category=topic_data['topic'],
                    topic_keywords=topic_data['keywords'],
                    topic_confidence=topic_data['confidence'],
                    business_intent=business_intent,
                    investment_related=investment_related,
                    regional_context=message.session.user_region,
                    analysis_method='advanced_ai_classification'
                )
                
        except Exception as e:
            self.logger.error(f"❌ Failed to create topic analysis: {e}")
    
    def get_topic_trends(self, days: int = 30) -> Dict:
        """Get topic trends and analytics for the specified period"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Get topic analysis data
            topics = AITopicAnalysis.objects.filter(
                analyzed_at__gte=start_date,
                analyzed_at__lte=end_date
            )
            
            # Topic distribution
            topic_distribution = topics.values('topic_category').annotate(
                count=Count('id'),
                avg_confidence=Avg('topic_confidence')
            ).order_by('-count')
            
            # Daily topic trends
            daily_trends = defaultdict(lambda: defaultdict(int))
            for topic in topics:
                date_key = topic.analyzed_at.strftime('%Y-%m-%d')
                daily_trends[date_key][topic.topic_category] += 1
            
            # Business vs non-business breakdown
            business_breakdown = {
                'business_related': topics.filter(business_intent=True).count(),
                'non_business': topics.filter(business_intent=False).count(),
                'investment_related': topics.filter(investment_related=True).count()
            }
            
            # Regional breakdown
            regional_breakdown = topics.values('regional_context').annotate(
                count=Count('id')
            ).order_by('-count')
            
            # Top keywords
            all_keywords = []
            for topic in topics:
                if topic.topic_keywords:
                    all_keywords.extend(topic.topic_keywords)
            
            top_keywords = Counter(all_keywords).most_common(20)
            
            return {
                'topic_distribution': list(topic_distribution),
                'daily_trends': dict(daily_trends),
                'business_breakdown': business_breakdown,
                'regional_breakdown': list(regional_breakdown),
                'top_keywords': top_keywords,
                'total_analyzed': topics.count(),
                'period_days': days
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get topic trends: {e}")
            return {}
    
    def get_topic_insights(self, topic_category: str, days: int = 30) -> Dict:
        """Get detailed insights for a specific topic"""
        try:
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            topic_data = AITopicAnalysis.objects.filter(
                topic_category=topic_category,
                analyzed_at__gte=start_date,
                analyzed_at__lte=end_date
            )
            
            if not topic_data.exists():
                return {'error': 'No data found for this topic'}
            
            # Basic stats
            total_count = topic_data.count()
            avg_confidence = topic_data.aggregate(avg=Avg('topic_confidence'))['avg']
            
            # Time series data
            daily_counts = defaultdict(int)
            for item in topic_data:
                date_key = item.analyzed_at.strftime('%Y-%m-%d')
                daily_counts[date_key] += 1
            
            # Related sessions and users
            unique_sessions = topic_data.values('session').distinct().count()
            unique_users = topic_data.values('session__user').distinct().count()
            
            # Keywords analysis
            all_keywords = []
            for item in topic_data:
                if item.topic_keywords:
                    all_keywords.extend(item.topic_keywords)
            
            keyword_frequency = Counter(all_keywords).most_common(10)
            
            return {
                'topic_category': topic_category,
                'total_count': total_count,
                'average_confidence': round(avg_confidence, 3) if avg_confidence else 0,
                'unique_sessions': unique_sessions,
                'unique_users': unique_users,
                'daily_counts': dict(daily_counts),
                'top_keywords': keyword_frequency,
                'period_days': days
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get topic insights: {e}")
            return {'error': str(e)}
