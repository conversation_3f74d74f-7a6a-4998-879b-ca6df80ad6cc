import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { useAppSelector } from '../store/hooks';
import {
  Calendar,
  MapPin,
  Users,
  Clock,
  Search,
  Filter,
  Star,
  Globe,
  ChevronRight,
  ChevronLeft,
  Plus,
  Sparkles
} from 'lucide-react';

interface Event {
  id: number;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  location: string;
  max_participants?: number;
  current_participants?: number;
  is_online: boolean;
  registration_deadline?: string;
  organizer?: {
    first_name: string;
    last_name: string;
  };
  status: string;
}

const PublicEventsPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const { user } = useAppSelector(state => state.auth);
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');

  // Check if user is admin/staff
  const isAdmin = user?.user_role === 'admin' || user?.is_staff || user?.is_superuser;

  // Mock data for demonstration
  const mockEvents: Event[] = [
    {
      id: 1,
      title: isRTL ? 'ورشة عمل: أساسيات ريادة الأعمال' : 'Workshop: Entrepreneurship Fundamentals',
      description: isRTL ? 'تعلم الأساسيات المهمة لبدء مشروعك التجاري' : 'Learn the essential fundamentals to start your business',
      start_date: '2024-08-15T10:00:00Z',
      end_date: '2024-08-15T16:00:00Z',
      location: isRTL ? 'مركز الابتكار - الرياض' : 'Innovation Center - Riyadh',
      max_participants: 50,
      current_participants: 32,
      is_online: false,
      registration_deadline: '2024-08-10T23:59:59Z',
      organizer: { first_name: 'أحمد', last_name: 'محمد' },
      status: 'upcoming'
    },
    {
      id: 2,
      title: isRTL ? 'ندوة عبر الإنترنت: التسويق الرقمي' : 'Online Seminar: Digital Marketing',
      description: isRTL ? 'استراتيجيات التسويق الرقمي الحديثة للشركات الناشئة' : 'Modern digital marketing strategies for startups',
      start_date: '2024-08-20T14:00:00Z',
      end_date: '2024-08-20T17:00:00Z',
      location: isRTL ? 'عبر الإنترنت' : 'Online',
      max_participants: 100,
      current_participants: 78,
      is_online: true,
      registration_deadline: '2024-08-18T23:59:59Z',
      organizer: { first_name: 'فاطمة', last_name: 'أحمد' },
      status: 'upcoming'
    },
    {
      id: 3,
      title: isRTL ? 'مؤتمر الذكاء الاصطناعي' : 'AI Conference 2024',
      description: isRTL ? 'مؤتمر سنوي حول أحدث تطورات الذكاء الاصطناعي' : 'Annual conference on the latest AI developments',
      start_date: '2024-09-05T09:00:00Z',
      end_date: '2024-09-06T18:00:00Z',
      location: isRTL ? 'فندق الريتز كارلتون - جدة' : 'Ritz Carlton Hotel - Jeddah',
      max_participants: 200,
      current_participants: 156,
      is_online: false,
      registration_deadline: '2024-08-30T23:59:59Z',
      organizer: { first_name: 'محمد', last_name: 'علي' },
      status: 'upcoming'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setEvents(mockEvents);
      setLoading(false);
    }, 1000);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(isRTL ? 'ar-SA' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventStatus = (event: Event) => {
    const now = new Date();
    const startDate = new Date(event.start_date);
    const endDate = new Date(event.end_date);
    
    if (now < startDate) return 'upcoming';
    if (now >= startDate && now <= endDate) return 'ongoing';
    return 'completed';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'ongoing': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'completed': return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const getStatusLabel = (status: string) => {
    const labels = {
      upcoming: isRTL ? 'قادم' : 'Upcoming',
      ongoing: isRTL ? 'جاري' : 'Ongoing',
      completed: isRTL ? 'مكتمل' : 'Completed'
    };
    return labels[status as keyof typeof labels] || status;
  };

  const filteredEvents = events.filter(event => {
    const matchesSearch = event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         event.description.toLowerCase().includes(searchQuery.toLowerCase());
    const eventStatus = getEventStatus(event);
    const matchesFilter = selectedFilter === 'all' || eventStatus === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center ${isRTL ? 'font-arabic' : ''}`}>
        <div className="text-white text-xl">{isRTL ? 'جاري التحميل...' : 'Loading...'}</div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 ${isRTL ? 'font-arabic' : ''}`}>
      {/* Modern Header with Dark Theme */}
      <div className="bg-black/30 backdrop-blur-sm shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className={`flex items-center justify-between h-20 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl blur opacity-75"></div>
                <div className="relative bg-gradient-to-r from-blue-600 to-purple-600 p-2 rounded-xl">
                  <Sparkles className="w-8 h-8 text-white" />
                </div>
              </div>
              <h1 className={`text-2xl font-bold text-white ${isRTL ? 'mr-4' : 'ml-4'}`}>
                {isRTL ? 'الفعاليات والورش' : 'Events & Workshops'}
              </h1>
            </div>

            {/* Admin Create Event Button */}
            {isAdmin && (
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 shadow-lg">
                <Plus className="w-5 h-5" />
                {isRTL ? 'إنشاء فعالية' : 'Create Event'}
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Description */}
        <div className="text-center mb-8">
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {isRTL
              ? 'اكتشف ورش العمل والفعاليات التفاعلية لتطوير مهاراتك ومعرفتك'
              : 'Discover interactive workshops and events to develop your skills and knowledge'
            }
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8">
          <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400 w-5 h-5`} />
                <input
                  type="text"
                  placeholder={isRTL ? 'البحث في الفعاليات...' : 'Search events...'}
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className={`w-full bg-white/10 border border-white/20 rounded-lg py-3 ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
                />
              </div>
              <div className="relative">
                <Filter className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400 w-5 h-5`} />
                <select
                  value={selectedFilter}
                  onChange={(e) => setSelectedFilter(e.target.value)}
                  className={`bg-white/10 border border-white/20 rounded-lg py-3 ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} text-white focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200`}
                >
                  <option value="all">{isRTL ? 'جميع الفعاليات' : 'All Events'}</option>
                  <option value="upcoming">{isRTL ? 'الفعاليات القادمة' : 'Upcoming Events'}</option>
                  <option value="ongoing">{isRTL ? 'الفعاليات الجارية' : 'Ongoing Events'}</option>
                  <option value="completed">{isRTL ? 'الفعاليات المكتملة' : 'Completed Events'}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredEvents.map((event) => {
            const eventStatus = getEventStatus(event);
            const isRegistrationOpen = new Date() < new Date(event.registration_deadline || event.start_date);

            return (
              <div
                key={event.id}
                className="bg-black/30 backdrop-blur-sm border border-white/20 rounded-2xl p-6 hover:bg-black/40 transition-all duration-300 hover:transform hover:scale-105 hover:shadow-2xl group"
              >
                {/* Event Status Badge */}
                <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border mb-4 ${getStatusColor(eventStatus)}`}>
                  {getStatusLabel(eventStatus)}
                </div>

                {/* Event Title */}
                <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2 group-hover:text-blue-300 transition-colors duration-300">
                  {event.title}
                </h3>

                {/* Event Description */}
                <p className="text-gray-300 mb-4 line-clamp-3 group-hover:text-gray-200 transition-colors duration-300">
                  {event.description}
                </p>

                {/* Event Details */}
                <div className="space-y-3 mb-6">
                  <div className={`flex items-center gap-2 text-sm text-gray-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Calendar className="w-4 h-4 text-blue-400" />
                    <span>{formatDate(event.start_date)}</span>
                  </div>
                  
                  <div className={`flex items-center gap-2 text-sm text-gray-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {event.is_online ? <Globe className="w-4 h-4 text-green-400" /> : <MapPin className="w-4 h-4 text-red-400" />}
                    <span>{event.location}</span>
                  </div>
                  
                  {event.max_participants && (
                    <div className={`flex items-center gap-2 text-sm text-gray-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Users className="w-4 h-4 text-purple-400" />
                      <span>
                        {event.current_participants}/{event.max_participants} {isRTL ? 'مشارك' : 'participants'}
                      </span>
                    </div>
                  )}
                </div>

                {/* Action Button */}
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <span className="text-sm text-gray-400 group-hover:text-gray-300 transition-colors duration-300">
                    {isRTL ? 'بواسطة' : 'By'} {event.organizer?.first_name} {event.organizer?.last_name}
                  </span>

                  {eventStatus === 'upcoming' && isRegistrationOpen ? (
                    <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg transition-all duration-200 flex items-center gap-2 shadow-lg hover:shadow-xl">
                      {isRTL ? 'تسجيل' : 'Register'}
                      {isRTL ? <ChevronLeft className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
                    </button>
                  ) : (
                    <button className="bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 text-white px-4 py-2 rounded-lg transition-all duration-200 backdrop-blur-sm">
                      {isRTL ? 'عرض التفاصيل' : 'View Details'}
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {filteredEvents.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-black/30 backdrop-blur-sm rounded-2xl p-12 border border-white/20">
              <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {isRTL ? 'لا توجد فعاليات' : 'No Events Found'}
              </h3>
              <p className="text-gray-400">
                {isRTL ? 'جرب تغيير معايير البحث' : 'Try adjusting your search criteria'}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublicEventsPage;
