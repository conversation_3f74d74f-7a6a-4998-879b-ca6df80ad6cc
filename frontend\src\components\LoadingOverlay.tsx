import React from 'react';
import { Loader2 } from 'lucide-react';

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  messageAr?: string;
  language: string;
  className?: string;
}

const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message,
  messageAr,
  language,
  className = ''
}) => {
  if (!isVisible) return null;

  const displayMessage = language === 'ar' ? messageAr || message : message;

  return (
    <div className={`fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 ${className}`}>
      <div className="bg-black/80 backdrop-blur-md rounded-xl p-8 border border-white/20 shadow-2xl max-w-sm w-full mx-4">
        <div className="text-center">
          {/* Animated Loading Spinner */}
          <div className="relative mb-6">
            <div className="w-16 h-16 mx-auto">
              <Loader2 className="w-16 h-16 text-purple-400 animate-spin" />
            </div>
            <div className="absolute inset-0 w-16 h-16 mx-auto">
              <div className="w-16 h-16 border-4 border-purple-600/20 border-t-purple-400 rounded-full animate-spin"></div>
            </div>
          </div>

          {/* Loading Message */}
          {displayMessage && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-white">
                {displayMessage}
              </h3>
              <p className="text-sm text-gray-300">
                {language === 'ar' 
                  ? 'يرجى الانتظار...'
                  : 'Please wait...'
                }
              </p>
            </div>
          )}

          {/* Progress Dots */}
          <div className="flex justify-center space-x-2 mt-6">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoadingOverlay;
