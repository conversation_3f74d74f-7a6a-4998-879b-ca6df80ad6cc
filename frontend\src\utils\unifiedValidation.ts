/**
 * 🎯 UNIFIED VALIDATION SYSTEM
 * Single source of truth for all validation logic - eliminates 25+ duplicate validation functions
 */

import { VALIDATION } from '../config/constants';

// ========================================
// TYPES
// ========================================

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  phone?: boolean;
  url?: boolean;
  number?: boolean;
  integer?: boolean;
  min?: number;
  max?: number;
  arabicOnly?: boolean;
  mixedText?: boolean;
  businessName?: boolean;
  address?: boolean;
  custom?: (value: any) => boolean | string;
  conditional?: (formData: any) => boolean;
}

export interface ValidationSchema {
  [fieldName: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface FormValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings?: Record<string, string[]>;
  fieldErrors: string[];
  globalErrors: string[];
}

// ========================================
// UNIFIED VALIDATOR CLASS
// ========================================

export class UnifiedValidator {
  private language: 'ar' | 'en';
  private messages: Record<string, Record<string, string>>;

  constructor(language: 'ar' | 'en' = 'en') {
    this.language = language;
    this.messages = {
      en: {
        required: 'This field is required',
        minLength: 'Must be at least {min} characters',
        maxLength: 'Must be less than {max} characters',
        invalidEmail: 'Please enter a valid email address',
        invalidPhone: 'Please enter a valid phone number',
        invalidUrl: 'Please enter a valid URL',
        invalidNumber: 'Please enter a valid number',
        invalidInteger: 'Please enter a valid integer',
        tooSmall: 'Value must be at least {min}',
        tooLarge: 'Value must be at most {max}',
        invalidFormat: 'Invalid format',
        invalidArabicText: 'Please enter valid Arabic text',
        invalidMixedText: 'Please enter valid text',
        invalidBusinessName: 'Please enter a valid business name',
        invalidAddress: 'Please enter a valid address'
      },
      ar: {
        required: 'هذا الحقل مطلوب',
        minLength: 'يجب أن يكون على الأقل {min} أحرف',
        maxLength: 'يجب أن يكون أقل من {max} أحرف',
        invalidEmail: 'يرجى إدخال عنوان بريد إلكتروني صحيح',
        invalidPhone: 'يرجى إدخال رقم هاتف صحيح',
        invalidUrl: 'يرجى إدخال رابط صحيح',
        invalidNumber: 'يرجى إدخال رقم صحيح',
        invalidInteger: 'يرجى إدخال رقم صحيح',
        tooSmall: 'يجب أن تكون القيمة على الأقل {min}',
        tooLarge: 'يجب أن تكون القيمة على الأكثر {max}',
        invalidFormat: 'تنسيق غير صحيح',
        invalidArabicText: 'يرجى إدخال نص عربي صحيح',
        invalidMixedText: 'يرجى إدخال نص صحيح',
        invalidBusinessName: 'يرجى إدخال اسم شركة صحيح',
        invalidAddress: 'يرجى إدخال عنوان صحيح'
      }
    };
  }

  /**
   * Validate a single field
   */
  validateField(value: any, rules: ValidationRule, formData?: any): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Skip validation if conditional rule is not met
    if (rules.conditional && formData && !rules.conditional(formData)) {
      return { isValid: true, errors: [], warnings };
    }

    const stringValue = String(value || '').trim();

    // Required validation
    if (rules.required && this.isEmpty(value)) {
      errors.push(this.getMessage('required'));
      return { isValid: false, errors, warnings };
    }

    // Skip other validations if field is empty and not required
    if (this.isEmpty(value) && !rules.required) {
      return { isValid: true, errors: [], warnings };
    }

    // Length validations
    if (rules.minLength !== undefined && stringValue.length < rules.minLength) {
      errors.push(this.getMessage('minLength', { min: rules.minLength }));
    }

    if (rules.maxLength !== undefined && stringValue.length > rules.maxLength) {
      errors.push(this.getMessage('maxLength', { max: rules.maxLength }));
    }

    // Pattern validation
    if (rules.pattern && !rules.pattern.test(stringValue)) {
      errors.push(this.getMessage('invalidFormat'));
    }

    // Email validation
    if (rules.email && !this.isValidEmail(stringValue)) {
      errors.push(this.getMessage('invalidEmail'));
    }

    // Phone validation
    if (rules.phone && !this.isValidPhone(stringValue)) {
      errors.push(this.getMessage('invalidPhone'));
    }

    // URL validation
    if (rules.url && !this.isValidUrl(stringValue)) {
      errors.push(this.getMessage('invalidUrl'));
    }

    // Number validation
    if (rules.number && !this.isValidNumber(value)) {
      errors.push(this.getMessage('invalidNumber'));
    }

    // Integer validation
    if (rules.integer && !this.isValidInteger(value)) {
      errors.push(this.getMessage('invalidInteger'));
    }

    // Min/Max value validation
    if (rules.min !== undefined && Number(value) < rules.min) {
      errors.push(this.getMessage('tooSmall', { min: rules.min }));
    }

    if (rules.max !== undefined && Number(value) > rules.max) {
      errors.push(this.getMessage('tooLarge', { max: rules.max }));
    }

    // Arabic-specific validations
    if (rules.arabicOnly && !this.isValidArabicText(stringValue)) {
      errors.push(this.getMessage('invalidArabicText'));
    }

    if (rules.mixedText && !this.isValidMixedText(stringValue)) {
      errors.push(this.getMessage('invalidMixedText'));
    }

    if (rules.businessName && !this.isValidBusinessName(stringValue)) {
      errors.push(this.getMessage('invalidBusinessName'));
    }

    if (rules.address && !this.isValidAddress(stringValue)) {
      errors.push(this.getMessage('invalidAddress'));
    }

    // Custom validation
    if (rules.custom) {
      const customResult = rules.custom(value);
      if (customResult === false) {
        errors.push(this.getMessage('invalidFormat'));
      } else if (typeof customResult === 'string') {
        errors.push(customResult);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate entire form
   */
  validateForm(formData: Record<string, any>, schema: ValidationSchema): FormValidationResult {
    const errors: Record<string, string[]> = {};
    const warnings: Record<string, string[]> = {};
    const fieldErrors: string[] = [];
    const globalErrors: string[] = [];
    let isValid = true;

    // Validate each field
    Object.keys(schema).forEach(fieldName => {
      const fieldValue = formData[fieldName];
      const fieldRules = schema[fieldName];

      const result = this.validateField(fieldValue, fieldRules, formData);

      if (!result.isValid) {
        errors[fieldName] = result.errors;
        fieldErrors.push(...result.errors);
        isValid = false;
      }

      if (result.warnings && result.warnings.length > 0) {
        warnings[fieldName] = result.warnings;
      }
    });

    return {
      isValid,
      errors,
      warnings,
      fieldErrors,
      globalErrors
    };
  }

  // ========================================
  // VALIDATION HELPERS
  // ========================================

  private isEmpty(value: any): boolean {
    return value === null || value === undefined || String(value).trim() === '';
  }

  private isValidEmail(email: string): boolean {
    return VALIDATION.PATTERNS.EMAIL.test(email);
  }

  private isValidPhone(phone: string): boolean {
    return VALIDATION.PATTERNS.PHONE.test(phone);
  }

  private isValidUrl(url: string): boolean {
    return VALIDATION.PATTERNS.URL.test(url);
  }

  private isValidNumber(value: any): boolean {
    return !isNaN(Number(value)) && isFinite(Number(value));
  }

  private isValidInteger(value: any): boolean {
    return Number.isInteger(Number(value));
  }

  private isValidArabicText(text: string): boolean {
    const arabicRegex = /^[\u0600-\u06FF\u0750-\u077F\s\u060C\u061B\u061F\u0640]+$/;
    return arabicRegex.test(text);
  }

  private isValidMixedText(text: string): boolean {
    const mixedRegex = /^[\u0600-\u06FF\u0750-\u077F\u0660-\u0669a-zA-Z0-9\s.,!?؟،]+$/;
    return mixedRegex.test(text);
  }

  private isValidBusinessName(name: string): boolean {
    if (name.length < 2 || name.length > 100) return false;
    const businessRegex = /^[\u0600-\u06FF\u0750-\u077Fa-zA-Z0-9\s&\-().،]+$/;
    return businessRegex.test(name);
  }

  private isValidAddress(address: string): boolean {
    if (address.length < 5 || address.length > 200) return false;
    const addressRegex = /^[\u0600-\u06FF\u0750-\u077Fa-zA-Z0-9\s\-.,/()،]+$/;
    return addressRegex.test(address);
  }

  private getMessage(key: string, params?: Record<string, any>): string {
    let message = this.messages[this.language][key] || this.messages.en[key] || key;
    
    if (params) {
      Object.keys(params).forEach(param => {
        message = message.replace(`{${param}}`, String(params[param]));
      });
    }
    
    return message;
  }

  // ========================================
  // UTILITY METHODS
  // ========================================

  setLanguage(language: 'ar' | 'en'): void {
    this.language = language;
  }

  getLanguage(): 'ar' | 'en' {
    return this.language;
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

export const validator = new UnifiedValidator();

// ========================================
// CONVENIENCE FUNCTIONS
// ========================================

export const validateField = (value: any, rules: ValidationRule, formData?: any): ValidationResult => {
  return validator.validateField(value, rules, formData);
};

export const validateForm = (formData: Record<string, any>, schema: ValidationSchema): FormValidationResult => {
  return validator.validateForm(formData, schema);
};

export const setValidationLanguage = (language: 'ar' | 'en'): void => {
  validator.setLanguage(language);
};

// ========================================
// COMMON VALIDATION SCHEMAS
// ========================================

export const commonSchemas = {
  user: {
    first_name: { required: true, minLength: 2, maxLength: 50 },
    last_name: { required: true, minLength: 2, maxLength: 50 },
    email: { required: true, email: true },
    phone: { phone: true },
    bio: { maxLength: 1000 },
    website: { url: true },
    location: { maxLength: 100 }
  },
  
  post: {
    title: { required: true, minLength: 5, maxLength: 200 },
    content: { required: true, minLength: 50 },
    tags: { maxLength: 500 }
  },
  
  event: {
    title: { required: true, minLength: 5, maxLength: 200 },
    description: { required: true, minLength: 20 },
    date: { required: true },
    location: { required: true, minLength: 5 }
  },
  
  business: {
    name: { required: true, businessName: true },
    description: { required: true, minLength: 50, maxLength: 2000 },
    industry: { required: true },
  },

  // ✅ ADDED: BusinessIdea validation schema matching backend model constraints
  businessIdea: {
    title: { required: true, minLength: 5, maxLength: 200 },
    description: { required: true, minLength: 20 },
    problem_statement: { required: true, minLength: 20 },
    solution_description: { required: true, minLength: 20 },
    target_audience: { required: true, minLength: 10 },
    market_opportunity: { maxLength: 2000 },
    business_model: { maxLength: 2000 },
    current_stage: { required: true },
    stage: { required: true }
  }
};

export default {
  UnifiedValidator,
  validator,
  validateField,
  validateForm,
  setValidationLanguage,
  commonSchemas
};
