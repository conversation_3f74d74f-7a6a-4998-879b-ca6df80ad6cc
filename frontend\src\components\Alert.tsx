import React from 'react';
import { CheckCircle, XCircle, Alert<PERSON>riangle, Info, X } from 'lucide-react';

interface AlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  message: string;
  onClose?: () => void;
  className?: string;
  showIcon?: boolean;
  dismissible?: boolean;
  actions?: React.ReactNode;
}

const Alert: React.FC<AlertProps> = ({
  type,
  title,
  message,
  onClose,
  className = '',
  showIcon = true,
  dismissible = false,
  actions
}) => {
  const getAlertClasses = () => {
    const baseClasses = 'rounded-lg p-4 border backdrop-blur-sm';
    
    switch (type) {
      case 'success':
        return `${baseClasses} bg-green-500/10 border-green-500/30 text-green-300`;
      case 'error':
        return `${baseClasses} bg-red-500/10 border-red-500/30 text-red-300`;
      case 'warning':
        return `${baseClasses} bg-yellow-500/10 border-yellow-500/30 text-yellow-300`;
      case 'info':
        return `${baseClasses} bg-blue-500/10 border-blue-500/30 text-blue-300`;
      default:
        return `${baseClasses} bg-gray-500/10 border-gray-500/30 text-gray-300`;
    }
  };

  const getIcon = () => {
    const iconClasses = 'w-5 h-5 flex-shrink-0';
    
    switch (type) {
      case 'success':
        return <CheckCircle className={`${iconClasses} text-green-400`} />;
      case 'error':
        return <XCircle className={`${iconClasses} text-red-400`} />;
      case 'warning':
        return <AlertTriangle className={`${iconClasses} text-yellow-400`} />;
      case 'info':
        return <Info className={`${iconClasses} text-blue-400`} />;
      default:
        return <Info className={`${iconClasses} text-gray-400`} />;
    }
  };

  const getIconColor = () => {
    switch (type) {
      case 'success':
        return 'text-green-400';
      case 'error':
        return 'text-red-400';
      case 'warning':
        return 'text-yellow-400';
      case 'info':
        return 'text-blue-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <div className={`${getAlertClasses()} ${className} animate-fadeIn`}>
      <div className="flex items-start">
        {showIcon && (
          <div className="mr-3 mt-0.5">
            {getIcon()}
          </div>
        )}
        
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className="text-sm font-medium mb-1">
              {title}
            </h3>
          )}
          
          <div className="text-sm">
            {message}
          </div>
          
          {actions && (
            <div className="mt-3">
              {actions}
            </div>
          )}
        </div>
        
        {dismissible && onClose && (
          <button
            onClick={onClose}
            className={`ml-3 flex-shrink-0 ${getIconColor()} hover:opacity-75 transition-opacity`}
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
};

export default Alert;
