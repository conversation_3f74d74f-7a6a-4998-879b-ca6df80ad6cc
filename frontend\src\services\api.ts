// Simple API service with error tracking
import { unifiedErrorHandler } from '../utils/unifiedErrorHandler';
import type { QueryParams } from '../types/api';
import { API_CONFIG } from '../config/constants';
// ✅ UNIFIED: Import from centralized types
import type {
  User,
  UserProfile,
  UserActivity,
  UserRegistrationData,
  UserRoleInfo,
  DatabaseRole,
  UserRole,
  PermissionLevel
} from '../types/user';

const API_URL = import.meta.env.VITE_API_URL || API_CONFIG.BASE_URL;

// API Error class for structured error handling
export class ApiError extends Error {
  public status: number;
  public data?: any;

  constructor(message: string, status: number, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

// Extract error message from API response
export function extractErrorMessage(error: any): string {
  if (typeof error === 'string') {
    return error;
  }
  if (error?.message) {
    return error.message;
  }
  if (error?.detail) {
    return error.detail;
  }
  if (error?.error) {
    return error.error;
  }
  return 'An unknown error occurred';
}

// Get CSRF token from cookies or meta tag
export function getCsrfToken(): string | null {
  // Try to get from cookie first
  const cookies = document.cookie.split(';');
  for (const cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'csrftoken') {
      return value;
    }
  }

  // Try to get from meta tag
  const metaTag = document.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
  if (metaTag) {
    return metaTag.content;
  }

  return null;
}

// ✅ CONSOLIDATED: Refresh authentication token using centralized error handling
export async function refreshToken(): Promise<string | null> {
  const refreshTokenValue = localStorage.getItem(REFRESH_TOKEN_KEY);
  if (!refreshTokenValue) {
    return null;
  }

  try {
    // Use direct fetch for token refresh to avoid circular dependency with apiRequest
    const response = await fetch(`${API_URL}/api/auth/token/refresh/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refresh: refreshTokenValue }),
    });

    if (!response.ok) {
      throw new Error('Token refresh failed');
    }

    const data = await response.json();
    const newAccessToken = data.access;

    if (newAccessToken) {
      localStorage.setItem(TOKEN_KEY, newAccessToken);
      return newAccessToken;
    }

    return null;
  } catch (error) {
    console.error('Token refresh error:', error);
    clearAuthTokens();
    return null;
  }
}

// Token keys
const TOKEN_KEY = 'yasmeen_auth_token';
const REFRESH_TOKEN_KEY = 'yasmeen_refresh_token';

// ✅ UNIFIED: User interface now imported from centralized types

// Event interface - Updated to match backend serializer
export interface Event {
  id: number;
  title: string;
  description: string;
  date: string;
  location: string;
  organizer: string;
  organizer_id?: number;
  attendees?: number;
  attendee_count?: number;
  max_attendees?: number;
  is_attending?: boolean;
  is_virtual?: boolean;
  virtual_link?: string;
  image?: string;
  created_at?: string;
  updated_at?: string;
}

// Create event data interface
export interface CreateEventData {
  title: string;
  description: string;
  date: string;
  location: string;
  is_virtual?: boolean;
  virtual_link?: string;
  organizer_id?: number;
}

// ✅ UNIFIED: UserProfile interface now imported from centralized types

// Dashboard stats interfaces
export interface DashboardStats {
  users: {
    total_users: number;
    active_users: number;
    new_users: number;
  };
  events: {
    total_events: number;
    upcoming_events: number;
    new_events: number;
  };
  business_plans: {
    total_plans: number;
    active_plans: number;
    new_plans: number;
  };
}

// Recent activity interface
export interface RecentActivity {
  id: number;
  type: string;
  description: string;
  timestamp: string;
  user?: string;
}

// Membership application interface
export interface MembershipApplication {
  id?: number;
  full_name: string;
  email: string;
  phone: string;
  country: string;
  state: string;
  location: string;
  expertise_areas: string;
  expertise_level: string;
  background: string;
  motivation: string;
  linkedin_profile: string;
  github_profile: string;
  portfolio_url: string;
  status?: string;
  created_at?: string;
}

// Simple token functions
export function getAuthToken(): string | null {
  return localStorage.getItem(TOKEN_KEY);
}

export function getRefreshToken(): string | null {
  return localStorage.getItem(REFRESH_TOKEN_KEY);
}

export function setAuthTokens(accessToken: string, refreshToken: string): void {
  localStorage.setItem(TOKEN_KEY, accessToken);
  localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken);
}

export function clearAuthTokens(): void {
  localStorage.removeItem(TOKEN_KEY);
  localStorage.removeItem(REFRESH_TOKEN_KEY);
}

export function getTokenExpiry(): number | null {
  const token = getAuthToken();
  if (!token) return null;
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // Convert to milliseconds
  } catch {
    return null;
  }
}

export function isTokenValid(): boolean {
  const token = getAuthToken();
  if (!token) return false;
  
  const expiry = getTokenExpiry();
  if (!expiry) return false;
  
  return Date.now() < expiry;
}

// Simple API request function with error tracking - exported for use by other API services
export async function apiRequest<T>(endpoint: string, method: string = 'GET', data?: any): Promise<T> {
  const startTime = Date.now();
  const token = getAuthToken();

  const config: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` }),
    },
  };

  if (data && method !== 'GET') {
    config.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`${API_URL}${endpoint}`, config);
    const duration = Date.now() - startTime;

    // Track slow API calls
    if (duration > 5000) { // 5 seconds threshold
      unifiedErrorHandler.handleError(
        new Error(`Slow API response: ${duration}ms for ${method} ${endpoint}`),
        'api_performance'
      );
    }

    if (!response.ok) {
      const duration = Date.now() - startTime;
      let errorData;
      let errorText;

      try {
        // Clone the response to avoid "body stream already read" error
        const responseClone = response.clone();
        errorText = await responseClone.text();

        // Try to parse as JSON first
        if (errorText) {
          try {
            errorData = JSON.parse(errorText);
          } catch {
            errorData = errorText;
          }
        } else {
          errorData = `HTTP ${response.status}`;
        }
      } catch {
        errorData = `HTTP ${response.status}`;
      }

      // Track API error
      unifiedErrorHandler.handleError(
        new Error(`API Error: ${response.status} ${response.statusText} for ${method} ${endpoint}`),
        'api_error'
      );

      // Handle authentication errors
      if (response.status === 401) {
        console.error('Authentication failed, clearing tokens');
        clearAuthTokens();

        // Dispatch Redux action to clear auth state
        if (typeof window !== 'undefined' && (window as any).__REDUX_STORE__) {
          const store = (window as any).__REDUX_STORE__;
          store.dispatch({ type: 'auth/clearAuth' });
        }

        // Don't redirect automatically, let the component handle it
        // But dispatch a custom event for components to listen to
        if (typeof window !== 'undefined') {
          window.dispatchEvent(new CustomEvent('auth-error', {
            detail: { status: 401, message: 'Authentication required' }
          }));
        }
      }

      const errorMessage = typeof errorData === 'string'
        ? errorData
        : errorData?.message || errorData?.detail || errorData?.error || `HTTP ${response.status}`;

      throw new ApiError(errorMessage, response.status, errorData);
    }

    // For successful responses, parse JSON
    try {
      return await response.json();
    } catch (e) {
      // If JSON parsing fails, return empty object for successful responses
      return {};
    }
  } catch (error) {
    // Handle network errors or other exceptions
    const duration = Date.now() - startTime;
    console.error('API request failed:', error);

    // If this is already an ApiError with a specific message, preserve it
    if (error instanceof ApiError) {
      throw error;
    }

    unifiedErrorHandler.handleError(
      new Error(`Network Error for ${method} ${endpoint}: ${error}`),
      'api_network_error'
    );

    // Provide more helpful error message for network issues
    const errorAny = error as any;
    const isConnectionRefused = errorAny?.message?.includes('fetch') || errorAny?.code === 'ECONNREFUSED';
    const errorMessage = isConnectionRefused
      ? 'Unable to connect to the server. Please ensure the backend server is running on port 8000.'
      : errorAny?.message || 'Network error occurred';

    throw new ApiError(errorMessage, 0, error);
  }
}

// Auth API
export const authAPI = {
  async login(username: string, password: string): Promise<User> {
    // First get JWT tokens
    const tokenResponse = await apiRequest<{ access: string; refresh: string }>('/api/auth/token/', 'POST', {
      username,
      password,
    });

    console.log('🔍 authAPI.login tokenResponse:', tokenResponse);

    // Set tokens first
    setAuthTokens(tokenResponse.access, tokenResponse.refresh);

    // Then get user info using the token
    const userResponse = await apiRequest<User>('/api/auth/me/', 'GET');

    console.log('🔍 authAPI.login userResponse:', userResponse);

    return userResponse;
  },

  async logout(): Promise<void> {
    try {
      const refreshToken = getRefreshToken();
      await apiRequest('/api/auth/logout/', 'POST', {
        refresh: refreshToken
      });
    } catch (error) {
      // Log the error but don't throw it - logout should always succeed on frontend
      console.warn('Logout API call failed, but clearing tokens anyway:', error);
    } finally {
      clearAuthTokens();
    }
  },

  // Basic register method removed - only enhanced registration is used

  async registerEnhanced(userData: {
    username: string;
    first_name: string;
    last_name: string;
    email: string;
    password: string;
    password_confirm: string;
    phone?: string;
    location?: string;
    bio?: string;
    company?: string;
    job_title?: string;
    industry?: string;
    website?: string;
    linkedin_url?: string;
    language?: string;
    selected_role?: string;
    role_additional_info?: any;
  }): Promise<User> {
    try {
      const response = await apiRequest<{
        access?: string;
        refresh?: string;
        user: User;
        message?: string;
        status?: string;
        requires_approval?: boolean;
      }>('/api/auth/register-enhanced/', 'POST', userData);

      // Only set tokens if they exist (user is approved)
      if (response.access && response.refresh) {
        setAuthTokens(response.access, response.refresh);
      }

      // Log additional response information for debugging
      if (response.message) {
        console.log('Registration message:', response.message);
      }
      if (response.status) {
        console.log('Registration status:', response.status);
      }

      return response.user;
    } catch (error: any) {
      // Handle validation errors from backend
      if (error.status === 400 && error.data?.errors) {
        throw new Error(Object.values(error.data.errors).flat().join(', '));
      }
      throw error;
    }
  },

  async getCurrentUser(): Promise<User> {
    return apiRequest<User>('/api/auth/user/');
  },

  async refreshToken(): Promise<boolean> {
    const refreshToken = getRefreshToken();
    if (!refreshToken) {
      console.log('❌ No refresh token available');
      return false;
    }

    try {
      console.log('🔄 Attempting token refresh...');
      const response = await apiRequest<{ access: string }>('/api/auth/refresh/', 'POST', {
        refresh: refreshToken,
      });

      if (response.access) {
        setAuthTokens(response.access, refreshToken);
        console.log('✅ Token refreshed successfully');
        return true;
      } else {
        console.log('❌ Token refresh failed: No access token in response');
        clearAuthTokens();
        return false;
      }
    } catch (error) {
      console.log('❌ Token refresh failed:', error);
      clearAuthTokens();
      return false;
    }
  },
};

// Paginated response interface
interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Events API
export const eventsAPI = {
  async getEvents(params?: QueryParams): Promise<PaginatedResponse<Event>> {
    const queryString = params ? new URLSearchParams(params as any).toString() : '';
    const url = queryString ? `/api/events/?${queryString}` : '/api/events/';
    const response = await apiRequest<PaginatedResponse<Event>>(url);

    // Ensure we always return a paginated response structure
    if (response && typeof response === 'object' && 'results' in response) {
      return response;
    }

    // If response is an array, wrap it in pagination structure
    if (Array.isArray(response)) {
      return {
        count: response.length,
        next: null,
        previous: null,
        results: response
      };
    }

    // Default empty response
    return {
      count: 0,
      next: null,
      previous: null,
      results: []
    };
  },

  async createEvent(eventData: CreateEventData): Promise<Event> {
    return apiRequest<Event>('/api/events/', 'POST', eventData);
  },

  async updateEvent(eventId: number, eventData: Partial<CreateEventData>): Promise<Event> {
    return apiRequest<Event>(`/api/events/${eventId}/`, 'PUT', eventData);
  },

  async deleteEvent(eventId: number): Promise<void> {
    return apiRequest<void>(`/api/events/${eventId}/`, 'DELETE');
  },

  async attendEvent(eventId: number): Promise<void> {
    return apiRequest<void>(`/api/events/${eventId}/attend/`, 'POST');
  },

  async unattendEvent(eventId: number): Promise<void> {
    return apiRequest<void>(`/api/events/${eventId}/unattend/`, 'POST');
  },
};

// Admin API
export const adminAPI = {
  async getAllStats(): Promise<DashboardStats> {
    return apiRequest<DashboardStats>('/api/admin/stats/');
  },

  async getUsers(): Promise<User[]> {
    const response = await apiRequest<PaginatedResponse<User>>('/api/admin/users/');
    // Handle both paginated and direct array responses
    if (response && typeof response === 'object' && 'results' in response) {
      return response.results;
    }
    // Fallback for direct array responses
    return Array.isArray(response) ? response : [];
  },

  async createUser(userData: Partial<User>): Promise<User> {
    return apiRequest<User>('/api/admin/users/', 'POST', userData);
  },

  async updateUser(userId: number, userData: Partial<User>): Promise<User> {
    return apiRequest<User>(`/api/admin/users/${userId}/`, 'PATCH', userData);
  },

  async updateUserProfile(userId: number, profileData: Partial<UserProfile>): Promise<UserProfile> {
    return apiRequest<UserProfile>(`/api/admin/users/${userId}/profile/`, 'PATCH', profileData);
  },

  async deleteUser(userId: number): Promise<void> {
    return apiRequest<void>(`/api/admin/users/${userId}/`, 'DELETE');
  },

  async getRecentActivity(): Promise<RecentActivity[]> {
    const response = await apiRequest<PaginatedResponse<RecentActivity>>('/api/admin/activity/');
    // Handle both paginated and direct array responses
    if (response && typeof response === 'object' && 'results' in response) {
      return response.results;
    }
    // Fallback for direct array responses
    return Array.isArray(response) ? response : [];
  },

  // Individual stats methods for backward compatibility
  async getUserStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.users;
  },

  async getEventStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.events;
  },

  async getPostStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.posts;
  },

  // Dashboard Analytics
  async getDashboardStats() {
    return apiRequest('/api/users/admin/dashboard/stats/', 'GET');
  },

  async getSystemHealth() {
    return apiRequest('/api/ai/analytics/admin/dashboard/', 'GET');
  },

  async getUserAnalytics(timeRange: string = '30d') {
    return apiRequest(`/api/admin/analytics/users/?range=${timeRange}`, 'GET');
  },

  async getActivityLogs(limit: number = 10) {
    // For now, return mock data since this endpoint doesn't exist yet
    return Promise.resolve({
      logs: [
        {
          id: 1,
          action: 'User Login',
          user: 'admin',
          timestamp: new Date().toISOString(),
          details: 'Successful login'
        },
        {
          id: 2,
          action: 'AI Query',
          user: 'user1',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          details: 'Business planning query'
        }
      ]
    });
  },

  // User Management
  async getPendingApprovals() {
    return apiRequest('/api/admin/approvals/pending/', 'GET');
  },

  async getMembershipApplications() {
    return apiRequest('/api/admin/applications/membership/', 'GET');
  },

  async getResourceStats(): Promise<any> {
    const stats = await this.getAllStats();
    return stats.resources;
  },

  // Content Management
  async getContent(): Promise<any[]> {
    try {
      const response = await apiRequest<PaginatedResponse<any>>('/admin/content/');
      return Array.isArray(response) ? response : response?.results || [];
    } catch (error) {
      console.error('Error fetching content:', error);
      // Return mock data as fallback
      return [
        {
          id: 1,
          title: 'دليل ريادة الأعمال',
          type: 'article',
          author: 'فريق المحتوى',
          status: 'published',
          views: 1250,
          created_at: '2025-07-20',
          updated_at: '2025-07-22'
        },
        {
          id: 2,
          title: 'كيفية الحصول على التمويل',
          type: 'video',
          author: 'خبير التمويل',
          status: 'review',
          views: 890,
          created_at: '2025-07-18',
          updated_at: '2025-07-24'
        },
        {
          id: 3,
          title: 'ورشة عمل الابتكار',
          type: 'event',
          author: 'منظم الفعاليات',
          status: 'flagged',
          views: 456,
          created_at: '2025-07-15',
          updated_at: '2025-07-25'
        },
        {
          id: 4,
          title: 'نصائح للمستثمرين',
          type: 'guide',
          author: 'مستشار الاستثمار',
          status: 'published',
          views: 2100,
          created_at: '2025-07-10',
          updated_at: '2025-07-20'
        }
      ];
    }
  },

  async createContent(contentData: any): Promise<any> {
    return apiRequest<any>('/admin/content/', 'POST', contentData);
  },

  async updateContent(contentId: number, contentData: any): Promise<any> {
    return apiRequest<any>(`/admin/content/${contentId}/`, 'PATCH', contentData);
  },

  async deleteContent(contentId: number): Promise<void> {
    return apiRequest<void>(`/admin/content/${contentId}/`, 'DELETE');
  },
};

// ✅ UNIFIED: UserActivity interface now imported from centralized types

// User API
export const userAPI = {
  async getUserProfile(userId: string): Promise<User> {
    return apiRequest<User>(`/api/users/users/${userId}/`);
  },

  async getUserActivity(): Promise<UserActivity> {
    return apiRequest<UserActivity>('/api/users/users/user_activity/');
  },

  async getForumActivity(): Promise<any> {
    return apiRequest<any>('/api/users/users/forum_activity/');
  },

  async updateProfile(profileData: any): Promise<User> {
    return apiRequest<User>('/api/users/users/me/', 'PATCH', profileData);
  },

  // User Approval API
  async getUserApprovals(): Promise<any[]> {
    const response = await apiRequest<PaginatedResponse<any>>('/api/users/approvals/');
    return response.results || [];
  },

  async getUserApproval(id: string): Promise<any> {
    return apiRequest<any>(`/api/users/approvals/${id}/`);
  },

  async approveUser(id: string): Promise<any> {
    return apiRequest<any>(`/api/users/approvals/${id}/approve/`, 'POST');
  },

  async rejectUser(id: string, reason: string): Promise<any> {
    return apiRequest<any>(`/api/users/approvals/${id}/reject/`, 'POST', { reason });
  },
};

// ✅ REMOVED: Duplicate incubatorAPI - use specific APIs from incubatorApi.ts instead
// Use: businessIdeasAPI, mentorProfilesAPI, fundingOpportunitiesAPI from incubatorApi.ts

// Membership API
export const membershipAPI = {
  async createApplication(applicationData: Omit<MembershipApplication, 'id' | 'status' | 'created_at'>): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>('/membership/applications/', 'POST', applicationData);
  },

  async getApplications(): Promise<MembershipApplication[]> {
    const response = await apiRequest<PaginatedResponse<MembershipApplication>>('/membership/applications/');
    // Handle both paginated and direct array responses
    if (response && typeof response === 'object' && 'results' in response) {
      return response.results;
    }
    // Fallback for direct array responses
    return Array.isArray(response) ? response : [];
  },

  async getApplication(id: number): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>(`/membership/applications/${id}/`);
  },

  async updateApplicationStatus(id: number, status: string): Promise<MembershipApplication> {
    return apiRequest<MembershipApplication>(`/membership/applications/${id}/`, 'PATCH', { status });
  },
};

// Posts API
export const postsAPI = {
  async getPosts(): Promise<any[]> {
    const response = await apiRequest<PaginatedResponse<any>>('/api/posts/');
    // Handle both paginated and direct array responses
    if (response && typeof response === 'object' && 'results' in response) {
      return response.results;
    }
    // Fallback for direct array responses
    return Array.isArray(response) ? response : [];
  },

  async getPost(id: string): Promise<any> {
    return apiRequest<any>(`/api/posts/${id}/`);
  },

  async createPost(postData: any): Promise<any> {
    return apiRequest<any>('/api/posts/', 'POST', postData);
  },

  async updatePost(id: string, postData: any): Promise<any> {
    return apiRequest<any>(`/api/posts/${id}/`, 'PUT', postData);
  },

  async deletePost(id: string): Promise<void> {
    return apiRequest<void>(`/api/posts/${id}/`, 'DELETE');
  },
};

// Search interfaces
export interface SearchResult {
  id: string;
  title: string;
  description: string;
  type: 'business_idea' | 'business_plan' | 'template' | 'post' | 'user';
  url?: string;
  score?: number;
  metadata?: Record<string, any>;
}

export interface SearchFilters {
  type?: string[];
  category?: string[];
  dateRange?: {
    start?: string;
    end?: string;
  };
  tags?: string[];
  author?: string;
  status?: string[];
}

export interface SearchResponse {
  results: SearchResult[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// Resource interface
export interface Resource {
  id: number;
  title: string;
  description: string;
  type: 'document' | 'video' | 'link' | 'template' | 'guide';
  category: string;
  url?: string;
  file_url?: string;
  tags: string[];
  author: string;
  created_at: string;
  updated_at: string;
  downloads: number;
  rating: number;
  is_featured: boolean;
  is_premium: boolean;
}

// Resources API
export const resourcesAPI = {
  async getResources(): Promise<Resource[]> {
    try {
      const response = await apiRequest<PaginatedResponse<Resource>>('/api/resources/');
      // Handle both paginated and direct array responses
      if (response && typeof response === 'object' && 'results' in response) {
        return response.results;
      }
      // Fallback for direct array responses
      return Array.isArray(response) ? response : [];
    } catch (error) {
      console.error('Failed to fetch resources:', error);
      throw error;
    }
  },

  async getResource(id: number): Promise<Resource> {
    try {
      return apiRequest<Resource>(`/api/resources/${id}/`);
    } catch (error) {
      throw new ApiError('Resource not found', 404);
    }
  },

  async createResource(resourceData: Omit<Resource, 'id' | 'created_at' | 'updated_at' | 'downloads' | 'rating'>): Promise<Resource> {
    return apiRequest<Resource>('/api/resources/', 'POST', resourceData);
  },

  async updateResource(id: number, resourceData: Partial<Resource>): Promise<Resource> {
    return apiRequest<Resource>(`/api/resources/${id}/`, 'PATCH', resourceData);
  },

  async deleteResource(id: number): Promise<void> {
    return apiRequest<void>(`/api/resources/${id}/`, 'DELETE');
  },

  async downloadResource(id: number): Promise<void> {
    try {
      const response = await fetch(`${API_URL}/resources/${id}/download/`, {
        headers: {
          'Authorization': `Bearer ${getAuthToken()}`
        }
      });

      if (!response.ok) {
        throw new ApiError('Download failed', response.status);
      }

      // Trigger download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `resource-${id}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.warn('Download not available:', error);
    }
  }
};

// Comments API
export const commentsAPI = {
  async getComments(postId?: number): Promise<any[]> {
    const url = postId ? `/comments/?post=${postId}` : '/comments/';
    const response = await apiRequest<PaginatedResponse<any>>(url);
    return response.results || [];
  },

  async createComment(commentData: any): Promise<any> {
    return apiRequest<any>('/comments/', 'POST', commentData);
  },

  async updateComment(commentId: number, commentData: any): Promise<any> {
    return apiRequest<any>(`/comments/${commentId}/`, 'PATCH', commentData);
  },

  async deleteComment(commentId: number): Promise<void> {
    return apiRequest<void>(`/comments/${commentId}/`, 'DELETE');
  },
};

// Search API
export const searchAPI = {
  async universalSearch(
    query: string,
    filters: SearchFilters = {},
    limit: number = 20
  ): Promise<SearchResponse> {
    try {
      const params = new URLSearchParams({
        q: query,
        limit: limit.toString(),
        ...Object.entries(filters).reduce((acc, [key, value]) => {
          if (value !== undefined && value !== null) {
            acc[key] = Array.isArray(value) ? value.join(',') : String(value);
          }
          return acc;
        }, {} as Record<string, string>)
      });

      return apiRequest<SearchResponse>(`/search/?${params.toString()}`);
    } catch (error) {
      console.error('Failed to perform search:', error);
      throw error;
    }
  },

  async searchBusinessIdeas(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['business_idea'] });
    return response.results;
  },

  async searchTemplates(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['template'] });
    return response.results;
  },

  async searchPosts(query: string, filters: SearchFilters = {}): Promise<SearchResult[]> {
    const response = await this.universalSearch(query, { ...filters, type: ['post'] });
    return response.results;
  }
};



// Generic API functions
export const api = {
  get: <T>(endpoint: string) => apiRequest<T>(endpoint, 'GET'),
  post: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'POST', data),
  put: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'PUT', data),
  patch: <T>(endpoint: string, data?: any) => apiRequest<T>(endpoint, 'PATCH', data),
  delete: <T>(endpoint: string) => apiRequest<T>(endpoint, 'DELETE'),
};

export default api;