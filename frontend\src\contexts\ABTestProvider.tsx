// A/B Test React Context Provider
import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { 
  ABTestExperiment, 
  ABTestVariant, 
  ABTestAssignment, 
  ABTestContextValue,
  ABTestProviderProps 
} from '../types/abtest';
import { abTestManager } from '../services/ABTestManager';

const ABTestContext = createContext<ABTestContextValue | undefined>(undefined);

export const ABTestProvider: React.FC<ABTestProviderProps> = ({
  children,
  userId,
  sessionId
}) => {
  const [experiments, setExperiments] = useState<ABTestExperiment[]>([]);
  const [assignments, setAssignments] = useState<Record<string, ABTestAssignment>>({});
  const [isLoading, setIsLoading] = useState(true);

  // Initialize and load experiments
  useEffect(() => {
    const loadExperiments = async () => {
      try {
        setIsLoading(true);
        
        // Load all experiments
        const allExperiments = abTestManager.getAllExperiments();
        setExperiments(allExperiments);
        
        // Load user assignments
        const userAssignments: Record<string, ABTestAssignment> = {};
        allExperiments.forEach(exp => {
          const assignment = abTestManager.getUserAssignment(exp.id);
          if (assignment) {
            userAssignments[exp.id] = assignment;
          }
        });
        setAssignments(userAssignments);
        
      } catch (error) {
        console.error('Error loading A/B test experiments:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadExperiments();
  }, [userId, sessionId]);

  // Get variant for a specific experiment
  const getVariant = useCallback((experimentId: string): ABTestVariant | null => {
    try {
      return abTestManager.getVariant(experimentId);
    } catch (error) {
      console.error(`Error getting variant for experiment ${experimentId}:`, error);
      return null;
    }
  }, []);

  // Track event for a specific experiment
  const trackEvent = useCallback((
    experimentId: string, 
    eventName: string, 
    properties?: Record<string, any>
  ): void => {
    try {
      abTestManager.trackEvent(experimentId, eventName, properties);
    } catch (error) {
      console.error(`Error tracking event ${eventName} for experiment ${experimentId}:`, error);
    }
  }, []);

  // Track conversion for a specific experiment
  const trackConversion = useCallback((
    experimentId: string, 
    conversionValue?: number
  ): void => {
    try {
      abTestManager.trackEvent(experimentId, 'conversion', { 
        value: conversionValue,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error(`Error tracking conversion for experiment ${experimentId}:`, error);
    }
  }, []);

  const contextValue: ABTestContextValue = {
    experiments,
    assignments,
    getVariant,
    trackEvent,
    trackConversion,
    isLoading
  };

  return (
    <ABTestContext.Provider value={contextValue}>
      {children}
    </ABTestContext.Provider>
  );
};

// Custom hook to use A/B test context
export const useABTestContext = (): ABTestContextValue => {
  const context = useContext(ABTestContext);
  if (context === undefined) {
    throw new Error('useABTestContext must be used within an ABTestProvider');
  }
  return context;
};

export default ABTestProvider;
