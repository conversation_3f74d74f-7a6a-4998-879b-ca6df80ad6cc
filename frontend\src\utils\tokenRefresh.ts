/**
 * Token refresh utility for handling JWT token expiration
 */

interface TokenRefreshResponse {
  access: string;
  refresh?: string;
}

interface TokenInfo {
  exp: number;
  user_id: number;
  username: string;
}

/**
 * Decode JWT token to get expiration time
 */
function decodeJWT(token: string): TokenInfo | null {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    console.error('❌ Error decoding JWT token:', error);
    return null;
  }
}

/**
 * Check if token is expired or will expire soon (within 5 minutes)
 */
function isTokenExpired(token: string): boolean {
  const decoded = decodeJWT(token);
  if (!decoded) return true;
  
  const now = Math.floor(Date.now() / 1000);
  const bufferTime = 5 * 60; // 5 minutes buffer
  
  return decoded.exp < (now + bufferTime);
}

/**
 * Refresh the access token using the refresh token
 */
async function refreshAccessToken(): Promise<string | null> {
  try {
    const refreshToken = localStorage.getItem('yasmeen_refresh_token');
    if (!refreshToken) {
      console.warn('⚠️ No refresh token available');
      return null;
    }

    console.log('🔄 Attempting to refresh access token...');
    
    const response = await fetch('/api/auth/token/refresh/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        refresh: refreshToken
      })
    });

    if (response.ok) {
      const data: TokenRefreshResponse = await response.json();
      
      // Store the new access token
      localStorage.setItem('yasmeen_auth_token', data.access);
      
      // Update refresh token if provided (token rotation)
      if (data.refresh) {
        localStorage.setItem('yasmeen_refresh_token', data.refresh);
      }
      
      console.log('✅ Access token refreshed successfully');
      return data.access;
    } else {
      console.error('❌ Failed to refresh token:', response.status, response.statusText);
      
      // If refresh fails, clear all tokens and redirect to login
      localStorage.removeItem('yasmeen_auth_token');
      localStorage.removeItem('yasmeen_refresh_token');
      
      // Redirect to login page
      window.location.href = '/login';
      return null;
    }
  } catch (error) {
    console.error('❌ Error refreshing token:', error);
    
    // Clear tokens on error
    localStorage.removeItem('yasmeen_auth_token');
    localStorage.removeItem('yasmeen_refresh_token');
    
    return null;
  }
}

/**
 * Get a valid access token, refreshing if necessary
 */
export async function getValidAccessToken(): Promise<string | null> {
  let accessToken = localStorage.getItem('yasmeen_auth_token');
  
  if (!accessToken) {
    console.warn('⚠️ No access token found');
    return null;
  }
  
  // Check if token is expired or will expire soon
  if (isTokenExpired(accessToken)) {
    console.log('🔄 Access token expired or expiring soon, attempting refresh...');
    accessToken = await refreshAccessToken();
  }
  
  return accessToken;
}

/**
 * Enhanced authenticated fetch that handles token refresh automatically
 */
export async function authenticatedFetchWithRefresh(url: string, options: RequestInit = {}): Promise<Response> {
  // Get a valid token (refresh if needed)
  const token = await getValidAccessToken();

  if (!token) {
    throw new Error('No valid authentication token available');
  }

  // ✅ FIXED: Ensure URL includes the API base URL for relative paths
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL.replace('/api', '')}${url}`;

  // Add authorization header
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${token}`,
    ...options.headers,
  };

  console.log(`🔑 Making authenticated request to ${fullUrl} with token length: ${token.length}`);

  // Make the request
  const response = await fetch(fullUrl, {
    ...options,
    headers,
  });

  // If we get a 401, try to refresh the token once more
  if (response.status === 401) {
    console.log('🔄 Got 401, attempting token refresh...');

    const newToken = await refreshAccessToken();
    if (newToken) {
      // Retry the request with the new token
      const retryHeaders = {
        ...headers,
        'Authorization': `Bearer ${newToken}`,
      };

      console.log(`🔄 Retrying request with refreshed token`);
      return fetch(fullUrl, {
        ...options,
        headers: retryHeaders,
      });
    }
  }

  return response;
}

/**
 * Check if user is authenticated with a valid token
 */
export function isAuthenticated(): boolean {
  const token = localStorage.getItem('yasmeen_auth_token');
  if (!token) return false;
  
  return !isTokenExpired(token);
}

/**
 * Force logout by clearing all tokens
 */
export function forceLogout(): void {
  localStorage.removeItem('yasmeen_auth_token');
  localStorage.removeItem('yasmeen_refresh_token');
  window.location.href = '/login';
}

/**
 * Get token information for debugging
 */
export function getTokenInfo(): { accessToken: TokenInfo | null; isExpired: boolean } {
  const token = localStorage.getItem('yasmeen_auth_token');
  if (!token) {
    return { accessToken: null, isExpired: true };
  }
  
  const decoded = decodeJWT(token);
  return {
    accessToken: decoded,
    isExpired: isTokenExpired(token)
  };
}
