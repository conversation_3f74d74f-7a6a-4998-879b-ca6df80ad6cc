// Custom Hook for A/B Testing
import { useCallback, useEffect, useState } from 'react';
import { ABTestVariant, ABTestHookResult, ABTestConfig } from '../types/abtest';
import { useABTestContext } from '../contexts/ABTestProvider';

export const useABTest = (experimentId: string): ABTestHookResult => {
  const { getVariant, trackEvent, trackConversion, isLoading } = useABTestContext();
  const [variant, setVariant] = useState<ABTestVariant | null>(null);

  // Get variant assignment on mount
  useEffect(() => {
    if (!isLoading) {
      const assignedVariant = getVariant(experimentId);
      setVariant(assignedVariant);
      
      // Track view event when variant is assigned
      if (assignedVariant) {
        trackEvent(experimentId, 'view', {
          variantId: assignedVariant.id,
          timestamp: new Date().toISOString()
        });
      }
    }
  }, [experimentId, getVariant, trackEvent, isLoading]);

  // Track custom event
  const handleTrackEvent = useCallback((
    eventName: string, 
    properties?: Record<string, any>
  ) => {
    if (variant) {
      trackEvent(experimentId, eventName, {
        ...properties,
        variantId: variant.id,
        timestamp: new Date().toISOString()
      });
    }
  }, [experimentId, variant, trackEvent]);

  // Track conversion
  const handleTrackConversion = useCallback((conversionValue?: number) => {
    if (variant) {
      trackConversion(experimentId, conversionValue);
    }
  }, [experimentId, variant, trackConversion]);

  return {
    variant,
    isLoading,
    trackEvent: handleTrackEvent,
    trackConversion: handleTrackConversion
  };
};

// Specialized hooks for common experiments

// Registration step design experiment
export const useRegistrationDesign = () => {
  const { variant, trackEvent, trackConversion, isLoading } = useABTest('reg_step_design_001');
  
  const design = variant?.config?.design || 'original';
  
  const trackStepView = useCallback((step: number) => {
    trackEvent('step_view', { step });
  }, [trackEvent]);
  
  const trackStepComplete = useCallback((step: number) => {
    trackEvent('step_complete', { step });
  }, [trackEvent]);
  
  const trackRegistrationComplete = useCallback(() => {
    trackConversion();
  }, [trackConversion]);

  return {
    design,
    isLoading,
    trackStepView,
    trackStepComplete,
    trackRegistrationComplete
  };
};

// Button text experiment
export const useButtonText = () => {
  const { variant, trackEvent, isLoading } = useABTest('reg_button_text_001');
  
  const buttonText = variant?.config?.buttonText || 'Create Account';
  
  const trackButtonClick = useCallback(() => {
    trackEvent('button_click', { buttonText });
  }, [trackEvent, buttonText]);

  return {
    buttonText,
    isLoading,
    trackButtonClick
  };
};

// Generic configuration hook
export const useABTestConfig = <T extends keyof ABTestConfig>(
  experimentId: string,
  configKey: T,
  defaultValue: ABTestConfig[T]
): {
  value: ABTestConfig[T];
  isLoading: boolean;
  trackEvent: (eventName: string, properties?: Record<string, any>) => void;
  trackConversion: (conversionValue?: number) => void;
} => {
  const { variant, trackEvent, trackConversion, isLoading } = useABTest(experimentId);
  
  const value = variant?.config?.[configKey] ?? defaultValue;
  
  return {
    value,
    isLoading,
    trackEvent,
    trackConversion
  };
};

// Multi-variant hook for complex experiments
export const useMultiVariantTest = (experimentIds: string[]) => {
  const [variants, setVariants] = useState<Record<string, ABTestVariant | null>>({});
  const [loading, setLoading] = useState(true);
  const { getVariant, trackEvent, trackConversion } = useABTestContext();

  useEffect(() => {
    const loadVariants = async () => {
      const variantMap: Record<string, ABTestVariant | null> = {};
      
      experimentIds.forEach(id => {
        variantMap[id] = getVariant(id);
      });
      
      setVariants(variantMap);
      setLoading(false);
    };

    loadVariants();
  }, [experimentIds, getVariant]);

  const trackEventForExperiment = useCallback((
    experimentId: string,
    eventName: string,
    properties?: Record<string, any>
  ) => {
    const variant = variants[experimentId];
    if (variant) {
      trackEvent(experimentId, eventName, {
        ...properties,
        variantId: variant.id
      });
    }
  }, [variants, trackEvent]);

  const trackConversionForExperiment = useCallback((
    experimentId: string,
    conversionValue?: number
  ) => {
    if (variants[experimentId]) {
      trackConversion(experimentId, conversionValue);
    }
  }, [variants, trackConversion]);

  return {
    variants,
    isLoading: loading,
    trackEvent: trackEventForExperiment,
    trackConversion: trackConversionForExperiment
  };
};

// Feature flag hook (simple on/off experiments)
export const useFeatureFlag = (flagName: string, defaultValue: boolean = false) => {
  const { variant, trackEvent, isLoading } = useABTest(flagName);
  
  const isEnabled = variant?.config?.enabled ?? defaultValue;
  
  const trackFeatureUsage = useCallback((action: string, properties?: Record<string, any>) => {
    trackEvent('feature_usage', {
      action,
      enabled: isEnabled,
      ...properties
    });
  }, [trackEvent, isEnabled]);

  return {
    isEnabled,
    isLoading,
    trackFeatureUsage
  };
};

export default useABTest;
