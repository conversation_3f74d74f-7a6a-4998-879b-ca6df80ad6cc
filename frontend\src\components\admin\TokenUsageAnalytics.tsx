/**
 * 🎯 Token Usage Analytics
 * Detailed analysis of token consumption and costs
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell
} from 'recharts';
import {
  DollarSign, TrendingUp, Zap, Target,
  AlertTriangle, Download, RefreshCw
} from 'lucide-react';
import { aiAnalyticsService, TokenUsageAnalytics } from '@/services/aiAnalyticsService';

// ========================================
// TOKEN METRICS OVERVIEW
// ========================================

const TokenMetricsOverview: React.FC<{ analytics: TokenUsageAnalytics }> = ({ analytics }) => {
  const formatNumber = (num: number) => num.toLocaleString();
  const formatCurrency = (amount: number) => `$${amount.toFixed(6)}`;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Tokens */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الـ Tokens</CardTitle>
          <Zap className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatNumber(analytics.total_tokens)}</div>
          <p className="text-xs text-muted-foreground">
            {formatNumber(analytics.input_tokens)} دخل / {formatNumber(analytics.output_tokens)} خرج
          </p>
        </CardContent>
      </Card>

      {/* Total Cost */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">التكلفة الإجمالية</CardTitle>
          <DollarSign className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCurrency(analytics.total_cost_usd)}</div>
          <p className="text-xs text-muted-foreground">
            {formatCurrency(analytics.total_cost_usd / 30)} متوسط يومي
          </p>
        </CardContent>
      </Card>

      {/* Average per Message */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">متوسط لكل رسالة</CardTitle>
          <Target className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatNumber(Math.round(analytics.average_tokens_per_message))}
          </div>
          <p className="text-xs text-muted-foreground">tokens لكل رسالة</p>
        </CardContent>
      </Card>

      {/* Cost Efficiency */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">كفاءة التكلفة</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatCurrency(analytics.total_cost_usd / analytics.total_tokens * 1000)}
          </div>
          <p className="text-xs text-muted-foreground">لكل 1K tokens</p>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// COST TREND CHART
// ========================================

const CostTrendChart: React.FC<{ analytics: TokenUsageAnalytics }> = ({ analytics }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>اتجاه التكلفة والاستخدام</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={analytics.cost_trend}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis yAxisId="tokens" orientation="left" />
            <YAxis yAxisId="cost" orientation="right" />
            <Tooltip 
              formatter={(value, name) => [
                name === 'tokens' ? value.toLocaleString() : `$${Number(value).toFixed(4)}`,
                name === 'tokens' ? 'Tokens' : 'التكلفة'
              ]}
            />
            <Legend />
            <Bar 
              yAxisId="tokens"
              dataKey="tokens" 
              fill="#8884d8" 
              name="Tokens"
              opacity={0.7}
            />
            <Line 
              yAxisId="cost"
              type="monotone" 
              dataKey="cost" 
              stroke="#ff7300" 
              strokeWidth={3}
              name="التكلفة ($)"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// ========================================
// MODEL BREAKDOWN CHART
// ========================================

const ModelBreakdownChart: React.FC<{ analytics: TokenUsageAnalytics }> = ({ analytics }) => {
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <Card>
      <CardHeader>
        <CardTitle>توزيع النماذج</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={analytics.model_breakdown}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ model, percentage }) => `${model} ${percentage.toFixed(1)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="tokens"
            >
              {analytics.model_breakdown.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value) => [
                value.toLocaleString() + ' tokens',
                'الاستخدام'
              ]}
            />
          </PieChart>
        </ResponsiveContainer>
        
        {/* Model Details Table */}
        <div className="mt-4 space-y-2">
          {analytics.model_breakdown.map((model, index) => (
            <div key={model.model} className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <div className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full" 
                  style={{ backgroundColor: COLORS[index % COLORS.length] }}
                />
                <span className="font-medium">{model.model}</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-semibold">
                  {model.tokens.toLocaleString()} tokens
                </div>
                <div className="text-xs text-muted-foreground">
                  ${model.cost.toFixed(4)}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

// ========================================
// MAIN TOKEN ANALYTICS COMPONENT
// ========================================

const TokenUsageAnalyticsComponent: React.FC = () => {
  const [analytics, setAnalytics] = useState<TokenUsageAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState(30);
  const [groupBy, setGroupBy] = useState<'daily' | 'weekly' | 'monthly'>('daily');

  // Load analytics data
  const loadAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await aiAnalyticsService.getTokenUsageAnalytics({
        days: timeRange,
        group_by: groupBy
      });
      setAnalytics(data);
    } catch (err) {
      setError('فشل في تحميل تحليلات الـ Tokens');
      console.error('Token analytics loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Export data
  const handleExport = async (format: 'csv' | 'json') => {
    try {
      const blob = await aiAnalyticsService.exportData({
        type: 'tokens',
        format,
        days: timeRange
      });
      
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `token-analytics-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (err) {
      console.error('Export error:', err);
    }
  };

  useEffect(() => {
    loadAnalytics();
  }, [timeRange, groupBy]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل تحليلات الـ Tokens...</p>
        </div>
      </div>
    );
  }

  if (error || !analytics) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold">خطأ في التحميل</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadAnalytics}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">⚡ تحليلات الـ Tokens</h1>
          <p className="text-muted-foreground">مراقبة استخدام الـ tokens والتكاليف</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Group By Selector */}
          <select 
            value={groupBy} 
            onChange={(e) => setGroupBy(e.target.value as 'daily' | 'weekly' | 'monthly')}
            className="px-3 py-2 border rounded-md"
          >
            <option value="daily">يومي</option>
            <option value="weekly">أسبوعي</option>
            <option value="monthly">شهري</option>
          </select>

          {/* Time Range Selector */}
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={7}>آخر 7 أيام</option>
            <option value={30}>آخر 30 يوم</option>
            <option value={90}>آخر 3 أشهر</option>
          </select>

          <Button 
            variant="outline" 
            onClick={() => handleExport('csv')}
          >
            <Download className="h-4 w-4 mr-2" />
            تصدير
          </Button>
        </div>
      </div>

      {/* Metrics Overview */}
      <TokenMetricsOverview analytics={analytics} />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <CostTrendChart analytics={analytics} />
        <ModelBreakdownChart analytics={analytics} />
      </div>
    </div>
  );
};

export default TokenUsageAnalyticsComponent;
