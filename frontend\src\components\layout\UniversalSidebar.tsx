/**
 * ✨ FIXED Universal Sidebar Component
 *
 * FEATURES:
 * - 📱 Fully responsive design (mobile-first)
 * - 🌍 Complete Arabic/RTL support
 * - 🎨 Consistent design system (like login page)
 * - 📊 Complete user data display
 * - ⚡ Optimized performance
 * - 🔧 Proper mobile/desktop variants
 * - 🎯 Fixed styling and layout issues
 */

import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../../store/hooks';
import { logout } from '../../store/authSlice';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../../hooks/useLanguage';
import { useUnifiedRoles } from '../../hooks/useUnifiedRoles';
import { getRoleNavigation } from '../../config/roleConfig';
import LanguageSwitcher from '../common/LanguageSwitcher';
import { renderIcon } from '../../utils/optimized-icons';
import { RTLIcon, RTLText, RTLComponent } from '../../components/rtl';
import { Button, Card, Badge, Loading } from '../../components/ui';
import {
  Home, User, Settings, Bot, LogOut, X, Menu,
  Shield, BarChart3, Users, FileText, Calendar,
  TrendingUp, Briefcase, DollarSign, BookOpen,
  Plus, Search, Flag, Folder, UserCheck, Link, Activity,
  CheckCircle, Heart, Eye, Lock, BarChart, Cog,
  Crown, Award, Building, ChevronLeft, ChevronRight
} from '../../utils/optimized-icons';

interface UniversalSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  onToggle?: () => void;
  variant?: 'desktop' | 'mobile';
  isCollapsed?: boolean;
}

// Simple icon mapping
const iconMap: Record<string, React.ComponentType<any>> = {
  Home, User, Settings, Bot, LogOut, Shield, BarChart3, Users,
  FileText, Calendar, TrendingUp, Briefcase, DollarSign, BookOpen,
  Plus, Search, Flag, Folder, UserCheck, Link, Activity,
  CheckCircle, Heart, Eye, Lock, BarChart, Cog,
  // Icon aliases for consistency
  CheckCircleIcon: CheckCircle,
  HeartIcon: Heart,
  EyeIcon: Eye,
  LockClosedIcon: Lock,
  ChartBarIcon: BarChart3,
  ChartPieIcon: BarChart,
  CogIcon: Cog,
  UsersIcon: Users,
  UserIcon: User,
  DocumentTextIcon: FileText,
  LinkIcon: Link
};

const UniversalSidebar: React.FC<UniversalSidebarProps> = ({
  isOpen = false,
  onClose,
  onToggle,
  variant = 'desktop',
  isCollapsed = false
}) => {
  // ✅ ALL HOOKS MUST BE CALLED FIRST (Rules of Hooks)
  const { user } = useAppSelector(state => state.auth);
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();
  const { isRTL, changeLanguage } = useLanguage();

  // ✅ UNIFIED ROLE INTEGRATION
  const {
    primaryRole,
    shouldShowSidebar,
    sidebarType,
    accessibleRoutes,
    canAccess
  } = useUnifiedRoles();

  // ✅ LOADING STATE
  const [isLoading, setIsLoading] = React.useState(false);

  // Get role-specific navigation
  const navigation = React.useMemo(() => {
    try {
      const nav = getRoleNavigation(primaryRole);
      console.log(`🔍 Navigation for ${primaryRole}:`, nav);
      return nav;
    } catch (error) {
      console.error(`❌ Error getting navigation for ${primaryRole}:`, error);
      return [];
    }
  }, [primaryRole]);

  // ✅ DETERMINE EFFECTIVE ROLE FROM URL CONTEXT
  const effectiveRole = React.useMemo(() => {
    // Extract role from current URL path (e.g., /admin/dashboard -> admin)
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const urlRole = pathSegments[0];

    // Valid roles that can be used in URLs (consolidated - removed super_admin)
    const validUrlRoles = ['user', 'entrepreneur', 'mentor', 'investor', 'moderator', 'admin'];

    // If URL contains a valid role, use it; otherwise fall back to primaryRole
    if (validUrlRoles.includes(urlRole)) {
      return urlRole;
    }

    return primaryRole || 'user';
  }, [location.pathname, primaryRole]);

  // Helper function to get navigation item name
  const getNavigationName = (item: any, isRTL: boolean): string => {
    if (isRTL && item.labelAr) return item.labelAr;
    if (isRTL && item.nameAr) return item.nameAr;
    if (item.label) return item.label;
    if (item.name) return item.name;
    return item.id || 'Unknown';
  };

  // ✅ NAVIGATION ITEMS (moved to top to fix hooks order)
  const navItems = React.useMemo(() => {
    try {
      // Get navigation items from the role configuration
      const items = navigation || getRoleNavigation(effectiveRole);
      console.log(`🔍 Raw navigation items for ${effectiveRole}:`, items);

      // Filter and transform items - ADMIN GETS ALL ADMIN ITEMS
      const filteredItems = items
        .filter(item => {
          // ✅ CONSOLIDATED: For admin, show ALL admin-specific items
          if (effectiveRole === 'admin') {
            // Admin gets access to all items without capability checks
            console.log(`🔍 Admin access granted for: ${item.id}`);
            return true;
          }

          // For other roles, check capability if required
          if (item.requiredCapability) {
            const hasCapability = canAccess(item.requiredCapability);
            console.log(`🔍 Capability check for ${item.id}: ${item.requiredCapability} = ${hasCapability}`);
            return hasCapability;
          }
          // Otherwise, allow the item (basic navigation items)
          return true;
        })
        .map(item => ({
          ...item,
          name: getNavigationName(item, isRTL),
          allowedRoles: [effectiveRole] // Add allowedRoles for compatibility
        }));

      console.log(`🔍 Filtered navigation items for ${effectiveRole}:`, filteredItems);
      return filteredItems;
    } catch (error) {
      console.error('❌ Error getting sidebar navigation:', error);
      // Fallback navigation with proper translations and role filtering

      if (effectiveRole === 'user') {
        return [{
          id: 'home',
          name: isRTL ? 'الرئيسية' : 'Home',
          nameAr: 'الرئيسية',
          path: '/user/home',
          icon: 'Home',
          allowedRoles: ['user'],
          category: 'main' as const
        }];
      }

      if (effectiveRole === 'admin') {
        // Admin gets comprehensive fallback navigation
        return [
          {
            id: 'dashboard',
            name: isRTL ? 'لوحة التحكم' : 'Dashboard',
            nameAr: 'لوحة التحكم',
            path: '/admin/dashboard',
            icon: 'Home',
            allowedRoles: ['admin'],
            category: 'main' as const
          },
          {
            id: 'users',
            name: isRTL ? 'إدارة المستخدمين' : 'User Management',
            nameAr: 'إدارة المستخدمين',
            path: '/admin/users',
            icon: 'Users',
            allowedRoles: ['admin'],
            category: 'admin' as const
          },
          {
            id: 'analytics',
            name: isRTL ? 'التحليلات' : 'Analytics',
            nameAr: 'التحليلات',
            path: '/admin/analytics',
            icon: 'BarChart3',
            allowedRoles: ['admin'],
            category: 'admin' as const
          },
          {
            id: 'system',
            name: isRTL ? 'إدارة النظام' : 'System Management',
            nameAr: 'إدارة النظام',
            path: '/admin/system',
            icon: 'Settings',
            allowedRoles: ['admin'],
            category: 'admin' as const
          }
        ];
      }

      // Other roles get basic dashboard
      return [{
        id: 'dashboard',
        name: isRTL ? 'لوحة التحكم' : 'Dashboard',
        nameAr: 'لوحة التحكم',
        path: `/${effectiveRole}/dashboard`,
        icon: 'Home',
        allowedRoles: ['admin', 'mentor', 'investor', 'entrepreneur', 'moderator'],
        category: 'main' as const
      }].filter(item => item.allowedRoles && item.allowedRoles.includes(effectiveRole));
    }
  }, [effectiveRole, isRTL]);

  // ✅ KEYBOARD SUPPORT (moved to top to fix hooks order)
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (variant === 'mobile' && isOpen && event.key === 'Escape') {
        onClose?.();
      }
    };

    if (variant === 'mobile' && isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [variant, isOpen, onClose]);

  // ✅ LOADING STATE HANDLING (after all hooks)
  if (isLoading) {
    // Show loading skeleton while role data is being fetched
    return variant === 'mobile' ? (
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40 flex items-center justify-center">
        <div className="text-white flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          {isRTL ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    ) : (
      <div className="w-72 h-screen bg-gradient-to-br from-indigo-950 to-purple-950 shadow-2xl flex items-center justify-center">
        <div className="text-white flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          {isRTL ? 'جاري التحميل...' : 'Loading...'}
        </div>
      </div>
    );
  }

  // ✅ ERROR STATE HANDLING - Removed undefined error reference
  // Navigation errors are handled in the useMemo catch block above
  // The sidebar will continue with fallback navigation items

  // Get role-specific styling
  const getRoleTheme = (role: string) => {
    const themes = {
      super_admin: {
        gradient: 'from-gray-900 via-red-900 to-purple-900',
        icon: '👑',
        badge: 'SUPER ADMIN',
        accent: 'red'
      },
      admin: {
        gradient: 'from-gray-900 via-purple-900 to-violet-900',
        icon: '⚡',
        badge: 'ADMIN',
        accent: 'purple'
      },
      entrepreneur: {
        gradient: 'from-orange-950 to-red-950',
        icon: '🚀',
        badge: 'ENTREPRENEUR',
        accent: 'orange'
      },
      mentor: {
        gradient: 'from-blue-950 to-indigo-950',
        icon: '🎯',
        badge: 'MENTOR',
        accent: 'blue'
      },
      investor: {
        gradient: 'from-green-950 to-emerald-950',
        icon: '💰',
        badge: 'INVESTOR',
        accent: 'green'
      },
      moderator: {
        gradient: 'from-gray-950 to-slate-950',
        icon: '🛡️',
        badge: 'MODERATOR',
        accent: 'gray'
      },
      user: {
        gradient: 'from-indigo-950 to-purple-950',
        icon: '👤',
        badge: 'USER',
        accent: 'indigo'
      }
    };
    return themes[role as keyof typeof themes] || themes.user;
  };

  const roleTheme = getRoleTheme(effectiveRole);

  // ✅ ARABIC TRANSLATION HELPER - COMPREHENSIVE TRANSLATIONS
  const getArabicTranslation = (key: string, isArabic: boolean) => {
    const translations: Record<string, { ar: string; en: string }> = {
      // Main Navigation
      'Dashboard': { ar: 'لوحة التحكم', en: 'Dashboard' },
      'Profile': { ar: 'الملف الشخصي', en: 'Profile' },
      'Settings': { ar: 'الإعدادات', en: 'Settings' },
      'AI Chat': { ar: 'محادثة الذكاء الاصطناعي', en: 'AI Chat' },

      // Admin & Management
      'Users': { ar: 'المستخدمون', en: 'Users' },
      'User Management': { ar: 'إدارة المستخدمين', en: 'User Management' },
      'System Management': { ar: 'إدارة النظام', en: 'System Management' },
      'Content Management': { ar: 'إدارة المحتوى', en: 'Content Management' },
      'AI Management': { ar: 'إدارة الذكاء الاصطناعي', en: 'AI Management' },

      // Analytics & Reports
      'Reports': { ar: 'التقارير', en: 'Reports' },
      'Analytics': { ar: 'التحليلات', en: 'Analytics' },
      'Market Analysis': { ar: 'تحليل السوق', en: 'Market Analysis' },

      // Communication
      'Messages': { ar: 'الرسائل', en: 'Messages' },
      'Notifications': { ar: 'الإشعارات', en: 'Notifications' },
      'Help': { ar: 'المساعدة', en: 'Help' },

      // Business & Entrepreneurship
      'Business Plans': { ar: 'خطط الأعمال', en: 'Business Plans' },
      'My Projects': { ar: 'مشاريعي', en: 'My Projects' },
      'Opportunities': { ar: 'الفرص', en: 'Opportunities' },
      'Portfolio': { ar: 'المحفظة', en: 'Portfolio' },

      // Mentorship & Learning
      'Mentorship': { ar: 'الإرشاد', en: 'Mentorship' },
      'My Mentees': { ar: 'المتدربون لدي', en: 'My Mentees' },
      'Sessions': { ar: 'الجلسات', en: 'Sessions' },

      // Funding & Investment
      'Funding': { ar: 'التمويل', en: 'Funding' },

      // Community & Events
      'Events': { ar: 'الفعاليات', en: 'Events' },
      'Community': { ar: 'المجتمع', en: 'Community' },
      'Resources': { ar: 'الموارد', en: 'Resources' },
      'Templates': { ar: 'القوالب', en: 'Templates' },
      'Incubator': { ar: 'الحاضنة', en: 'Incubator' },

      // Role translations
      'role_user': { ar: 'مستخدم', en: 'User' },
      'role_admin': { ar: 'مدير', en: 'Admin' },
      'role_super_admin': { ar: 'مدير عام', en: 'Super Admin' },
      'role_mentor': { ar: 'مرشد', en: 'Mentor' },
      'role_investor': { ar: 'مستثمر', en: 'Investor' },
      'role_entrepreneur': { ar: 'رائد أعمال', en: 'Entrepreneur' },
      'role_moderator': { ar: 'مشرف', en: 'Moderator' },

      // System & Misc
      'Logout': { ar: 'تسجيل الخروج', en: 'Logout' },
      'us': { ar: 'نحن', en: 'About Us' },
      'About Us': { ar: 'نحن', en: 'About Us' },
      'Contact': { ar: 'اتصل بنا', en: 'Contact' },
      'Support': { ar: 'الدعم', en: 'Support' }
    };

    const translation = translations[key];
    if (translation) {
      return isArabic ? translation.ar : translation.en;
    }

    // Enhanced fallback - try i18n first, then return original key
    const i18nResult = t(key);
    if (i18nResult && i18nResult !== key) {
      return i18nResult;
    }

    // Final fallback - return original key
    return key;
  };



  // ✅ ENHANCED HANDLERS WITH KEYBOARD SUPPORT
  const handleNavigation = (path: string) => {
    navigate(path);
    if (variant === 'mobile' && onClose) {
      onClose();
    }
  };

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap();
      navigate('/login');
      if (variant === 'mobile' && onClose) {
        onClose();
      }
    } catch (error) {
      console.error('Logout error:', error);
    }
  };



  const isActive = (path: string) => location.pathname === path;

  // ✅ UNIFIED MODERN STYLING - No role-specific colors needed

  // ✅ FIXED: Move useEffect outside conditional to follow Rules of Hooks
  React.useEffect(() => {
    // Only redirect if sidebar should not be shown
    if (!shouldShowSidebar()) {
      const homeRoute = primaryRole === 'user' ? '/user/home' : `/${primaryRole}/dashboard`;
      if (location.pathname !== homeRoute && !location.pathname.startsWith(`/${primaryRole}/`)) {
        navigate(homeRoute);
      }
    }
  }, [location.pathname, navigate, primaryRole, shouldShowSidebar]);

  // ✅ UNIFIED SIDEBAR VISIBILITY CHECK
  if (!shouldShowSidebar()) {
    return null;
  }

  // ✨ ENHANCED MOBILE SIDEBAR - Improved responsiveness and compatibility
  if (variant === 'mobile') {
    return (
      <>
        {/* Enhanced backdrop with consistent styling */}
        {isOpen && (
          <div
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40 transition-opacity duration-300"
            onClick={onClose}
            onKeyDown={(e) => e.key === 'Escape' && onClose?.()}
            role="button"
            tabIndex={0}
            aria-label="Close sidebar"
          />
        )}
        {/* Mobile sidebar - Consistent with login page styling */}
        <div
          className={`fixed top-0 ${isRTL ? 'right-0' : 'left-0'} h-full w-72 sm:w-80 max-w-[90vw]
            bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 backdrop-blur-sm shadow-2xl
            ${isRTL ? 'border-l' : 'border-r'} border-white/20
            transform transition-transform duration-300 ease-in-out z-50
            ${isOpen ? 'translate-x-0' : isRTL ? 'translate-x-full' : '-translate-x-full'}
            overflow-y-auto overscroll-contain
          `}
          id="mobile-sidebar"
          role="navigation"
          aria-label="Mobile navigation"
          aria-hidden={!isOpen}
        >
          <SidebarContent />
        </div>
      </>
    );
  }

  // ✨ ENHANCED DESKTOP SIDEBAR - Consistent with login page styling
  return (
    <div
      className={`${isCollapsed ? 'w-16 sm:w-20' : 'w-64 sm:w-72 lg:w-80'} h-screen fixed ${isRTL ? 'right-0' : 'left-0'} top-0 z-30
        bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 backdrop-blur-sm shadow-2xl
        ${isRTL ? 'border-l' : 'border-r'} border-white/20
        transition-all duration-300 ease-in-out overflow-hidden
      `}
      style={{
        '--sidebar-width': isCollapsed ? '80px' : '320px'
      } as React.CSSProperties}
    >
      <SidebarContent />

      {/* Collapse Toggle - Consistent with login page styling */}
      <button
        onClick={onToggle}
        className={`absolute top-3 sm:top-4 ${isRTL ? 'left-3 sm:left-4' : 'right-3 sm:right-4'}
          min-w-[36px] min-h-[36px] sm:min-w-[40px] sm:min-h-[40px] p-2
          bg-black/30 backdrop-blur-sm hover:bg-black/40
          rounded-lg border border-white/20 hover:border-white/30 transition-all duration-300
          focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent
          touch-manipulation
        `}
        aria-label={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
        title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
      >
        <RTLIcon
          icon={isCollapsed ? (isRTL ? ChevronLeft : ChevronRight) : (isRTL ? ChevronRight : ChevronLeft)}
          size={16}
          className="text-gray-300 hover:text-white transition-colors duration-300"
        />
      </button>
    </div>
  );

  function SidebarContent() {
    return (
      <RTLComponent className="h-full flex flex-col" dir={isRTL ? 'rtl' : 'ltr'}>
        {/* ✅ REMOVED: Admin profile header section as requested */}
        {variant === 'mobile' && (
          <div className="m-4 p-2 flex justify-end">
            <Button variant="ghost" size="sm" onClick={onClose} className="p-2 hover:bg-white/10">
              <RTLIcon icon={X} size={20} />
            </Button>
          </div>
        )}

        {/* ✅ REMOVED: User info section as requested */}

        {/* ✨ NAVIGATION - Consistent with login page styling */}
        <div className="flex-1 px-3 sm:px-4 overflow-y-auto">
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loading size="sm" />
            </div>
          ) : (
            <nav className="space-y-1 sm:space-y-2">
              {navItems.map((item) => {
                const active = isActive(item.path);
                const displayName = item.name;

                return (
                  <button
                    key={item.id}
                    className={`w-full ${isCollapsed ? 'justify-center px-2' : 'justify-start px-3 sm:px-4'}
                      min-h-[44px] sm:min-h-[48px] py-2 sm:py-3 rounded-lg transition-all duration-300 group relative
                      touch-manipulation focus:outline-none focus:ring-2 focus:ring-purple-400 focus:ring-offset-2 focus:ring-offset-transparent
                      ${active
                        ? 'bg-black/30 backdrop-blur-sm text-white shadow-lg border border-white/20'
                        : 'text-gray-300 hover:bg-black/20 hover:backdrop-blur-sm hover:text-white hover:border hover:border-white/10'
                      }
                    `}
                    onClick={() => handleNavigation(item.path)}
                    title={isCollapsed ? displayName : undefined}
                    aria-label={displayName}
                    role="menuitem"
                  >
                    {/* Active indicator - consistent with login page style */}
                    {active && (
                      <div className={`absolute ${isRTL ? 'right-0 rounded-l-md' : 'left-0 rounded-r-md'} top-2 bottom-2 w-1 bg-gradient-to-b from-purple-400 to-blue-400`} />
                    )}

                    <RTLComponent className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3 rtl:space-x-reverse'}`}>
                      <div className={`inline-flex items-center justify-center ${isCollapsed ? 'w-6 h-6' : 'w-5 h-5 sm:w-6 sm:h-6'} transition-all duration-300 group-hover:scale-105`}>
                        {renderIcon(item.icon, {
                          size: isCollapsed ? 24 : 20,
                          className: `transition-all duration-300 ${active ? 'text-purple-400' : 'text-gray-400 group-hover:text-purple-300'}`
                        })}
                      </div>
                      {!isCollapsed && (
                        <RTLText className="font-medium text-sm sm:text-base transition-all duration-300 truncate">
                          {displayName}
                        </RTLText>
                      )}
                    </RTLComponent>
                  </button>
                );
              })}
            </nav>
          )}
        </div>

        {/* ✨ FOOTER SECTION - Consistent with login page styling */}
        <div className="p-3 sm:p-4 space-y-2 sm:space-y-3">
          {/* Language Switcher */}
          <div className="bg-black/30 backdrop-blur-sm rounded-lg p-2 sm:p-3 border border-white/20">
            {isCollapsed ? (
              <div className="flex justify-center">
                <button
                  onClick={() => {
                    const newLang = isRTL ? 'en' : 'ar';
                    changeLanguage(newLang);
                  }}
                  className="p-2 hover:bg-white/10 rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-purple-400"
                  title={isRTL ? 'تغيير اللغة' : 'Change Language'}
                >
                  <span className="text-lg">{isRTL ? '🇸🇦' : '🇺🇸'}</span>
                </button>
              </div>
            ) : (
              <LanguageSwitcher variant="compact" className="w-full" />
            )}
          </div>

          {/* Logout Button */}
          <button
            onClick={handleLogout}
            className={`w-full ${isCollapsed ? 'justify-center px-2 py-2' : 'justify-start px-3 py-2 sm:py-3'}
              min-h-[40px] sm:min-h-[44px] text-red-400 hover:text-red-300
              bg-black/30 backdrop-blur-sm hover:bg-red-500/10 rounded-lg
              transition-all duration-300 border border-white/20 hover:border-red-400/30
              touch-manipulation focus:outline-none focus:ring-2 focus:ring-red-400 focus:ring-offset-2 focus:ring-offset-transparent
            `}
            title={getArabicTranslation('Logout', isRTL)}
            aria-label={getArabicTranslation('Logout', isRTL)}
          >
            <RTLComponent className={`flex items-center ${isCollapsed ? 'justify-center' : 'space-x-3 rtl:space-x-reverse'}`}>
              <RTLIcon icon={LogOut} size={18} className="transition-all duration-300 group-hover:scale-105" />
              {!isCollapsed && (
                <RTLText className="font-medium text-sm sm:text-base">
                  {getArabicTranslation('Logout', isRTL)}
                </RTLText>
              )}
            </RTLComponent>
          </button>
        </div>
      </RTLComponent>
    );
  }
};

export default UniversalSidebar;
