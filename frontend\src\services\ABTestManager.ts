// A/B Test Manager Service
import { 
  ABTestExperiment, 
  ABTestVariant, 
  ABTestAssignment, 
  ABTestEvent,
  ABTestMetrics,
  ABTestResults,
  StatisticalTest
} from '../types/abtest';

class ABTestManager {
  private experiments: Map<string, ABTestExperiment> = new Map();
  private assignments: Map<string, ABTestAssignment> = new Map();
  private events: ABTestEvent[] = [];
  private userId: string;
  private sessionId: string;

  constructor() {
    this.userId = this.getUserId();
    this.sessionId = this.getSessionId();
    this.loadFromStorage();
    this.initializeDefaultExperiments();
  }

  // User and session management
  private getUserId(): string {
    let userId = localStorage.getItem('abtest_user_id');
    if (!userId) {
      userId = 'user_' + Math.random().toString(36).substr(2, 9);
      localStorage.setItem('abtest_user_id', userId);
    }
    return userId;
  }

  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('abtest_session_id');
    if (!sessionId) {
      sessionId = 'session_' + Math.random().toString(36).substr(2, 9);
      sessionStorage.setItem('abtest_session_id', sessionId);
    }
    return sessionId;
  }

  // Storage management
  private loadFromStorage(): void {
    try {
      const experimentsData = localStorage.getItem('abtest_experiments');
      if (experimentsData) {
        const experiments = JSON.parse(experimentsData);
        experiments.forEach((exp: ABTestExperiment) => {
          this.experiments.set(exp.id, {
            ...exp,
            startDate: new Date(exp.startDate),
            endDate: exp.endDate ? new Date(exp.endDate) : undefined,
            createdAt: new Date(exp.createdAt),
            updatedAt: new Date(exp.updatedAt)
          });
        });
      }

      const assignmentsData = localStorage.getItem('abtest_assignments');
      if (assignmentsData) {
        const assignments = JSON.parse(assignmentsData);
        assignments.forEach((assignment: ABTestAssignment) => {
          this.assignments.set(assignment.experimentId, {
            ...assignment,
            assignedAt: new Date(assignment.assignedAt)
          });
        });
      }

      const eventsData = localStorage.getItem('abtest_events');
      if (eventsData) {
        this.events = JSON.parse(eventsData).map((event: any) => ({
          ...event,
          timestamp: new Date(event.timestamp)
        }));
      }
    } catch (error) {
      console.error('Error loading A/B test data from storage:', error);
    }
  }

  private saveToStorage(): void {
    try {
      localStorage.setItem('abtest_experiments', JSON.stringify(Array.from(this.experiments.values())));
      localStorage.setItem('abtest_assignments', JSON.stringify(Array.from(this.assignments.values())));
      localStorage.setItem('abtest_events', JSON.stringify(this.events));
    } catch (error) {
      console.error('Error saving A/B test data to storage:', error);
    }
  }

  // Initialize default experiments for registration flow
  private initializeDefaultExperiments(): void {
    if (this.experiments.size === 0) {
      // Registration Step Design Experiment
      const registrationExperiment: ABTestExperiment = {
        id: 'reg_step_design_001',
        name: 'Registration Step Design',
        description: 'Test different designs for registration steps 1 & 2',
        status: 'running',
        startDate: new Date(),
        targetMetric: 'registration_completion',
        trafficAllocation: 100,
        variants: [
          {
            id: 'control',
            name: 'Original Design',
            weight: 50,
            config: { design: 'original' }
          },
          {
            id: 'modern',
            name: 'Modern Card Design',
            weight: 50,
            config: { design: 'modern' }
          }
        ],
        createdBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Registration Button Text Experiment
      const buttonTextExperiment: ABTestExperiment = {
        id: 'reg_button_text_001',
        name: 'Registration Button Text',
        description: 'Test different call-to-action button texts',
        status: 'running',
        startDate: new Date(),
        targetMetric: 'button_click',
        trafficAllocation: 100,
        variants: [
          {
            id: 'control',
            name: 'Create Account',
            weight: 33,
            config: { buttonText: 'Create Account' }
          },
          {
            id: 'variant_a',
            name: 'Join Yasmeen AI',
            weight: 33,
            config: { buttonText: 'Join Yasmeen AI' }
          },
          {
            id: 'variant_b',
            name: 'Start Your Journey',
            weight: 34,
            config: { buttonText: 'Start Your Journey' }
          }
        ],
        createdBy: 'system',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.experiments.set(registrationExperiment.id, registrationExperiment);
      this.experiments.set(buttonTextExperiment.id, buttonTextExperiment);
      this.saveToStorage();
    }
  }

  // Experiment management
  createExperiment(experiment: Omit<ABTestExperiment, 'id' | 'createdAt' | 'updatedAt'>): string {
    const id = 'exp_' + Math.random().toString(36).substr(2, 9);
    const newExperiment: ABTestExperiment = {
      ...experiment,
      id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    this.experiments.set(id, newExperiment);
    this.saveToStorage();
    return id;
  }

  updateExperiment(id: string, updates: Partial<ABTestExperiment>): boolean {
    const experiment = this.experiments.get(id);
    if (!experiment) return false;

    const updatedExperiment = {
      ...experiment,
      ...updates,
      updatedAt: new Date()
    };
    
    this.experiments.set(id, updatedExperiment);
    this.saveToStorage();
    return true;
  }

  deleteExperiment(id: string): boolean {
    const deleted = this.experiments.delete(id);
    if (deleted) {
      this.assignments.delete(id);
      this.events = this.events.filter(event => event.experimentId !== id);
      this.saveToStorage();
    }
    return deleted;
  }

  // Variant assignment
  getVariant(experimentId: string): ABTestVariant | null {
    const experiment = this.experiments.get(experimentId);
    if (!experiment || experiment.status !== 'running') {
      return null;
    }

    // Check if user already has assignment
    const existingAssignment = this.assignments.get(experimentId);
    if (existingAssignment) {
      return experiment.variants.find(v => v.id === existingAssignment.variantId) || null;
    }

    // Check traffic allocation
    if (Math.random() * 100 > experiment.trafficAllocation) {
      return null;
    }

    // Assign variant based on weights
    const random = Math.random() * 100;
    let cumulative = 0;
    
    for (const variant of experiment.variants) {
      cumulative += variant.weight;
      if (random <= cumulative) {
        const assignment: ABTestAssignment = {
          experimentId,
          variantId: variant.id,
          userId: this.userId,
          sessionId: this.sessionId,
          assignedAt: new Date()
        };
        
        this.assignments.set(experimentId, assignment);
        this.saveToStorage();
        
        // Track assignment event
        this.trackEvent(experimentId, 'assignment', { variantId: variant.id });
        
        return variant;
      }
    }

    return null;
  }

  // Event tracking
  trackEvent(experimentId: string, eventName: string, properties?: Record<string, any>): void {
    const assignment = this.assignments.get(experimentId);
    if (!assignment) return;

    const event: ABTestEvent = {
      id: 'event_' + Math.random().toString(36).substr(2, 9),
      experimentId,
      variantId: assignment.variantId,
      userId: this.userId,
      sessionId: this.sessionId,
      eventType: this.getEventType(eventName),
      eventName,
      properties,
      timestamp: new Date()
    };

    this.events.push(event);
    this.saveToStorage();

    // Send to analytics service (if available)
    this.sendToAnalytics(event);
  }

  private getEventType(eventName: string): ABTestEvent['eventType'] {
    if (eventName.includes('conversion') || eventName.includes('complete')) return 'conversion';
    if (eventName.includes('click')) return 'click';
    if (eventName.includes('view')) return 'view';
    return 'custom';
  }

  private sendToAnalytics(event: ABTestEvent): void {
    // Integration point for external analytics services
    // Could send to Google Analytics, Mixpanel, etc.
    console.log('A/B Test Event:', event);
  }

  // Analytics and reporting
  getExperimentMetrics(experimentId: string): ABTestMetrics[] {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) return [];

    const experimentEvents = this.events.filter(e => e.experimentId === experimentId);
    const metrics: ABTestMetrics[] = [];

    for (const variant of experiment.variants) {
      const variantEvents = experimentEvents.filter(e => e.variantId === variant.id);
      const users = new Set(variantEvents.map(e => e.userId)).size;
      const conversions = variantEvents.filter(e => e.eventType === 'conversion').length;
      
      metrics.push({
        experimentId,
        variantId: variant.id,
        totalUsers: users,
        totalEvents: variantEvents.length,
        conversions,
        conversionRate: users > 0 ? (conversions / users) * 100 : 0,
        confidence: 0, // Will be calculated by statistical analysis
        statisticalSignificance: false
      });
    }

    return metrics;
  }

  getAllExperiments(): ABTestExperiment[] {
    return Array.from(this.experiments.values());
  }

  getActiveExperiments(): ABTestExperiment[] {
    return Array.from(this.experiments.values()).filter(exp => exp.status === 'running');
  }

  getExperimentResults(experimentId: string): ABTestResults | null {
    const experiment = this.experiments.get(experimentId);
    if (!experiment) return null;

    const metrics = this.getExperimentMetrics(experimentId);
    const totalParticipants = metrics.reduce((sum, m) => sum + m.totalUsers, 0);
    const duration = Math.ceil((Date.now() - experiment.startDate.getTime()) / (1000 * 60 * 60 * 24));

    return {
      experiment,
      metrics,
      confidence: 0, // Will be calculated by statistical analysis
      duration,
      totalParticipants
    };
  }

  // Utility methods
  isUserInExperiment(experimentId: string): boolean {
    return this.assignments.has(experimentId);
  }

  getUserAssignment(experimentId: string): ABTestAssignment | null {
    return this.assignments.get(experimentId) || null;
  }

  clearUserData(): void {
    this.assignments.clear();
    this.events = [];
    localStorage.removeItem('abtest_assignments');
    localStorage.removeItem('abtest_events');
    localStorage.removeItem('abtest_user_id');
    sessionStorage.removeItem('abtest_session_id');
  }
}

// Singleton instance
export const abTestManager = new ABTestManager();
export default ABTestManager;
