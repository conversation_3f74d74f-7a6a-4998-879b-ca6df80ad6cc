/**
 * 🎯 AI Analytics Service
 * Advanced analytics and monitoring for AI interactions
 */

import { api } from './api';

// ========================================
// TYPES & INTERFACES
// ========================================

export interface DashboardStats {
  total_sessions: number;
  total_messages: number;
  unique_users: number;
  total_tokens: number;
  total_cost_usd: number;
  average_response_time: number;
  active_sessions: number;
  recent_errors: number;
  success_rate: number;
  
  // Regional breakdown
  regional_stats: {
    damascus: number;
    aleppo: number;
    homs: number;
    latakia: number;
  };
  
  // Topic breakdown
  topic_stats: {
    business: number;
    general: number;
    investment: number;
    other: number;
  };
  
  // Time series data
  daily_usage: Array<{
    date: string;
    sessions: number;
    messages: number;
    tokens: number;
    cost: number;
  }>;
}

export interface TokenUsageAnalytics {
  total_tokens: number;
  input_tokens: number;
  output_tokens: number;
  total_cost_usd: number;
  average_tokens_per_message: number;
  cost_trend: Array<{
    date: string;
    tokens: number;
    cost: number;
  }>;
  model_breakdown: Array<{
    model: string;
    tokens: number;
    cost: number;
    percentage: number;
  }>;
}

export interface TopicAnalytics {
  topic_distribution: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  business_intent_ratio: number;
  investment_related_ratio: number;
  regional_preferences: Array<{
    region: string;
    topics: Array<{
      category: string;
      count: number;
    }>;
  }>;
  trending_keywords: Array<{
    keyword: string;
    frequency: number;
    trend: 'up' | 'down' | 'stable';
  }>;
}

export interface UserBehaviorAnalytics {
  most_active_users: Array<{
    user_id: number;
    username: string;
    sessions: number;
    messages: number;
    tokens_used: number;
    last_active: string;
  }>;
  session_duration_stats: {
    average_duration: number;
    median_duration: number;
    longest_session: number;
    shortest_session: number;
  };
  user_satisfaction: {
    average_rating: number;
    total_ratings: number;
    rating_distribution: Array<{
      rating: number;
      count: number;
    }>;
  };
  retention_metrics: {
    daily_active_users: number;
    weekly_active_users: number;
    monthly_active_users: number;
    return_rate: number;
  };
}

export interface ErrorMonitoring {
  total_errors: number;
  error_rate: number;
  error_types: Array<{
    type: string;
    count: number;
    percentage: number;
  }>;
  recent_errors: Array<{
    id: number;
    error_type: string;
    error_message: string;
    occurred_at: string;
    session_id?: string;
    resolved: boolean;
  }>;
  error_trends: Array<{
    date: string;
    error_count: number;
    total_requests: number;
    error_rate: number;
  }>;
}

// ========================================
// ANALYTICS SERVICE CLASS
// ========================================

class AIAnalyticsService {
  private baseUrl = '/api/ai/analytics/admin';

  /**
   * Get comprehensive dashboard statistics
   */
  async getDashboardStats(days: number = 30): Promise<DashboardStats> {
    try {
      const response = await api.get(`${this.baseUrl}/dashboard/?days=${days}`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch dashboard stats:', error);
      throw error;
    }
  }



  /**
   * Get token usage analytics
   */
  async getTokenUsageAnalytics(params: {
    days?: number;
    group_by?: 'daily' | 'weekly' | 'monthly';
  } = {}): Promise<TokenUsageAnalytics> {
    try {
      const queryParams = new URLSearchParams({
        days: (params.days || 30).toString(),
        group_by: params.group_by || 'daily'
      });
      const response = await api.get(`${this.baseUrl}/tokens/?${queryParams}`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch token analytics:', error);
      throw error;
    }
  }



  /**
   * Get conversation topic analytics
   */
  async getTopicAnalytics(params: {
    days?: number;
    region?: string;
  } = {}): Promise<TopicAnalytics> {
    try {
      const queryParams = new URLSearchParams({
        days: (params.days || 30).toString(),
        ...(params.region && { region: params.region })
      });
      const response = await api.get(`${this.baseUrl}/topics/?${queryParams}`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch topic analytics:', error);
      throw error;
    }
  }

  /**
   * Get user behavior analytics
   */
  async getUserBehaviorAnalytics(params: {
    days?: number;
    limit?: number;
  } = {}): Promise<UserBehaviorAnalytics> {
    try {
      const queryParams = new URLSearchParams({
        days: (params.days || 30).toString(),
        limit: (params.limit || 50).toString()
      });
      const response = await api.get(`${this.baseUrl}/users/?${queryParams}`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch user behavior analytics:', error);
      throw error;
    }
  }

  /**
   * Get error monitoring data
   */
  async getErrorMonitoring(params: {
    days?: number;
    error_type?: string;
    resolved?: boolean;
  } = {}): Promise<ErrorMonitoring> {
    try {
      const queryParams = new URLSearchParams({
        days: (params.days || 30).toString(),
        ...(params.error_type && { error_type: params.error_type }),
        ...(params.resolved !== undefined && { resolved: params.resolved.toString() })
      });
      const response = await api.get(`${this.baseUrl}/errors/?${queryParams}`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch error monitoring:', error);
      throw error;
    }
  }



  /**
   * Export analytics data
   */
  async exportData(params: {
    type: 'dashboard' | 'tokens' | 'topics' | 'users' | 'errors';
    format: 'csv' | 'json';
    days?: number;
  }): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams({
        type: params.type,
        format: params.format,
        days: (params.days || 30).toString()
      });
      // Note: For blob responses, we might need to handle this differently
      const response = await api.get(`${this.baseUrl}/export/?${queryParams}`);
      return new Blob([JSON.stringify(response)], { type: 'application/json' });
    } catch {
      console.error('❌ Failed to export analytics data');
      throw new Error('Failed to export analytics data');
    }
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics(): Promise<{
    active_sessions: number;
    messages_per_minute: number;
    current_load: number;
    response_time: number;
  }> {
    try {
      const response = await api.get(`${this.baseUrl}/realtime/`);
      return response;
    } catch (error) {
      console.error('❌ Failed to fetch real-time metrics:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const aiAnalyticsService = new AIAnalyticsService();
export default aiAnalyticsService;
