// Statistical Analysis Utilities for A/B Testing

import { ABTestMetrics, StatisticalTest, ABTestStatistics } from '../types/abtest';

export class StatisticsCalculator {
  
  // Calculate Z-score for two proportions
  static calculateZScore(p1: number, n1: number, p2: number, n2: number): number {
    if (n1 === 0 || n2 === 0) return 0;
    
    const pooledProportion = (p1 * n1 + p2 * n2) / (n1 + n2);
    const standardError = Math.sqrt(pooledProportion * (1 - pooledProportion) * (1/n1 + 1/n2));
    
    if (standardError === 0) return 0;
    
    return (p1 - p2) / standardError;
  }

  // Calculate p-value from Z-score (two-tailed test)
  static calculatePValue(zScore: number): number {
    const absZ = Math.abs(zScore);
    
    // Approximation of the cumulative distribution function for standard normal
    const t = 1 / (1 + 0.2316419 * absZ);
    const d = 0.3989423 * Math.exp(-absZ * absZ / 2);
    const prob = d * t * (0.3193815 + t * (-0.3565638 + t * (1.781478 + t * (-1.821256 + t * 1.330274))));
    
    return 2 * prob; // Two-tailed test
  }

  // Calculate confidence interval for conversion rate
  static calculateConfidenceInterval(conversions: number, total: number, confidence: number = 0.95): [number, number] {
    if (total === 0) return [0, 0];
    
    const p = conversions / total;
    const z = this.getZValueForConfidence(confidence);
    const margin = z * Math.sqrt((p * (1 - p)) / total);
    
    return [
      Math.max(0, p - margin),
      Math.min(1, p + margin)
    ];
  }

  // Get Z-value for confidence level
  static getZValueForConfidence(confidence: number): number {
    const confidenceMap: Record<number, number> = {
      0.90: 1.645,
      0.95: 1.96,
      0.99: 2.576
    };
    
    return confidenceMap[confidence] || 1.96;
  }

  // Calculate minimum sample size needed for statistical significance
  static calculateMinSampleSize(
    baselineRate: number, 
    minimumDetectableEffect: number, 
    alpha: number = 0.05, 
    power: number = 0.8
  ): number {
    const zAlpha = this.getZValueForConfidence(1 - alpha);
    const zBeta = this.getZValueForConfidence(power);
    
    const p1 = baselineRate;
    const p2 = baselineRate * (1 + minimumDetectableEffect);
    
    const numerator = Math.pow(zAlpha * Math.sqrt(2 * p1 * (1 - p1)) + zBeta * Math.sqrt(p1 * (1 - p1) + p2 * (1 - p2)), 2);
    const denominator = Math.pow(p2 - p1, 2);
    
    return Math.ceil(numerator / denominator);
  }

  // Calculate statistical significance between control and variant
  static calculateSignificance(controlMetrics: ABTestMetrics, variantMetrics: ABTestMetrics): StatisticalTest {
    const controlRate = controlMetrics.conversions / controlMetrics.totalUsers;
    const variantRate = variantMetrics.conversions / variantMetrics.totalUsers;
    
    const zScore = this.calculateZScore(
      variantRate, 
      variantMetrics.totalUsers,
      controlRate, 
      controlMetrics.totalUsers
    );
    
    const pValue = this.calculatePValue(zScore);
    const confidence = 1 - pValue;
    const isSignificant = pValue < 0.05;
    
    const effect = variantRate - controlRate;
    const pooledProportion = (controlMetrics.conversions + variantMetrics.conversions) / 
                            (controlMetrics.totalUsers + variantMetrics.totalUsers);
    const standardError = Math.sqrt(pooledProportion * (1 - pooledProportion) * 
                                   (1/controlMetrics.totalUsers + 1/variantMetrics.totalUsers));
    
    return {
      pValue,
      confidence,
      isSignificant,
      sampleSize: controlMetrics.totalUsers + variantMetrics.totalUsers,
      effect,
      standardError
    };
  }

  // Calculate uplift percentage
  static calculateUplift(controlRate: number, variantRate: number): number {
    if (controlRate === 0) return 0;
    return ((variantRate - controlRate) / controlRate) * 100;
  }

  // Estimate time to reach statistical significance
  static estimateTimeToSignificance(
    currentSampleSize: number,
    targetSampleSize: number,
    dailyTraffic: number
  ): number {
    if (currentSampleSize >= targetSampleSize) return 0;
    if (dailyTraffic <= 0) return Infinity;
    
    const remainingSamples = targetSampleSize - currentSampleSize;
    return Math.ceil(remainingSamples / dailyTraffic);
  }

  // Generate comprehensive statistics for an experiment
  static generateExperimentStatistics(
    controlMetrics: ABTestMetrics,
    variantMetrics: ABTestMetrics[],
    dailyTraffic: number = 100
  ): ABTestStatistics {
    const tests: Record<string, StatisticalTest> = {};
    const recommendations: string[] = [];
    
    // Calculate statistics for each variant vs control
    variantMetrics.forEach((variant) => {
      const test = this.calculateSignificance(controlMetrics, variant);
      tests[variant.variantId] = test;
      
      // Generate recommendations
      if (test.isSignificant) {
        const uplift = this.calculateUplift(
          controlMetrics.conversions / controlMetrics.totalUsers,
          variant.conversions / variant.totalUsers
        );
        
        if (uplift > 0) {
          recommendations.push(`Variant ${variant.variantId} shows significant improvement (+${uplift.toFixed(1)}%)`);
        } else {
          recommendations.push(`Variant ${variant.variantId} shows significant decrease (${uplift.toFixed(1)}%)`);
        }
      } else {
        recommendations.push(`Variant ${variant.variantId} needs more data for conclusive results`);
      }
    });
    
    // Calculate minimum sample size
    const baselineRate = controlMetrics.conversions / controlMetrics.totalUsers;
    const minSampleSize = this.calculateMinSampleSize(baselineRate, 0.1); // 10% minimum detectable effect
    
    // Estimate duration
    const currentSampleSize = controlMetrics.totalUsers + variantMetrics.reduce((sum, v) => sum + v.totalUsers, 0);
    const estimatedDuration = this.estimateTimeToSignificance(currentSampleSize, minSampleSize, dailyTraffic);
    
    // Add general recommendations
    if (currentSampleSize < minSampleSize) {
      recommendations.push(`Need ${minSampleSize - currentSampleSize} more participants for reliable results`);
    }
    
    if (estimatedDuration > 0 && estimatedDuration < Infinity) {
      recommendations.push(`Estimated ${estimatedDuration} more days needed for statistical significance`);
    }
    
    return {
      controlMetrics,
      variantMetrics,
      tests,
      recommendations,
      minSampleSize,
      estimatedDuration
    };
  }

  // Calculate Bayesian probability that variant is better than control
  static calculateBayesianProbability(
    controlConversions: number,
    controlTotal: number,
    variantConversions: number,
    variantTotal: number,
    iterations: number = 10000
  ): number {
    // Simple Monte Carlo simulation for Bayesian A/B testing
    let variantWins = 0;
    
    for (let i = 0; i < iterations; i++) {
      // Sample from Beta distributions
      const controlSample = this.sampleBeta(controlConversions + 1, controlTotal - controlConversions + 1);
      const variantSample = this.sampleBeta(variantConversions + 1, variantTotal - variantConversions + 1);
      
      if (variantSample > controlSample) {
        variantWins++;
      }
    }
    
    return variantWins / iterations;
  }

  // Sample from Beta distribution (simplified)
  private static sampleBeta(alpha: number, beta: number): number {
    // Using Gamma distribution to sample from Beta
    const x = this.sampleGamma(alpha);
    const y = this.sampleGamma(beta);
    return x / (x + y);
  }

  // Sample from Gamma distribution (simplified)
  private static sampleGamma(shape: number): number {
    // Simplified gamma sampling for shape > 1
    if (shape < 1) return this.sampleGamma(shape + 1) * Math.pow(Math.random(), 1 / shape);
    
    const d = shape - 1/3;
    const c = 1 / Math.sqrt(9 * d);
    
    while (true) {
      let x, v;
      do {
        x = this.randomNormal();
        v = 1 + c * x;
      } while (v <= 0);
      
      v = v * v * v;
      const u = Math.random();
      
      if (u < 1 - 0.0331 * x * x * x * x) {
        return d * v;
      }
      
      if (Math.log(u) < 0.5 * x * x + d * (1 - v + Math.log(v))) {
        return d * v;
      }
    }
  }

  // Generate random normal distribution
  private static randomNormal(): number {
    // Box-Muller transform
    const u1 = Math.random();
    const u2 = Math.random();
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }
}

export default StatisticsCalculator;
