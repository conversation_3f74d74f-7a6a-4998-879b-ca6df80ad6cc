/**
 * 🎯 Error Monitoring Dashboard
 * Real-time monitoring and analysis of AI errors
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  PieChart, Pie, Cell
} from 'recharts';
import {
  AlertTriangle, AlertCircle, CheckCircle, Clock,
  TrendingUp, RefreshCw
} from 'lucide-react';
import { aiAnalyticsService, ErrorMonitoring } from '@/services/aiAnalyticsService';

// ========================================
// ERROR METRICS OVERVIEW
// ========================================

const ErrorMetricsOverview: React.FC<{ monitoring: ErrorMonitoring }> = ({ monitoring }) => {
  const formatNumber = (num: number) => num.toLocaleString();
  const formatPercentage = (rate: number) => `${(rate * 100).toFixed(2)}%`;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Total Errors */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">إجمالي الأخطاء</CardTitle>
          <AlertTriangle className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {formatNumber(monitoring.total_errors)}
          </div>
          <p className="text-xs text-muted-foreground">
            {monitoring.recent_errors.filter(e => !e.resolved).length} غير محلول
          </p>
        </CardContent>
      </Card>

      {/* Error Rate */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">معدل الأخطاء</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatPercentage(monitoring.error_rate)}
          </div>
          <Badge variant={monitoring.error_rate < 0.05 ? "default" : "destructive"}>
            {monitoring.error_rate < 0.05 ? "ممتاز" : "يحتاج تحسين"}
          </Badge>
        </CardContent>
      </Card>

      {/* Most Common Error */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">أكثر الأخطاء شيوعاً</CardTitle>
          <AlertCircle className="h-4 w-4 text-orange-500" />
        </CardHeader>
        <CardContent>
          <div className="text-lg font-bold">
            {monitoring.error_types[0]?.type || 'لا توجد أخطاء'}
          </div>
          <p className="text-xs text-muted-foreground">
            {monitoring.error_types[0]?.count || 0} حالة
          </p>
        </CardContent>
      </Card>

      {/* Resolution Status */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">حالة الحل</CardTitle>
          <CheckCircle className="h-4 w-4 text-green-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {monitoring.recent_errors.filter(e => e.resolved).length}
          </div>
          <p className="text-xs text-muted-foreground">
            من {monitoring.recent_errors.length} محلول
          </p>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// ERROR TYPES BREAKDOWN
// ========================================

const ErrorTypesBreakdown: React.FC<{ monitoring: ErrorMonitoring }> = ({ monitoring }) => {
  const COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

  return (
    <Card>
      <CardHeader>
        <CardTitle>توزيع أنواع الأخطاء</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <PieChart>
            <Pie
              data={monitoring.error_types}
              cx="50%"
              cy="50%"
              labelLine={false}
              label={({ type, percentage }) => `${type} ${percentage.toFixed(1)}%`}
              outerRadius={80}
              fill="#8884d8"
              dataKey="count"
            >
              {monitoring.error_types.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip />
          </PieChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// ========================================
// ERROR TRENDS CHART
// ========================================

const ErrorTrendsChart: React.FC<{ monitoring: ErrorMonitoring }> = ({ monitoring }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>اتجاهات الأخطاء</CardTitle>
      </CardHeader>
      <CardContent>
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={monitoring.error_trends}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="error_count" 
              stroke="#ff7300" 
              strokeWidth={2}
              name="عدد الأخطاء"
            />
            <Line 
              type="monotone" 
              dataKey="error_rate" 
              stroke="#8884d8" 
              strokeWidth={2}
              name="معدل الأخطاء (%)"
            />
          </LineChart>
        </ResponsiveContainer>
      </CardContent>
    </Card>
  );
};

// ========================================
// RECENT ERRORS LIST
// ========================================

const RecentErrorsList: React.FC<{ monitoring: ErrorMonitoring }> = ({ monitoring }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('ar-SA');
  };

  const getErrorTypeColor = (type: string) => {
    switch (type) {
      case 'api_error': return 'destructive';
      case 'timeout': return 'secondary';
      case 'quota_exceeded': return 'destructive';
      case 'processing_error': return 'secondary';
      default: return 'outline';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>الأخطاء الحديثة</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {monitoring.recent_errors.slice(0, 10).map((error) => (
            <div 
              key={error.id} 
              className="flex items-start justify-between p-4 border rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <Badge variant={getErrorTypeColor(error.error_type)}>
                    {error.error_type}
                  </Badge>
                  {error.resolved ? (
                    <Badge variant="default">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      محلول
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      غير محلول
                    </Badge>
                  )}
                </div>
                
                <p className="text-sm font-medium mb-1">
                  {error.error_message}
                </p>
                
                <div className="flex items-center text-xs text-muted-foreground space-x-4">
                  <span className="flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatDate(error.occurred_at)}
                  </span>
                  {error.session_id && (
                    <span>الجلسة: {error.session_id}</span>
                  )}
                </div>
              </div>
            </div>
          ))}
          
          {monitoring.recent_errors.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>لا توجد أخطاء حديثة! 🎉</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

// ========================================
// MAIN ERROR MONITORING COMPONENT
// ========================================

const ErrorMonitoringComponent: React.FC = () => {
  const [monitoring, setMonitoring] = useState<ErrorMonitoring | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState(30);
  const [errorTypeFilter, setErrorTypeFilter] = useState<string>('');
  const [resolvedFilter, setResolvedFilter] = useState<boolean | undefined>(undefined);

  // Load monitoring data
  const loadMonitoring = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await aiAnalyticsService.getErrorMonitoring({
        days: timeRange,
        error_type: errorTypeFilter || undefined,
        resolved: resolvedFilter
      });
      setMonitoring(data);
    } catch (err) {
      setError('فشل في تحميل بيانات مراقبة الأخطاء');
      console.error('Error monitoring loading error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadMonitoring();
  }, [timeRange, errorTypeFilter, resolvedFilter]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري تحميل مراقبة الأخطاء...</p>
        </div>
      </div>
    );
  }

  if (error || !monitoring) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold">خطأ في التحميل</h3>
          <p className="text-muted-foreground mb-4">{error}</p>
          <Button onClick={loadMonitoring}>إعادة المحاولة</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">🚨 مراقبة الأخطاء</h1>
          <p className="text-muted-foreground">تتبع وتحليل أخطاء النظام</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Filters */}
          <select 
            value={errorTypeFilter} 
            onChange={(e) => setErrorTypeFilter(e.target.value)}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">جميع الأنواع</option>
            <option value="api_error">خطأ API</option>
            <option value="timeout">انتهاء الوقت</option>
            <option value="quota_exceeded">تجاوز الحد</option>
            <option value="processing_error">خطأ معالجة</option>
          </select>

          <select 
            value={resolvedFilter === undefined ? '' : resolvedFilter.toString()} 
            onChange={(e) => setResolvedFilter(
              e.target.value === '' ? undefined : e.target.value === 'true'
            )}
            className="px-3 py-2 border rounded-md"
          >
            <option value="">جميع الحالات</option>
            <option value="true">محلول</option>
            <option value="false">غير محلول</option>
          </select>

          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(Number(e.target.value))}
            className="px-3 py-2 border rounded-md"
          >
            <option value={7}>آخر 7 أيام</option>
            <option value={30}>آخر 30 يوم</option>
            <option value={90}>آخر 3 أشهر</option>
          </select>

          <Button 
            variant="outline" 
            onClick={loadMonitoring}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            تحديث
          </Button>
        </div>
      </div>

      {/* Metrics Overview */}
      <ErrorMetricsOverview monitoring={monitoring} />

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ErrorTypesBreakdown monitoring={monitoring} />
        <ErrorTrendsChart monitoring={monitoring} />
      </div>

      {/* Recent Errors */}
      <RecentErrorsList monitoring={monitoring} />
    </div>
  );
};

export default ErrorMonitoringComponent;
