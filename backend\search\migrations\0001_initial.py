# Generated by Django 5.2.1 on 2025-08-05 14:09

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SearchDocument",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("content_type", models.<PERSON>r<PERSON><PERSON>(max_length=20)),
                ("object_id", models.IntegerField()),
                ("title", models.Char<PERSON>ield(max_length=255)),
                ("content", models.TextField()),
                ("author", models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=150)),
                ("created_at", models.DateTimeField()),
                ("updated_at", models.DateTimeField()),
                ("tags", models.<PERSON><PERSON><PERSON>ield(blank=True, default=list)),
                ("metadata", models.JSONField(blank=True, default=dict)),
                (
                    "search_text",
                    models.TextField(
                        blank=True, help_text="Combined text for searching"
                    ),
                ),
            ],
            options={
                "indexes": [
                    models.Index(
                        fields=["content_type"], name="search_sear_content_b6dc6b_idx"
                    ),
                    models.Index(
                        fields=["object_id"], name="search_sear_object__019ba5_idx"
                    ),
                    models.Index(
                        fields=["created_at"], name="search_sear_created_9638b5_idx"
                    ),
                ],
                "unique_together": {("content_type", "object_id")},
            },
        ),
    ]
