# Generated migration for performance optimization
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0001_initial'),
    ]

    operations = [
        # Add indexes for commonly queried fields
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userprofile_user_id ON users_userprofile(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_userprofile_user_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userprofile_is_active ON users_userprofile(is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_userprofile_is_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userprofile_language ON users_userprofile(language);",
            reverse_sql="DROP INDEX IF EXISTS idx_userprofile_language;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userrole_name ON users_userrole(name);",
            reverse_sql="DROP INDEX IF EXISTS idx_userrole_name;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userrole_is_active ON users_userrole(is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_userrole_is_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userroleassignment_user_profile_id ON users_userroleassignment(user_profile_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_userroleassignment_user_profile_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userroleassignment_role_id ON users_userroleassignment(role_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_userroleassignment_role_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_userroleassignment_is_active ON users_userroleassignment(is_active);",
            reverse_sql="DROP INDEX IF EXISTS idx_userroleassignment_is_active;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_roleapplication_user_id ON users_roleapplication(user_id);",
            reverse_sql="DROP INDEX IF EXISTS idx_roleapplication_user_id;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_roleapplication_status ON users_roleapplication(status);",
            reverse_sql="DROP INDEX IF EXISTS idx_roleapplication_status;"
        ),
        migrations.RunSQL(
            "CREATE INDEX IF NOT EXISTS idx_roleapplication_created_at ON users_roleapplication(created_at);",
            reverse_sql="DROP INDEX IF EXISTS idx_roleapplication_created_at;"
        ),
    ]
