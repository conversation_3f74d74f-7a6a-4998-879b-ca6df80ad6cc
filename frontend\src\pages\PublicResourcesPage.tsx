import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLanguage } from '../hooks/useLanguage';
import { BookOpen, FileText, Video, Link, Star, Calendar, Search, Filter } from 'lucide-react';

interface Resource {
  id: number;
  title: string;
  description: string;
  resource_type: string;
  url?: string;
  created_at: string;
  author?: {
    first_name: string;
    last_name: string;
  };
  rating?: number;
}

const PublicResourcesPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [resources, setResources] = useState<Resource[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('all');

  // Mock data for demonstration
  const mockResources: Resource[] = [
    {
      id: 1,
      title: isRTL ? 'دليل ريادة الأعمال للمبتدئين' : 'Entrepreneurship Guide for Beginners',
      description: isRTL ? 'دليل شامل لبدء رحلتك في ريادة الأعمال' : 'Comprehensive guide to start your entrepreneurship journey',
      resource_type: 'document',
      url: '#',
      created_at: '2024-01-15',
      author: { first_name: 'أحمد', last_name: 'محمد' },
      rating: 4.5
    },
    {
      id: 2,
      title: isRTL ? 'كيفية كتابة خطة عمل فعالة' : 'How to Write an Effective Business Plan',
      description: isRTL ? 'تعلم كيفية إنشاء خطة عمل احترافية' : 'Learn how to create a professional business plan',
      resource_type: 'video',
      url: '#',
      created_at: '2024-01-10',
      author: { first_name: 'فاطمة', last_name: 'أحمد' },
      rating: 4.8
    },
    {
      id: 3,
      title: isRTL ? 'أساسيات التسويق الرقمي' : 'Digital Marketing Fundamentals',
      description: isRTL ? 'مقدمة شاملة للتسويق الرقمي والإعلان عبر الإنترنت' : 'Comprehensive introduction to digital marketing and online advertising',
      resource_type: 'course',
      url: '#',
      created_at: '2024-01-05',
      author: { first_name: 'محمد', last_name: 'علي' },
      rating: 4.3
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setResources(mockResources);
      setLoading(false);
    }, 1000);
  }, []);

  const getResourceIcon = (type: string) => {
    switch (type) {
      case 'document':
        return <FileText className="w-5 h-5" />;
      case 'video':
        return <Video className="w-5 h-5" />;
      case 'course':
        return <BookOpen className="w-5 h-5" />;
      default:
        return <Link className="w-5 h-5" />;
    }
  };

  const getResourceTypeLabel = (type: string) => {
    const labels = {
      document: isRTL ? 'مستند' : 'Document',
      video: isRTL ? 'فيديو' : 'Video',
      course: isRTL ? 'دورة' : 'Course',
      link: isRTL ? 'رابط' : 'Link'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const filteredResources = resources.filter(resource => {
    const matchesSearch = resource.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         resource.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedType === 'all' || resource.resource_type === selectedType;
    return matchesSearch && matchesType;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center">
        <div className="text-white text-xl">{isRTL ? 'جاري التحميل...' : 'Loading...'}</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            {isRTL ? 'الموارد التعليمية' : 'Educational Resources'}
          </h1>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            {isRTL 
              ? 'اكتشف مجموعة واسعة من الموارد التعليمية والدورات المجانية لتطوير مهاراتك'
              : 'Discover a wide range of educational resources and free courses to develop your skills'
            }
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-8 flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400 w-5 h-5`} />
            <input
              type="text"
              placeholder={isRTL ? 'البحث في الموارد...' : 'Search resources...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={`w-full bg-white/10 border border-white/20 rounded-lg py-3 ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
          </div>
          <div className="relative">
            <Filter className={`absolute top-3 ${isRTL ? 'right-3' : 'left-3'} text-gray-400 w-5 h-5`} />
            <select
              value={selectedType}
              onChange={(e) => setSelectedType(e.target.value)}
              className={`bg-white/10 border border-white/20 rounded-lg py-3 ${isRTL ? 'pr-10 pl-4' : 'pl-10 pr-4'} text-white focus:outline-none focus:ring-2 focus:ring-blue-500`}
            >
              <option value="all">{isRTL ? 'جميع الأنواع' : 'All Types'}</option>
              <option value="document">{isRTL ? 'مستندات' : 'Documents'}</option>
              <option value="video">{isRTL ? 'فيديوهات' : 'Videos'}</option>
              <option value="course">{isRTL ? 'دورات' : 'Courses'}</option>
            </select>
          </div>
        </div>

        {/* Resources Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredResources.map((resource) => (
            <div
              key={resource.id}
              className="bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl p-6 hover:bg-white/15 transition-all duration-300 hover:transform hover:scale-105"
            >
              <div className={`flex items-center gap-3 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="p-2 bg-blue-500/20 rounded-lg text-blue-400">
                  {getResourceIcon(resource.resource_type)}
                </div>
                <div className="flex-1">
                  <span className="text-sm text-gray-400 bg-gray-700/50 px-2 py-1 rounded-full">
                    {getResourceTypeLabel(resource.resource_type)}
                  </span>
                </div>
              </div>

              <h3 className="text-xl font-semibold text-white mb-3 line-clamp-2">
                {resource.title}
              </h3>

              <p className="text-gray-300 mb-4 line-clamp-3">
                {resource.description}
              </p>

              <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-sm text-gray-300">{resource.rating}</span>
                </div>
                <div className={`flex items-center gap-1 text-sm text-gray-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Calendar className="w-4 h-4" />
                  <span>{new Date(resource.created_at).toLocaleDateString()}</span>
                </div>
              </div>

              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <span className="text-sm text-gray-400">
                  {isRTL ? 'بواسطة' : 'By'} {resource.author?.first_name} {resource.author?.last_name}
                </span>
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                  {isRTL ? 'عرض' : 'View'}
                </button>
              </div>
            </div>
          ))}
        </div>

        {filteredResources.length === 0 && (
          <div className="text-center py-12">
            <BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">
              {isRTL ? 'لا توجد موارد' : 'No Resources Found'}
            </h3>
            <p className="text-gray-400">
              {isRTL ? 'جرب تغيير معايير البحث' : 'Try adjusting your search criteria'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PublicResourcesPage;
