/**
 * 🚀 PERFORMANCE OPTIMIZATION UTILITIES
 * Advanced performance optimization with code splitting, lazy loading, and monitoring
 */

import { lazy, ComponentType, LazyExoticComponent } from 'react';

// ========================================
// LAZY LOADING WITH ERROR BOUNDARIES
// ========================================

interface LazyLoadOptions {
  fallback?: React.ComponentType;
  retryCount?: number;
  timeout?: number;
  preload?: boolean;
}

/**
 * Enhanced lazy loading with retry mechanism and preloading
 */
export const createLazyComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: LazyLoadOptions = {}
): LazyExoticComponent<T> => {
  const { retryCount = 3, timeout = 10000 } = options;

  const enhancedImportFn = async (): Promise<{ default: T }> => {
    let lastError: Error;

    for (let attempt = 1; attempt <= retryCount; attempt++) {
      try {
        // Add timeout to import
        const importPromise = importFn();
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error('Import timeout')), timeout);
        });

        const result = await Promise.race([importPromise, timeoutPromise]);
        
        // Track successful load
        performance.mark(`lazy-load-success-${result.default.name || 'unknown'}`);
        
        return result;
      } catch (error) {
        lastError = error as Error;
        
        // Track failed attempt
        performance.mark(`lazy-load-attempt-${attempt}`);
        
        if (attempt < retryCount) {
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    // Track final failure
    performance.mark('lazy-load-failed');
    throw lastError!;
  };

  return lazy(enhancedImportFn);
};

// ========================================
// PRELOADING UTILITIES
// ========================================

const preloadCache = new Map<string, Promise<any>>();

/**
 * Preload components for better performance
 */
export const preloadComponent = (
  key: string,
  importFn: () => Promise<any>
): Promise<any> => {
  if (preloadCache.has(key)) {
    return preloadCache.get(key)!;
  }

  const promise = importFn().catch(error => {
    // Remove failed preload from cache
    preloadCache.delete(key);
    throw error;
  });

  preloadCache.set(key, promise);
  return promise;
};

/**
 * Preload multiple components
 */
export const preloadComponents = (
  components: Array<{ key: string; importFn: () => Promise<any> }>
): Promise<any[]> => {
  return Promise.allSettled(
    components.map(({ key, importFn }) => preloadComponent(key, importFn))
  );
};

// ========================================
// BUNDLE OPTIMIZATION
// ========================================

/**
 * Dynamic import with chunk naming
 */
export const createNamedChunk = (
  chunkName: string,
  importFn: () => Promise<any>
) => {
  return () => importFn();
};

/**
 * Lazy load with route-based splitting
 */
export const createRouteComponent = (routeName: string) => {
  return createLazyComponent(
    () => import(`../pages/${routeName}`),
    { preload: true }
  );
};

// ========================================
// MEMORY OPTIMIZATION
// ========================================

/**
 * Memory leak detection and cleanup
 */
export class MemoryMonitor {
  private observers: PerformanceObserver[] = [];
  private intervals: NodeJS.Timeout[] = [];
  private memoryThreshold = 50 * 1024 * 1024; // 50MB

  constructor() {
    this.setupMemoryMonitoring();
  }

  private setupMemoryMonitoring(): void {
    // Monitor memory usage
    if ('memory' in performance) {
      const interval = setInterval(() => {
        const memory = (performance as any).memory;
        
        if (memory.usedJSHeapSize > this.memoryThreshold) {
          console.warn('🚨 High memory usage detected:', {
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
            limit: `${(memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)}MB`
          });
          
          this.triggerGarbageCollection();
        }
      }, 30000); // Check every 30 seconds

      this.intervals.push(interval);
    }
  }

  private triggerGarbageCollection(): void {
    // Force garbage collection if available
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc();
    }
    
    // Clear caches
    this.clearCaches();
  }

  private clearCaches(): void {
    // Clear preload cache
    preloadCache.clear();
    
    // Clear other caches
    if ('caches' in window) {
      caches.keys().then(names => {
        names.forEach(name => {
          if (name.includes('temp') || name.includes('cache')) {
            caches.delete(name);
          }
        });
      });
    }
  }

  destroy(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.intervals.forEach(interval => clearInterval(interval));
  }
}

// ========================================
// PERFORMANCE MONITORING
// ========================================

export class PerformanceTracker {
  private metrics = new Map<string, number[]>();
  private observer: PerformanceObserver | null = null;

  constructor() {
    this.setupPerformanceObserver();
  }

  private setupPerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.recordMetric(entry.name, entry.duration || entry.startTime);
        });
      });

      this.observer.observe({ 
        entryTypes: ['measure', 'navigation', 'paint', 'largest-contentful-paint'] 
      });
    }
  }

  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  getMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {};
    
    this.metrics.forEach((values, name) => {
      if (values.length > 0) {
        result[name] = {
          avg: values.reduce((a, b) => a + b, 0) / values.length,
          min: Math.min(...values),
          max: Math.max(...values),
          count: values.length
        };
      }
    });
    
    return result;
  }

  measureAsync<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now();
    
    return fn().finally(() => {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
    });
  }

  measure<T>(name: string, fn: () => T): T {
    const start = performance.now();
    
    try {
      return fn();
    } finally {
      const duration = performance.now() - start;
      this.recordMetric(name, duration);
    }
  }

  destroy(): void {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// ========================================
// IMAGE OPTIMIZATION
// ========================================

/**
 * Lazy image loading with intersection observer
 */
export const createLazyImage = (src: string, options: {
  placeholder?: string;
  threshold?: number;
  rootMargin?: string;
} = {}) => {
  const { placeholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNGMEYwRjAiLz48L3N2Zz4=', threshold = 0.1, rootMargin = '50px' } = options;

  return {
    src: placeholder,
    'data-src': src,
    loading: 'lazy' as const,
    onLoad: (e: React.SyntheticEvent<HTMLImageElement>) => {
      const img = e.currentTarget;
      img.classList.add('loaded');
    },
    onError: (e: React.SyntheticEvent<HTMLImageElement>) => {
      const img = e.currentTarget;
      img.src = placeholder;
      img.classList.add('error');
    }
  };
};

// ========================================
// GLOBAL INSTANCES
// ========================================

export const memoryMonitor = new MemoryMonitor();
export const performanceTracker = new PerformanceTracker();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  memoryMonitor.destroy();
  performanceTracker.destroy();
});
