import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { aiAnalyticsService } from '../../services/aiAnalyticsService';

interface TopicAnalyticsProps {
  days?: number;
  region?: string;
}

const TopicAnalytics: React.FC<TopicAnalyticsProps> = ({ 
  days = 30, 
  region = 'all' 
}) => {
  const [selectedRegion, setSelectedRegion] = useState(region);
  const [selectedDays, setSelectedDays] = useState(days);

  // Fetch topic analytics data
  const { data: analyticsData, isLoading, error, refetch } = useQuery({
    queryKey: ['topicAnalytics', selectedDays, selectedRegion],
    queryFn: () => aiAnalyticsService.getTopicAnalytics({ days: selectedDays, region: selectedRegion }),
    refetchInterval: 30000,
  });

  // TODO: Use when implementing charts
  // const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7c7c', '#8dd1e1'];

  // Topic name mapping
  const topicNameMap: Record<string, string> = {
    'business_planning': 'تخطيط الأعمال',
    'investment': 'الاستثمار',
    'market_analysis': 'تحليل السوق',
    'startup_advice': 'نصائح الشركات الناشئة',
    'damascus_business': 'أعمال دمشق',
    'aleppo_business': 'أعمال حلب',
    'technology': 'التكنولوجيا',
    'education': 'التعليم',
    'health': 'الصحة',
    'general_chat': 'محادثة عامة'
  };

  // Prepare data for charts
  const prepareTopicDistributionData = () => {
    if (!analyticsData?.topic_distribution) return [];
    
    return analyticsData.topic_distribution.map((item: any) => ({
      ...item,
      name: topicNameMap[item.topic_category] || item.topic_category,
      percentage: analyticsData.summary?.total_analyzed > 0 
        ? ((item.count / analyticsData.summary.total_analyzed) * 100).toFixed(1)
        : '0'
    }));
  };

  const prepareBusinessVsGeneralData = () => {
    if (!analyticsData?.business_vs_general) return [];
    
    const { business_topics, general_topics } = analyticsData.business_vs_general;
    return [
      { name: 'مواضيع الأعمال', value: business_topics, color: '#8884d8' },
      { name: 'مواضيع عامة', value: general_topics, color: '#82ca9d' }
    ];
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل تحليلات المواضيع...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-medium">خطأ في تحميل البيانات</h3>
        <p className="text-red-600 text-sm mt-1">
          فشل في تحميل تحليلات المواضيع. يرجى المحاولة مرة أخرى.
        </p>
        <button 
          onClick={() => refetch()}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          إعادة المحاولة
        </button>
      </div>
    );
  }

  const topicDistributionData = prepareTopicDistributionData();
  const businessVsGeneralData = prepareBusinessVsGeneralData();

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              📊 تحليل مواضيع المحادثات
            </h1>
            <p className="text-gray-600 mt-1">
              تحليل مفصل لمواضيع المحادثات والاتجاهات
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <select
              value={selectedRegion}
              onChange={(e) => setSelectedRegion(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">جميع المناطق</option>
              <option value="damascus_dialect">دمشق</option>
              <option value="aleppo_dialect">حلب</option>
              <option value="homs_dialect">حمص</option>
              <option value="latakia_dialect">اللاذقية</option>
            </select>
            
            <select
              value={selectedDays}
              onChange={(e) => setSelectedDays(Number(e.target.value))}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={7}>آخر 7 أيام</option>
              <option value={30}>آخر 30 يوم</option>
              <option value={90}>آخر 3 أشهر</option>
            </select>
            
            <button
              onClick={() => refetch()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
            >
              🔄 تحديث
            </button>
          </div>
        </div>
      </div>

      {/* Summary Stats */}
      {analyticsData?.summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">إجمالي التحليلات</h3>
                <p className="text-3xl font-bold text-blue-600 mt-2">
                  {analyticsData.summary.total_analyzed?.toLocaleString() || 0}
                </p>
              </div>
              <div className="text-4xl">📈</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">المواضيع الفريدة</h3>
                <p className="text-3xl font-bold text-green-600 mt-2">
                  {analyticsData.summary.unique_topics || 0}
                </p>
              </div>
              <div className="text-4xl">🎯</div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">متوسط الثقة</h3>
                <p className="text-3xl font-bold text-purple-600 mt-2">
                  {((analyticsData.summary.avg_confidence || 0) * 100).toFixed(1)}%
                </p>
              </div>
              <div className="text-4xl">⭐</div>
            </div>
          </div>
        </div>
      )}

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Topic Distribution Bar Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">توزيع المواضيع</h3>
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={topicDistributionData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={100}
                fontSize={12}
              />
              <YAxis />
              <Tooltip 
                formatter={(value) => [value, 'عدد المحادثات']}
                labelFormatter={(label) => `الموضوع: ${label}`}
              />
              <Bar dataKey="count" fill="#8884d8" />
            </BarChart>
          </ResponsiveContainer>
        </div>

        {/* Business vs General Pie Chart */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الأعمال مقابل العام</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={businessVsGeneralData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {businessVsGeneralData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Empty State */}
      {(!analyticsData || !analyticsData.summary || analyticsData.summary.total_analyzed === 0) && (
        <div className="bg-white rounded-lg shadow-sm border p-12">
          <div className="text-center">
            <div className="text-6xl mb-4">📊</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              لا توجد بيانات متاحة
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              لم يتم العثور على بيانات تحليل المواضيع للفترة المحددة.
            </p>
            <button
              onClick={() => refetch()}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              🔄 إعادة التحميل
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TopicAnalytics;
