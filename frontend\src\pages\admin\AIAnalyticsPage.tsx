/**
 * 🎯 AI Analytics Main Page
 * Comprehensive analytics dashboard with tabbed navigation
 */

import React from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, Zap, MessageSquare, Users, AlertTriangle, 
  TrendingUp, Activity, DollarSign
} from 'lucide-react';
import { useUnifiedRoles } from '@/hooks/useUnifiedRoles';

// Import analytics components
import AIAnalyticsDashboard from '@/components/admin/AIAnalyticsDashboard';
import TokenUsageAnalytics from '@/components/admin/TokenUsageAnalytics';
import TopicAnalytics from '@/components/admin/TopicAnalyticsSimple';
import UserBehaviorAnalytics from '@/components/admin/UserBehaviorAnalytics';
import ErrorMonitoring from '@/components/admin/ErrorMonitoring';

// ========================================
// ANALYTICS NAVIGATION TABS
// ========================================

const AnalyticsNavigation: React.FC<{ activeTab: string; onTabChange: (tab: string) => void }> = ({ 
  activeTab, 
  onTabChange 
}) => {
  const tabs = [
    {
      id: 'dashboard',
      label: 'لوحة التحكم',
      icon: BarChart3,
      description: 'نظرة شاملة على الأداء'
    },
    {
      id: 'tokens',
      label: 'تحليل الـ Tokens',
      icon: Zap,
      description: 'استخدام الـ tokens والتكاليف'
    },
    {
      id: 'topics',
      label: 'تحليل المواضيع',
      icon: MessageSquare,
      description: 'مواضيع المحادثات والاتجاهات'
    },
    {
      id: 'users',
      label: 'سلوك المستخدمين',
      icon: Users,
      description: 'تحليل نشاط المستخدمين'
    },
    {
      id: 'errors',
      label: 'مراقبة الأخطاء',
      icon: AlertTriangle,
      description: 'تتبع وحل الأخطاء'
    }
  ];

  return (
    <TabsList className="grid w-full grid-cols-5">
      {tabs.map((tab) => {
        const Icon = tab.icon;
        return (
          <TabsTrigger 
            key={tab.id} 
            value={tab.id}
            className="flex flex-col items-center space-y-1 p-4"
          >
            <Icon className="h-5 w-5" />
            <span className="text-xs">{tab.label}</span>
          </TabsTrigger>
        );
      })}
    </TabsList>
  );
};

// ========================================
// QUICK STATS OVERVIEW
// ========================================

const QuickStatsOverview: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardContent className="flex items-center p-4">
          <Activity className="h-8 w-8 text-blue-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-muted-foreground">الحالة</p>
            <div className="flex items-center space-x-2">
              <Badge variant="default">نشط</Badge>
              <span className="text-sm">🟢</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-4">
          <TrendingUp className="h-8 w-8 text-green-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-muted-foreground">الأداء</p>
            <p className="text-lg font-bold">ممتاز</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-4">
          <DollarSign className="h-8 w-8 text-yellow-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-muted-foreground">التكلفة</p>
            <p className="text-lg font-bold">منخفضة</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="flex items-center p-4">
          <AlertTriangle className="h-8 w-8 text-red-500 mr-3" />
          <div>
            <p className="text-sm font-medium text-muted-foreground">الأخطاء</p>
            <p className="text-lg font-bold">قليلة</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// ========================================
// PLACEHOLDER COMPONENTS FOR MISSING TABS
// ========================================





// ========================================
// MAIN AI ANALYTICS PAGE
// ========================================

const AIAnalyticsPage: React.FC = () => {
  const { canAccess } = useUnifiedRoles();

  // Check admin permissions
  if (!canAccess('canAccessSystemSettings')) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <AlertTriangle className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">غير مصرح</h2>
          <p className="text-muted-foreground">
            تحتاج صلاحيات المدير للوصول إلى تحليلات الذكاء الاصطناعي
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold mb-2">
          🤖 تحليلات الذكاء الاصطناعي
        </h1>
        <p className="text-xl text-muted-foreground">
          مراقبة شاملة ومتقدمة لأداء ياسمين الذكية
        </p>
      </div>

      {/* Quick Stats */}
      <QuickStatsOverview />

      {/* Analytics Tabs */}
      <Tabs defaultValue="dashboard" className="space-y-6">
        <AnalyticsNavigation 
          activeTab="dashboard" 
          onTabChange={() => {}} 
        />

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <AIAnalyticsDashboard />
        </TabsContent>

        {/* Token Analytics Tab */}
        <TabsContent value="tokens" className="space-y-6">
          <TokenUsageAnalytics />
        </TabsContent>

        {/* Topic Analytics Tab */}
        <TabsContent value="topics" className="space-y-6">
          <TopicAnalytics />
        </TabsContent>

        {/* User Behavior Tab */}
        <TabsContent value="users" className="space-y-6">
          <UserBehaviorAnalytics />
        </TabsContent>

        {/* Error Monitoring Tab */}
        <TabsContent value="errors" className="space-y-6">
          <ErrorMonitoring />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AIAnalyticsPage;
