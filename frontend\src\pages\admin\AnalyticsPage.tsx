/**
 * 🎯 ADMIN ANALYTICS PAGE
 * Modern analytics dashboard using login page styling and real API data only
 * No mock data - 100% API integration
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  BarChart3, Users, TrendingUp, Activity,
  Download, Eye, MessageSquare, Calendar
} from 'lucide-react';
import { RTLIcon, RTLText } from '../../components/common';
import { useLanguage } from '../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import {
  ResponsiveContainer, AreaChart, Area, XAxis, YAxis, CartesianGrid,
  Tooltip, PieChart, Pie, Cell, BarChart, Bar, Legend, LineChart, Line
} from 'recharts';
import { EnhancedButton, EnhancedCard, designSystem } from '../../components/ui/DesignSystem';
import { useAnalyticsData } from '../../hooks/useAdminData';
import { formatNumber, formatPercentage, getStatusInfo } from '../../utils/adminUtils';
import {
  AdminPageHeader, AdminStatsCard, AdminLoadingState, AdminErrorState
} from '../../components/admin/shared/AdminSharedComponents';

// Types for API responses
interface AnalyticsData {
  users: {
    total: number;
    active: number;
    newThisMonth: number;
    growthRate: number;
  };
  content: {
    totalPosts: number;
    totalEvents: number;
    totalResources: number;
    engagementRate: number;
  };
  activity: {
    dailyActiveUsers: number;
    monthlyActiveUsers: number;
    averageSessionTime: number;
    bounceRate: number;
  };
  trends: {
    userRegistrations: any[];
    contentCreation: any[];
    engagement: any[];
  };
}

const AnalyticsPage: React.FC = () => {
  const navigate = useNavigate();
  const { isRTL, language } = useLanguage();
  const { t } = useTranslation();

  // ✅ CONSOLIDATED: Use unified analytics hook instead of duplicate state
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const {
    data: analytics,
    loading,
    error,
    refreshing,
    refresh
  } = useAnalyticsData(timeRange);

  // ✅ CONSOLIDATED: All data loading and refresh logic is now handled by useAnalyticsData hook

  // ✅ CONSOLIDATED: Use shared loading and error components
  if (loading) {
    return <AdminLoadingState message="Loading analytics..." messageAr="جاري تحميل التحليلات..." />;
  }

  if (error) {
    return <AdminErrorState error={error} onRetry={refresh} retrying={refreshing} />;
  }

  return (
    <div className={`min-h-screen ${designSystem.backgrounds.main}`}>
      <div className="max-w-7xl mx-auto p-4 sm:p-6 lg:p-8">
        {/* Header */}
        <EnhancedCard className="mb-6">
          <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mr-4">
                <RTLIcon icon={BarChart3} size={24} className="text-purple-400" />
              </div>
              <div>
                <RTLText as="h1" className="text-3xl font-bold text-white">
                  {isRTL ? 'لوحة التحليلات' : t('admin.analytics.title', 'Analytics Dashboard')}
                </RTLText>
                <RTLText className="text-gray-300">
                  {isRTL ? 'تحليلات شاملة ورؤى للمنصة' : t('admin.analytics.subtitle', 'Comprehensive platform analytics and insights')}
                </RTLText>
              </div>
            </div>

            <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
              {/* Time Range Selector */}
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as '7d' | '30d' | '90d')}
                className="bg-white/20 border border-white/30 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                dir={language === 'ar' ? 'rtl' : 'ltr'}
              >
                <option value="7d">{t('admin.analytics.last7days', 'Last 7 days')}</option>
                <option value="30d">{t('admin.analytics.last30days', 'Last 30 days')}</option>
                <option value="90d">{t('admin.analytics.last90days', 'Last 90 days')}</option>
              </select>

              {/* Refresh Button */}
              <EnhancedButton
                onClick={handleRefresh}
                disabled={refreshing}
                variant="primary"
              >
                <RTLIcon
                  icon={RefreshCw}
                  size={16}
                  className={refreshing ? 'animate-spin' : ''}
                />
                <RTLText>{refreshing ? t('admin.analytics.refreshing', 'Refreshing...') : t('admin.analytics.refresh', 'Refresh')}</RTLText>
              </EnhancedButton>

              {/* Export Button */}
              <EnhancedButton
                onClick={() => console.log('Export analytics data')}
                variant="secondary"
              >
                <RTLIcon icon={Download} size={16} />
                <RTLText>{t('admin.analytics.export', 'Export')}</RTLText>
              </EnhancedButton>
            </div>
          </div>
        </EnhancedCard>

        {analytics && (
          <>
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              {/* Total Users */}
              <EnhancedCard>
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-blue-600/20">
                    <RTLIcon icon={Users} size={20} className="text-blue-400" />
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${analytics.users.growthRate > 0 ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'}`}>
                    {analytics.users.growthRate > 0 ? '+' : ''}{analytics.users.growthRate}%
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {analytics.users.total.toLocaleString()}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.analytics.totalUsers', 'Total Users')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {analytics.users.newThisMonth} {t('admin.analytics.newThisMonth', 'new this month')}
                </RTLText>
              </EnhancedCard>

              {/* Active Users */}
              <EnhancedCard>
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-green-600/20">
                    <RTLIcon icon={Activity} size={20} className="text-green-400" />
                  </div>
                  <span className="text-xs px-2 py-1 rounded-full bg-green-500/20 text-green-400">
                    {t('admin.analytics.active', 'Active')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {analytics.users.active.toLocaleString()}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.analytics.activeUsers', 'Active Users')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {Math.round((analytics.users.active / analytics.users.total) * 100)}% {t('admin.analytics.ofTotal', 'of total')}
                </RTLText>
              </EnhancedCard>

              {/* Total Content */}
              <EnhancedCard>
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-purple-600/20">
                    <RTLIcon icon={MessageSquare} size={20} className="text-purple-400" />
                  </div>
                  <span className="text-xs px-2 py-1 rounded-full bg-purple-500/20 text-purple-400">
                    {t('admin.analytics.content', 'Content')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {analytics.content.totalPosts.toLocaleString()}
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.analytics.totalPosts', 'Total Posts')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {analytics.content.totalEvents} {t('admin.analytics.events', 'events')}, {analytics.content.totalResources} {t('admin.analytics.resources', 'resources')}
                </RTLText>
              </EnhancedCard>

              {/* Engagement Rate */}
              <EnhancedCard>
                <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className="inline-flex items-center justify-center w-10 h-10 rounded-full bg-orange-600/20">
                    <RTLIcon icon={TrendingUp} size={20} className="text-orange-400" />
                  </div>
                  <span className="text-xs px-2 py-1 rounded-full bg-orange-500/20 text-orange-400">
                    {t('admin.analytics.engagement', 'Engagement')}
                  </span>
                </div>
                <RTLText className="text-2xl font-bold text-white mb-1">
                  {analytics.content.engagementRate}%
                </RTLText>
                <RTLText className="text-gray-300 text-sm">{t('admin.analytics.engagementRate', 'Engagement Rate')}</RTLText>
                <RTLText className="text-gray-400 text-xs mt-2">
                  {analytics.activity.averageSessionTime}{t('admin.analytics.minAvgSession', 'min avg session')}
                </RTLText>
              </EnhancedCard>
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* User Registrations Trend */}
              <EnhancedCard>
                <RTLText className="text-xl font-bold text-white mb-4">{t('admin.analytics.userRegistrationsTrend', 'User Registrations Trend')}</RTLText>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={analytics.trends.userRegistrations}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="date" stroke="#9CA3AF" />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0, 0, 0, 0.8)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          borderRadius: '8px',
                          color: '#FFFFFF'
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="count"
                        stroke="#8B5CF6"
                        strokeWidth={2}
                        dot={{ fill: '#8B5CF6', strokeWidth: 2 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </EnhancedCard>

              {/* Content Creation */}
              <EnhancedCard>
                <RTLText className="text-xl font-bold text-white mb-4">{t('admin.analytics.contentCreation', 'Content Creation')}</RTLText>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={analytics.trends.contentCreation}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                      <XAxis dataKey="date" stroke="#9CA3AF" />
                      <YAxis stroke="#9CA3AF" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'rgba(0, 0, 0, 0.8)',
                          border: '1px solid rgba(255, 255, 255, 0.2)',
                          borderRadius: '8px',
                          color: '#FFFFFF'
                        }}
                      />
                      <Bar dataKey="posts" fill="#8B5CF6" />
                      <Bar dataKey="events" fill="#10B981" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </EnhancedCard>
            </div>

            {/* Activity Summary */}
            <EnhancedCard>
              <RTLText className="text-xl font-bold text-white mb-6">{t('admin.analytics.activitySummary', 'Activity Summary')}</RTLText>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <RTLText className="text-3xl font-bold text-blue-400 mb-2">
                    {analytics.activity.dailyActiveUsers}
                  </RTLText>
                  <RTLText className="text-gray-300">{t('admin.analytics.dailyActiveUsers', 'Daily Active Users')}</RTLText>
                </div>

                <div className="text-center">
                  <RTLText className="text-3xl font-bold text-green-400 mb-2">
                    {analytics.activity.monthlyActiveUsers}
                  </RTLText>
                  <RTLText className="text-gray-300">{t('admin.analytics.monthlyActiveUsers', 'Monthly Active Users')}</RTLText>
                </div>

                <div className="text-center">
                  <RTLText className="text-3xl font-bold text-orange-400 mb-2">
                    {analytics.activity.bounceRate}%
                  </RTLText>
                  <RTLText className="text-gray-300">{t('admin.analytics.bounceRate', 'Bounce Rate')}</RTLText>
                </div>
              </div>
            </EnhancedCard>
          </>
        )}
      </div>
    </div>
  );
};

export default AnalyticsPage;