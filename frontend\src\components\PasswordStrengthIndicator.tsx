import React from 'react';
import { Check, X } from 'lucide-react';

interface PasswordStrengthIndicatorProps {
  password: string;
  language: string;
  className?: string;
}

interface PasswordRequirement {
  id: string;
  label: string;
  labelAr: string;
  test: (password: string) => boolean;
  priority: number;
}

const PasswordStrengthIndicator: React.FC<PasswordStrengthIndicatorProps> = ({
  password,
  language,
  className = ''
}) => {
  const requirements: PasswordRequirement[] = [
    {
      id: 'length',
      label: 'At least 8 characters',
      labelAr: 'على الأقل 8 أحرف',
      test: (pwd) => pwd.length >= 8,
      priority: 1
    },
    {
      id: 'lowercase',
      label: 'One lowercase letter',
      labelAr: 'حرف صغير واحد',
      test: (pwd) => /[a-z]/.test(pwd),
      priority: 2
    },
    {
      id: 'uppercase',
      label: 'One uppercase letter',
      labelAr: 'حرف كبير واحد',
      test: (pwd) => /[A-Z]/.test(pwd),
      priority: 3
    },
    {
      id: 'number',
      label: 'One number',
      labelAr: 'رقم واحد',
      test: (pwd) => /\d/.test(pwd),
      priority: 4
    },
    {
      id: 'special',
      label: 'One special character',
      labelAr: 'رمز خاص واحد',
      test: (pwd) => /[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(pwd),
      priority: 5
    }
  ];

  const calculateStrength = (password: string): number => {
    if (!password) return 0;
    
    let score = 0;
    const maxScore = 100;
    
    // Length scoring (0-30 points)
    if (password.length >= 8) score += 15;
    if (password.length >= 12) score += 10;
    if (password.length >= 16) score += 5;
    
    // Character variety (0-40 points)
    if (/[a-z]/.test(password)) score += 10;
    if (/[A-Z]/.test(password)) score += 10;
    if (/\d/.test(password)) score += 10;
    if (/[!@#$%^&*()_+\-=[\]{}|;:,.<>?]/.test(password)) score += 10;
    
    // Complexity bonus (0-30 points)
    const uniqueChars = new Set(password).size;
    const uniqueRatio = uniqueChars / password.length;
    if (uniqueRatio > 0.7) score += 15; // Good character diversity
    
    // Check for common patterns (penalties)
    const commonPatterns = ['123', '234', '345', 'abc', 'bcd', 'password', 'qwerty'];
    const hasCommonPattern = commonPatterns.some(pattern => 
      password.toLowerCase().includes(pattern)
    );
    if (hasCommonPattern) score -= 20;
    
    // Check for repeated characters (penalty)
    const hasRepeatedChars = /(.)\1{2,}/.test(password); // 3+ repeated chars
    if (hasRepeatedChars) score -= 10;
    
    // Bonus for meeting all requirements
    const metRequirements = requirements.filter(req => req.test(password)).length;
    if (metRequirements === requirements.length) score += 15;
    
    return Math.max(0, Math.min(maxScore, score));
  };

  const getStrengthLevel = (score: number): { level: string; color: string; label: string; labelAr: string } => {
    if (score < 30) return { 
      level: 'weak', 
      color: 'red', 
      label: 'Weak', 
      labelAr: 'ضعيف' 
    };
    if (score < 60) return { 
      level: 'fair', 
      color: 'orange', 
      label: 'Fair', 
      labelAr: 'متوسط' 
    };
    if (score < 80) return { 
      level: 'good', 
      color: 'yellow', 
      label: 'Good', 
      labelAr: 'جيد' 
    };
    return { 
      level: 'strong', 
      color: 'green', 
      label: 'Strong', 
      labelAr: 'قوي' 
    };
  };

  const strength = calculateStrength(password);
  const strengthInfo = getStrengthLevel(strength);
  const isRTL = language === 'ar';

  if (!password) return null;

  return (
    <div className={`mt-2 p-3 bg-white/5 rounded-lg border border-white/10 ${className}`}>
      {/* Strength Meter */}
      <div className="mb-3">
        <div className={`flex items-center justify-between mb-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <span className="text-xs font-medium text-gray-300">
            {language === 'ar' ? 'قوة كلمة المرور' : 'Password Strength'}
          </span>
          <span className={`text-xs font-medium text-${strengthInfo.color}-400`}>
            {language === 'ar' ? strengthInfo.labelAr : strengthInfo.label}
          </span>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-gray-700 rounded-full h-1.5">
          <div
            className={`h-1.5 rounded-full transition-all duration-300 bg-${strengthInfo.color}-500`}
            style={{ width: `${strength}%` }}
          />
        </div>
      </div>

      {/* Requirements Checklist */}
      <div className="space-y-1.5">
        <h4 className="text-xs font-medium text-gray-300 mb-2">
          {language === 'ar' ? 'متطلبات كلمة المرور:' : 'Password Requirements:'}
        </h4>

        <div className="grid grid-cols-2 gap-x-4 gap-y-1">
          {requirements.map((requirement) => {
            const isMet = requirement.test(password);
            return (
              <div
                key={requirement.id}
                className={`flex items-center text-xs transition-colors duration-200 ${
                  isRTL ? 'flex-row-reverse' : ''
                }`}
              >
                <div className={`flex-shrink-0 ${isRTL ? 'ml-1.5' : 'mr-1.5'}`}>
                  {isMet ? (
                    <Check className="w-3 h-3 text-green-400" />
                  ) : (
                    <X className="w-3 h-3 text-red-400" />
                  )}
                </div>
                <span className={`${isMet ? 'text-green-400' : 'text-gray-400'} truncate`}>
                  {language === 'ar' ? requirement.labelAr : requirement.label}
                </span>
              </div>
            );
          })}
        </div>
      </div>


    </div>
  );
};

export default PasswordStrengthIndicator;
