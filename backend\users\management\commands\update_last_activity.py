from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from users.models import UserProfile
from community.models import OnlineUser
from datetime import timedelta


class Command(BaseCommand):
    help = 'Update user last activity based on actual user activity'

    def handle(self, *args, **options):
        self.stdout.write('Updating user last activity...')
        
        # Get all users
        users = User.objects.all()
        
        for user in users:
            try:
                profile = user.profile
                
                # Check various sources for actual last activity
                last_activity_sources = []
                
                # 1. User's last login
                if user.last_login:
                    last_activity_sources.append(user.last_login)
                
                # 2. OnlineUser last_seen
                try:
                    online_user = OnlineUser.objects.get(user=user)
                    last_activity_sources.append(online_user.last_seen)
                except OnlineUser.DoesNotExist:
                    pass
                
                # 3. Forum posts
                try:
                    from forums.models import ForumPost
                    latest_post = ForumPost.objects.filter(author=user).order_by('-created_at').first()
                    if latest_post:
                        last_activity_sources.append(latest_post.created_at)
                except:
                    pass
                
                # 4. Forum threads
                try:
                    from forums.models import ForumThread
                    latest_thread = ForumThread.objects.filter(author=user).order_by('-created_at').first()
                    if latest_thread:
                        last_activity_sources.append(latest_thread.created_at)
                except:
                    pass
                
                # 5. Community posts
                try:
                    from community.models import Post
                    latest_community_post = Post.objects.filter(author=user).order_by('-created_at').first()
                    if latest_community_post:
                        last_activity_sources.append(latest_community_post.created_at)
                except:
                    pass
                
                # 6. User activities
                try:
                    from community.models import UserActivity
                    latest_activity = UserActivity.objects.filter(user=user).order_by('-created_at').first()
                    if latest_activity:
                        last_activity_sources.append(latest_activity.created_at)
                except:
                    pass
                
                # Find the most recent activity
                if last_activity_sources:
                    actual_last_activity = max(last_activity_sources)
                    
                    # Only update if the new activity is different and more recent
                    if actual_last_activity != profile.last_activity:
                        old_activity = profile.last_activity
                        profile.last_activity = actual_last_activity
                        profile.save(update_fields=['last_activity'])
                        
                        self.stdout.write(
                            f'Updated {user.username}: {old_activity} -> {actual_last_activity}'
                        )
                    else:
                        self.stdout.write(f'No update needed for {user.username}')
                else:
                    # If no activity found, use a reasonable default (e.g., profile creation time)
                    if profile.last_activity == profile.created_at:
                        # Set to a few hours after creation to simulate some activity
                        simulated_activity = profile.created_at + timedelta(hours=2)
                        profile.last_activity = simulated_activity
                        profile.save(update_fields=['last_activity'])
                        
                        self.stdout.write(
                            f'Set simulated activity for {user.username}: {simulated_activity}'
                        )
                
            except Exception as e:
                self.stdout.write(f'Error updating {user.username}: {e}')
        
        self.stdout.write(self.style.SUCCESS('Successfully updated user last activity'))
