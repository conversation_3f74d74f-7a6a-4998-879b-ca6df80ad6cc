/**
 * 🎯 UNIFIED ERROR HANDLER
 * Single error handling system - eliminates 15+ duplicate error handlers
 */

import { useCallback, useState } from 'react';

// ========================================
// TYPES
// ========================================

export interface UnifiedError {
  id: string;
  message: string;
  code?: string;
  status?: number;
  type: 'api' | 'validation' | 'network' | 'auth' | 'permission' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  context?: string;
  details?: any;
  userFriendly: boolean;
  retryable: boolean;
  stack?: string;
}

export interface ErrorHandlerConfig {
  enableConsoleLogging: boolean;
  enableRemoteLogging: boolean;
  enableUserNotification: boolean;
  maxRetries: number;
  retryDelay: number;
  language: 'ar' | 'en';
}

export interface ErrorState {
  error: UnifiedError | null;
  isError: boolean;
  retryCount: number;
  isRetrying: boolean;
}

// ========================================
// ERROR MESSAGES
// ========================================

const errorMessages = {
  en: {
    network: 'Network connection failed. Please check your internet connection.',
    auth: 'Authentication failed. Please log in again.',
    permission: 'You do not have permission to perform this action.',
    validation: 'Please check your input and try again.',
    api: 'Server error occurred. Please try again later.',
    unknown: 'An unexpected error occurred. Please try again.',
    retry: 'Retrying...',
    maxRetries: 'Maximum retry attempts reached. Please try again later.'
  },
  ar: {
    network: 'فشل الاتصال بالشبكة. يرجى التحقق من اتصال الإنترنت.',
    auth: 'فشل في المصادقة. يرجى تسجيل الدخول مرة أخرى.',
    permission: 'ليس لديك إذن لتنفيذ هذا الإجراء.',
    validation: 'يرجى التحقق من المدخلات والمحاولة مرة أخرى.',
    api: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى لاحقاً.',
    unknown: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
    retry: 'جاري إعادة المحاولة...',
    maxRetries: 'تم الوصول إلى الحد الأقصى لمحاولات الإعادة. يرجى المحاولة مرة أخرى لاحقاً.'
  }
};

// ========================================
// UNIFIED ERROR HANDLER CLASS
// ========================================

export class UnifiedErrorHandler {
  private config: ErrorHandlerConfig;
  private errorQueue: UnifiedError[] = [];
  private errorCount = 0;

  constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableRemoteLogging: false,
      enableUserNotification: true,
      maxRetries: 3,
      retryDelay: 1000,
      language: 'en',
      ...config
    };
  }

  /**
   * Create a unified error from any error type
   */
  createError(error: any, context?: string): UnifiedError {
    const id = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const timestamp = new Date();

    // Determine error type and details
    let type: UnifiedError['type'] = 'unknown';
    let severity: UnifiedError['severity'] = 'medium';
    let message = 'An unexpected error occurred';
    const userFriendly = true;
    let retryable = true;
    let status: number | undefined;
    let code: string | undefined;

    // Handle different error types
    if (error?.response) {
      // HTTP/API errors
      type = 'api';
      status = error.response.status;
      code = `HTTP_${status}`;
      
      if (status === 401) {
        type = 'auth';
        severity = 'high';
        message = this.getMessage('auth');
        retryable = false;
      } else if (status === 403) {
        type = 'permission';
        severity = 'high';
        message = this.getMessage('permission');
        retryable = false;
      } else if (status >= 400 && status < 500) {
        severity = 'medium';
        message = error.response.data?.message || this.getMessage('validation');
        retryable = false;
      } else if (status >= 500) {
        severity = 'high';
        message = this.getMessage('api');
        retryable = true;
      }
    } else if (error?.name === 'NetworkError' || error?.message?.includes('fetch')) {
      // Network errors
      type = 'network';
      severity = 'high';
      message = this.getMessage('network');
      retryable = true;
    } else if (error?.name === 'ValidationError') {
      // Validation errors
      type = 'validation';
      severity = 'low';
      message = error.message || this.getMessage('validation');
      retryable = false;
    } else if (typeof error === 'string') {
      // String errors
      message = error;
      severity = 'low';
    } else if (error instanceof Error) {
      // Generic Error objects
      message = error.message;
      code = error.name;
    }

    return {
      id,
      message,
      code,
      status,
      type,
      severity,
      timestamp,
      context,
      details: error,
      userFriendly,
      retryable,
      stack: error?.stack
    };
  }

  /**
   * Handle an error with logging and notifications
   */
  handleError(error: any, context?: string): UnifiedError {
    const unifiedError = this.createError(error, context);
    
    // Add to error queue
    this.errorQueue.push(unifiedError);
    this.errorCount++;

    // Console logging
    if (this.config.enableConsoleLogging) {
      this.logToConsole(unifiedError);
    }

    // Remote logging
    if (this.config.enableRemoteLogging) {
      this.logToRemote(unifiedError);
    }

    // User notification
    if (this.config.enableUserNotification && unifiedError.userFriendly) {
      this.notifyUser(unifiedError);
    }

    return unifiedError;
  }

  /**
   * Handle async operations with automatic error handling and retry
   */
  async handleAsync<T>(
    operation: () => Promise<T>,
    context?: string,
    maxRetries?: number
  ): Promise<{ data?: T; error?: UnifiedError }> {
    const retries = maxRetries ?? this.config.maxRetries;
    let lastError: any;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const data = await operation();
        return { data };
      } catch (error) {
        lastError = error;
        const unifiedError = this.createError(error, context);

        // Don't retry if error is not retryable
        if (!unifiedError.retryable || attempt === retries) {
          const finalError = this.handleError(error, context);
          return { error: finalError };
        }

        // Wait before retrying
        if (attempt < retries) {
          await this.delay(this.config.retryDelay * Math.pow(2, attempt));
        }
      }
    }

    const finalError = this.handleError(lastError, context);
    return { error: finalError };
  }

  /**
   * Retry a failed operation
   */
  async retry<T>(
    operation: () => Promise<T>,
    originalError: UnifiedError,
    maxRetries?: number
  ): Promise<{ data?: T; error?: UnifiedError }> {
    if (!originalError.retryable) {
      return { error: originalError };
    }

    return this.handleAsync(operation, originalError.context, maxRetries);
  }

  // ========================================
  // PRIVATE METHODS
  // ========================================

  private getMessage(key: string): string {
    return errorMessages[this.config.language][key] || errorMessages.en[key] || key;
  }

  private logToConsole(error: UnifiedError): void {
    const style = this.getConsoleStyle(error.severity);
    
    console.group(`🚨 ${error.severity.toUpperCase()} Error [${error.type}]`);
    console.error(`%c${error.message}`, style);
    console.error('ID:', error.id);
    console.error('Code:', error.code);
    console.error('Status:', error.status);
    console.error('Context:', error.context);
    console.error('Timestamp:', error.timestamp);
    if (error.stack) console.error('Stack:', error.stack);
    if (error.details) console.error('Details:', error.details);
    console.groupEnd();
  }

  private getConsoleStyle(severity: UnifiedError['severity']): string {
    switch (severity) {
      case 'critical': return 'color: #ff0000; font-weight: bold; font-size: 14px;';
      case 'high': return 'color: #ff6600; font-weight: bold;';
      case 'medium': return 'color: #ffaa00;';
      case 'low': return 'color: #666666;';
      default: return '';
    }
  }

  private async logToRemote(error: UnifiedError): Promise<void> {
    try {
      // Implementation would send to monitoring service
      // Example: Sentry, LogRocket, etc.
      console.log('Would send to remote logging service:', error);
    } catch (remoteError) {
      console.error('Failed to log to remote service:', remoteError);
    }
  }

  private notifyUser(error: UnifiedError): void {
    // Dispatch custom event for toast/notification system
    window.dispatchEvent(new CustomEvent('unified-error', {
      detail: {
        type: 'error',
        message: error.message,
        severity: error.severity,
        retryable: error.retryable,
        errorId: error.id
      }
    }));
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // ========================================
  // PUBLIC UTILITY METHODS
  // ========================================

  getErrorHistory(): UnifiedError[] {
    return [...this.errorQueue];
  }

  clearErrorHistory(): void {
    this.errorQueue = [];
    this.errorCount = 0;
  }

  updateConfig(newConfig: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfig(): ErrorHandlerConfig {
    return { ...this.config };
  }
}

// ========================================
// SINGLETON INSTANCE
// ========================================

export const unifiedErrorHandler = new UnifiedErrorHandler({
  enableConsoleLogging: import.meta.env.DEV,
  enableRemoteLogging: import.meta.env.PROD,
  enableUserNotification: true
});

// ========================================
// REACT HOOK
// ========================================

export function useUnifiedErrorHandler(context?: string) {
  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    retryCount: 0,
    isRetrying: false
  });

  const handleError = useCallback((error: any, customContext?: string) => {
    const unifiedError = unifiedErrorHandler.handleError(error, customContext || context);
    
    setErrorState({
      error: unifiedError,
      isError: true,
      retryCount: 0,
      isRetrying: false
    });

    return unifiedError;
  }, [context]);

  const clearError = useCallback(() => {
    setErrorState({
      error: null,
      isError: false,
      retryCount: 0,
      isRetrying: false
    });
  }, []);

  const retry = useCallback(async (operation: () => Promise<any>) => {
    if (!errorState.error?.retryable) return;

    setErrorState(prev => ({
      ...prev,
      isRetrying: true,
      retryCount: prev.retryCount + 1
    }));

    try {
      const result = await operation();
      clearError();
      return result;
    } catch (error) {
      const newError = handleError(error);
      setErrorState(prev => ({
        ...prev,
        error: newError,
        isRetrying: false
      }));
      throw error;
    }
  }, [errorState.error, handleError, clearError]);

  const handleAsync = useCallback(async <T>(
    operation: () => Promise<T>,
    customContext?: string
  ): Promise<{ data?: T; error?: UnifiedError }> => {
    clearError();
    return unifiedErrorHandler.handleAsync(operation, customContext || context);
  }, [context, clearError]);

  return {
    ...errorState,
    handleError,
    clearError,
    retry,
    handleAsync
  };
}

// ========================================
// CONVENIENCE FUNCTIONS
// ========================================

export const handleError = (error: any, context?: string): UnifiedError => {
  return unifiedErrorHandler.handleError(error, context);
};

export const handleAsync = async <T>(
  operation: () => Promise<T>,
  context?: string
): Promise<{ data?: T; error?: UnifiedError }> => {
  return unifiedErrorHandler.handleAsync(operation, context);
};

export default {
  UnifiedErrorHandler,
  unifiedErrorHandler,
  useUnifiedErrorHandler,
  handleError,
  handleAsync
};
