/**
 * 🎯 UNIFIED USER TYPES
 * Single source of truth for all user-related interfaces
 * Matches backend Django models and serializers exactly
 */

// ========================================
// CORE USER TYPES
// ========================================

export type UserRole = 'admin' | 'moderator' | 'entrepreneur' | 'mentor' | 'investor' | 'user';
export type PermissionLevel = 'read' | 'write' | 'moderate' | 'admin';
export type ProfileVisibility = 'public' | 'members' | 'private';
export type Language = 'en' | 'ar';

// ========================================
// USER ROLE INTERFACES
// ========================================

export interface DatabaseRole {
  id: number;
  name: UserRole;
  display_name: string;
  description: string;
  permission_level: PermissionLevel;
  requires_approval: boolean;
  is_active: boolean;
}

export interface UserRoleAssignment {
  id: number;
  role: DatabaseRole;
  assigned_at: string;
  expires_at?: string;
  is_active: boolean;
  notes?: string;
}

export interface ActiveRole {
  name: UserRole;
  permission_level: PermissionLevel;
  display_name: string;
}

// ========================================
// USER PROFILE INTERFACE
// ========================================

export interface UserProfile {
  id: number;
  // Personal information
  bio?: string;
  location?: string;
  birth_date?: string;
  phone_number?: string;
  
  // Social links
  website?: string;
  linkedin_url?: string;
  twitter_url?: string;
  github_url?: string;
  
  // Professional information
  company?: string;
  job_title?: string;
  industry?: string;
  experience_years?: number;
  expertise?: string;
  
  // Settings and preferences
  is_active: boolean;
  email_notifications: boolean;
  marketing_emails: boolean;
  profile_visibility: ProfileVisibility;
  language: Language;
  
  // Computed fields
  completion_percentage?: number;
  active_roles?: ActiveRole[];
  highest_permission_level?: PermissionLevel;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  last_activity: string;
  
  // Profile image
  profile_image?: string;
}

// ========================================
// MAIN USER INTERFACE
// ========================================

export interface User {
  // Django User fields
  id: number;
  username: string;
  email: string;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_staff: boolean;
  is_superuser: boolean;
  date_joined: string;
  last_login?: string;
  
  // Computed fields
  full_name?: string;
  is_staff_member?: boolean;
  user_role?: UserRole;
  
  // Related data
  profile?: UserProfile;
  role_assignments?: UserRoleAssignment[];
}

// ========================================
// USER ROLE INFO (API RESPONSE)
// ========================================

export interface UserRoleInfo {
  user_id: number;
  username: string;
  primary_role: UserRole;
  all_roles: DatabaseRole[];
  is_staff: boolean;
  is_superuser: boolean;
  permission_level: PermissionLevel;
}

// ========================================
// COMMUNITY USER PROFILE (SIMPLIFIED)
// ========================================

export interface CommunityUserProfile {
  id: number; // Backend always returns numbers for IDs
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  avatar?: string | null; // Can be null from backend
  is_verified: boolean;
  bio?: string;
  location?: string;
  website?: string;
  user_role: UserRole;
  date_joined: string;
  followers_count: number;
  following_count: number;
  posts_count: number;
  is_following: boolean;
  is_followed_by: boolean;
}

// ========================================
// USER ACTIVITY INTERFACE
// ========================================

export interface UserActivity {
  post_count: number;
  comment_count: number;
  event_count: number;
  resource_count: number;
  likes_received: number;
  engagement_score: number;
  posts_by_day: Record<string, number>;
  comments_by_day: Record<string, number>;
  recent_posts: any[];
  recent_comments: any[];
  recent_events: any[];
}

// ========================================
// USER REGISTRATION DATA
// ========================================

export interface UserRegistrationData {
  // Basic user info
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  password_confirm: string;
  
  // Profile data
  bio?: string;
  location?: string;
  company?: string;
  job_title?: string;
  industry?: string;
  website?: string;
  linkedin_url?: string;
  language: Language;
  phone?: string;
  
  // Role selection
  selected_role?: UserRole;
  role_additional_info?: Record<string, any>;
  
  // Role-specific fields
  business_name?: string;
  business_stage?: string;
  funding_needed?: string;
  business_description?: string;
  team_size?: number;
  support_needed?: string;
  previous_experience?: string;
  
  // Mentor fields
  expertise?: string;
  experience?: string;
  mentorship_areas?: string;
  availability?: string;
  preferred_communication?: string;
  
  // Investor fields
  investment_range?: string;
  investment_stage?: string;
  preferred_industries?: string;
  investment_criteria?: string;
  portfolio_companies?: string;
  
  // Community Member fields
  interests?: string;
  goals?: string;
  
  // General fields
  portfolio_url?: string;
  motivation?: string;
  qualifications?: string;
}

// ========================================
// EXPORT ALL TYPES
// ========================================

export type {
  UserRole,
  PermissionLevel,
  ProfileVisibility,
  Language,
  DatabaseRole,
  UserRoleAssignment,
  ActiveRole,
  UserProfile,
  User,
  UserRoleInfo,
  CommunityUserProfile,
  UserActivity,
  UserRegistrationData
};
