/**
 * 🔄 DASHBOARD REDIRECT
 * Redirects to the appropriate dashboard based on user role
 * Cleaned up to eliminate duplicate dashboard implementations
 */

import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import { useUnifiedRoles } from '../hooks/useUnifiedRoles';
import AdminDashboardPage from './admin/AdminDashboardPage';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();
  const { primaryRole } = useUnifiedRoles();
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect admin users to the new admin dashboard
    if (primaryRole === 'admin') {
      // Don't redirect, just render the AdminDashboardPage directly
      return;
    }
    
    // For other roles, redirect to their specific pages
    switch (primaryRole) {
      case 'entrepreneur':
        navigate('/entrepreneur/business-plans');
        break;
      case 'mentor':
        navigate('/mentor/mentorships');
        break;
      case 'investor':
        navigate('/investor/opportunities');
        break;
      default:
        navigate('/');
    }
  }, [primaryRole, navigate]);

  // Render admin dashboard directly for admin users
  if (primaryRole === 'admin') {
    return <AdminDashboardPage />;
  }

  // Show loading for other roles while redirecting
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
        <p className="text-white">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
};

export default DashboardPage;
