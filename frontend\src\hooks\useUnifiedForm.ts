/**
 * 🎯 UNIFIED FORM HOOK
 * Single hook for all form handling - eliminates 30+ duplicate form functions
 */

import { useState, useCallback, useMemo } from 'react';
import { validateField, validateForm, ValidationSchema, ValidationResult, FormValidationResult } from '../utils/unifiedValidation';

// ========================================
// TYPES
// ========================================

export interface FormConfig<T> {
  initialData: T;
  validationSchema?: ValidationSchema;
  onSubmit?: (data: T) => Promise<void> | void;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  resetOnSubmit?: boolean;
  language?: 'ar' | 'en';
}

export interface FormState<T> {
  data: T;
  errors: Record<keyof T, string[]>;
  warnings: Record<keyof T, string[]>;
  touched: Record<keyof T, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
  isDirty: boolean;
  submitCount: number;
}

export interface FormActions<T> {
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;
  handleFieldChange: (name: keyof T, value: any) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  handleReset: () => void;
  setFieldValue: (name: keyof T, value: any) => void;
  setFieldError: (name: keyof T, error: string) => void;
  setFieldTouched: (name: keyof T, touched?: boolean) => void;
  validateField: (name: keyof T) => ValidationResult;
  validateForm: () => FormValidationResult;
  clearErrors: () => void;
  clearField: (name: keyof T) => void;
  resetForm: (newData?: T) => void;
}

export interface UnifiedFormReturn<T> extends FormState<T>, FormActions<T> {}

// ========================================
// UNIFIED FORM HOOK
// ========================================

export function useUnifiedForm<T extends Record<string, any>>(
  config: FormConfig<T>
): UnifiedFormReturn<T> {
  const {
    initialData,
    validationSchema = {},
    onSubmit,
    validateOnChange = false,
    validateOnBlur = true,
    resetOnSubmit = false,
    language = 'en'
  } = config;

  // ========================================
  // STATE
  // ========================================

  const [data, setData] = useState<T>(initialData);
  const [errors, setErrors] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>);
  const [warnings, setWarnings] = useState<Record<keyof T, string[]>>({} as Record<keyof T, string[]>);
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitCount, setSubmitCount] = useState(0);

  // ========================================
  // COMPUTED STATE
  // ========================================

  const isValid = useMemo(() => {
    return Object.keys(errors).length === 0 || Object.values(errors).every(errorList => errorList.length === 0);
  }, [errors]);

  const isDirty = useMemo(() => {
    return JSON.stringify(data) !== JSON.stringify(initialData);
  }, [data, initialData]);

  // ========================================
  // VALIDATION FUNCTIONS
  // ========================================

  const validateSingleField = useCallback((name: keyof T): ValidationResult => {
    const fieldRules = validationSchema[name as string];
    if (!fieldRules) {
      return { isValid: true, errors: [] };
    }

    return validateField(data[name], fieldRules, data);
  }, [data, validationSchema]);

  const validateEntireForm = useCallback((): FormValidationResult => {
    return validateForm(data, validationSchema);
  }, [data, validationSchema]);

  // ========================================
  // FIELD ACTIONS
  // ========================================

  const setFieldValue = useCallback((name: keyof T, value: any) => {
    setData(prev => ({ ...prev, [name]: value }));

    // Validate on change if enabled
    if (validateOnChange) {
      const fieldRules = validationSchema[name as string];
      if (fieldRules) {
        const result = validateField(value, fieldRules, { ...data, [name]: value });
        setErrors(prev => ({
          ...prev,
          [name]: result.errors
        }));
        if (result.warnings) {
          setWarnings(prev => ({
            ...prev,
            [name]: result.warnings || []
          }));
        }
      }
    } else {
      // Clear errors when user starts typing
      setErrors(prev => ({
        ...prev,
        [name]: []
      }));
    }
  }, [data, validationSchema, validateOnChange]);

  const setFieldError = useCallback((name: keyof T, error: string) => {
    setErrors(prev => ({
      ...prev,
      [name]: [error]
    }));
  }, []);

  const setFieldTouched = useCallback((name: keyof T, isTouched: boolean = true) => {
    setTouched(prev => ({
      ...prev,
      [name]: isTouched
    }));

    // Validate on blur if enabled and field is touched
    if (validateOnBlur && isTouched) {
      const result = validateSingleField(name);
      setErrors(prev => ({
        ...prev,
        [name]: result.errors
      }));
      if (result.warnings) {
        setWarnings(prev => ({
          ...prev,
          [name]: result.warnings || []
        }));
      }
    }
  }, [validateOnBlur, validateSingleField]);

  const clearField = useCallback((name: keyof T) => {
    setData(prev => ({ ...prev, [name]: '' as any }));
    setErrors(prev => ({ ...prev, [name]: [] }));
    setWarnings(prev => ({ ...prev, [name]: [] }));
    setTouched(prev => ({ ...prev, [name]: false }));
  }, []);

  // ========================================
  // INPUT HANDLERS
  // ========================================

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    
    // Handle different input types
    if (type === 'number') {
      processedValue = value === '' ? '' : parseFloat(value) || 0;
    } else if (type === 'checkbox') {
      processedValue = (e.target as HTMLInputElement).checked;
    } else if (type === 'file') {
      processedValue = (e.target as HTMLInputElement).files;
    }

    setFieldValue(name as keyof T, processedValue);
  }, [setFieldValue]);

  const handleFieldChange = useCallback((name: keyof T, value: any) => {
    setFieldValue(name, value);
  }, [setFieldValue]);

  // ========================================
  // FORM ACTIONS
  // ========================================

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (isSubmitting) return;

    setSubmitCount(prev => prev + 1);

    // Mark all fields as touched
    const allTouched = Object.keys(data).reduce((acc, key) => {
      acc[key as keyof T] = true;
      return acc;
    }, {} as Record<keyof T, boolean>);
    setTouched(allTouched);

    // Validate entire form
    const validationResult = validateEntireForm();
    
    if (!validationResult.isValid) {
      // Set all field errors
      const fieldErrors = Object.keys(validationResult.errors).reduce((acc, key) => {
        acc[key as keyof T] = validationResult.errors[key];
        return acc;
      }, {} as Record<keyof T, string[]>);
      
      setErrors(fieldErrors);
      return;
    }

    // Clear errors if validation passed
    setErrors({} as Record<keyof T, string[]>);

    // Submit form
    if (onSubmit) {
      setIsSubmitting(true);
      try {
        await onSubmit(data);
        
        if (resetOnSubmit) {
          resetForm();
        }
      } catch (error) {
        console.error('Form submission error:', error);
        // Handle submission error
        setErrors(prev => ({
          ...prev,
          submit: ['Submission failed. Please try again.'] as any
        }));
      } finally {
        setIsSubmitting(false);
      }
    }
  }, [data, isSubmitting, validateEntireForm, onSubmit, resetOnSubmit]);

  const handleReset = useCallback(() => {
    resetForm();
  }, []);

  const resetForm = useCallback((newData?: T) => {
    const resetData = newData || initialData;
    setData(resetData);
    setErrors({} as Record<keyof T, string[]>);
    setWarnings({} as Record<keyof T, string[]>);
    setTouched({} as Record<keyof T, boolean>);
    setIsSubmitting(false);
    setSubmitCount(0);
  }, [initialData]);

  const clearErrors = useCallback(() => {
    setErrors({} as Record<keyof T, string[]>);
    setWarnings({} as Record<keyof T, string[]>);
  }, []);

  // ========================================
  // RETURN OBJECT
  // ========================================

  return {
    // State
    data,
    errors,
    warnings,
    touched,
    isSubmitting,
    isValid,
    isDirty,
    submitCount,
    
    // Actions
    handleInputChange,
    handleFieldChange,
    handleSubmit,
    handleReset,
    setFieldValue,
    setFieldError,
    setFieldTouched,
    validateField: validateSingleField,
    validateForm: validateEntireForm,
    clearErrors,
    clearField,
    resetForm
  };
}

// ========================================
// CONVENIENCE HOOKS
// ========================================

/**
 * Simple form hook for basic forms
 */
export function useSimpleForm<T extends Record<string, any>>(
  initialData: T,
  onSubmit?: (data: T) => Promise<void> | void
) {
  return useUnifiedForm({
    initialData,
    onSubmit,
    validateOnBlur: false,
    validateOnChange: false
  });
}

/**
 * Validated form hook with schema
 */
export function useValidatedForm<T extends Record<string, any>>(
  initialData: T,
  validationSchema: ValidationSchema,
  onSubmit?: (data: T) => Promise<void> | void
) {
  return useUnifiedForm({
    initialData,
    validationSchema,
    onSubmit,
    validateOnBlur: true,
    validateOnChange: false
  });
}

/**
 * Real-time validated form hook
 */
export function useRealtimeForm<T extends Record<string, any>>(
  initialData: T,
  validationSchema: ValidationSchema,
  onSubmit?: (data: T) => Promise<void> | void
) {
  return useUnifiedForm({
    initialData,
    validationSchema,
    onSubmit,
    validateOnBlur: true,
    validateOnChange: true
  });
}

export default useUnifiedForm;
