"""
🔒 SECURITY HARDENING CONFIGURATION
Advanced security configurations and monitoring for Django
"""

import os
import logging
from django.conf import settings
from django.core.exceptions import DisallowedHost
from django.http import HttpResponseForbidden
from django.utils.deprecation import MiddlewareMixin
from django.middleware.security import SecurityMiddleware
from django.contrib.auth.signals import user_login_failed
from django.dispatch import receiver
from datetime import datetime, timedelta
import ipaddress
import re

logger = logging.getLogger('security')

# ========================================
# SECURITY CONFIGURATION
# ========================================

class SecurityConfig:
    """Centralized security configuration"""
    
    # Rate limiting
    RATE_LIMIT_REQUESTS = 100
    RATE_LIMIT_WINDOW = 3600  # 1 hour
    
    # Brute force protection
    MAX_LOGIN_ATTEMPTS = 5
    LOCKOUT_DURATION = 1800  # 30 minutes
    
    # IP whitelist/blacklist
    ALLOWED_IPS = []
    BLOCKED_IPS = []
    
    # Security headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
        'Content-Security-Policy': (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://apis.google.com; "
            "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://fonts.gstatic.com; "
            "connect-src 'self' wss: https:; "
            "frame-ancestors 'none'; "
            "base-uri 'self'; "
            "form-action 'self'"
        )
    }
    
    # Suspicious patterns
    SUSPICIOUS_PATTERNS = [
        r'<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>',
        r'javascript:',
        r'on\w+\s*=',
        r'eval\s*\(',
        r'document\.cookie',
        r'window\.location',
        r'<iframe',
        r'data:text\/html',
        r'vbscript:',
        r'expression\s*\(',
        r'import\s+os',
        r'__import__',
        r'exec\s*\(',
        r'system\s*\(',
        r'subprocess',
        r'\.\.\/\.\.\/',  # Path traversal
        r'union\s+select',  # SQL injection
        r'drop\s+table',
        r'delete\s+from',
        r'insert\s+into'
    ]

# ========================================
# SECURITY MIDDLEWARE
# ========================================

class EnhancedSecurityMiddleware(MiddlewareMixin):
    """Enhanced security middleware with threat detection"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.rate_limits = {}
        self.failed_attempts = {}
        super().__init__(get_response)
    
    def process_request(self, request):
        """Process incoming requests for security threats"""
        
        # Get client IP
        client_ip = self.get_client_ip(request)
        
        # Check IP blacklist
        if self.is_ip_blocked(client_ip):
            logger.warning(f"Blocked request from blacklisted IP: {client_ip}")
            return HttpResponseForbidden("Access denied")
        
        # Rate limiting
        if not self.check_rate_limit(client_ip):
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            return HttpResponseForbidden("Rate limit exceeded")
        
        # Check for suspicious patterns in request
        if self.detect_suspicious_patterns(request):
            logger.error(f"Suspicious request detected from IP: {client_ip}")
            self.log_security_event('suspicious_request', {
                'ip': client_ip,
                'path': request.path,
                'method': request.method,
                'user_agent': request.META.get('HTTP_USER_AGENT', ''),
                'data': str(request.body)[:500] if hasattr(request, 'body') else ''
            })
            return HttpResponseForbidden("Suspicious request detected")
        
        return None
    
    def process_response(self, request, response):
        """Add security headers to response"""
        
        for header, value in SecurityConfig.SECURITY_HEADERS.items():
            response[header] = value
        
        return response
    
    def get_client_ip(self, request):
        """Get the real client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def is_ip_blocked(self, ip):
        """Check if IP is in blacklist"""
        try:
            ip_obj = ipaddress.ip_address(ip)
            for blocked_ip in SecurityConfig.BLOCKED_IPS:
                if ip_obj in ipaddress.ip_network(blocked_ip, strict=False):
                    return True
        except ValueError:
            pass
        return False
    
    def check_rate_limit(self, ip):
        """Check rate limiting for IP"""
        now = datetime.now()
        window_start = now - timedelta(seconds=SecurityConfig.RATE_LIMIT_WINDOW)
        
        if ip not in self.rate_limits:
            self.rate_limits[ip] = []
        
        # Clean old requests
        self.rate_limits[ip] = [
            req_time for req_time in self.rate_limits[ip] 
            if req_time > window_start
        ]
        
        # Check limit
        if len(self.rate_limits[ip]) >= SecurityConfig.RATE_LIMIT_REQUESTS:
            return False
        
        # Add current request
        self.rate_limits[ip].append(now)
        return True
    
    def detect_suspicious_patterns(self, request):
        """Detect suspicious patterns in request"""
        
        # Check URL path
        if self.contains_suspicious_pattern(request.path):
            return True
        
        # Check query parameters
        for key, value in request.GET.items():
            if self.contains_suspicious_pattern(f"{key}={value}"):
                return True
        
        # Check POST data
        if hasattr(request, 'body') and request.body:
            try:
                body_str = request.body.decode('utf-8')
                if self.contains_suspicious_pattern(body_str):
                    return True
            except UnicodeDecodeError:
                pass
        
        # Check headers
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        if self.contains_suspicious_pattern(user_agent):
            return True
        
        return False
    
    def contains_suspicious_pattern(self, text):
        """Check if text contains suspicious patterns"""
        for pattern in SecurityConfig.SUSPICIOUS_PATTERNS:
            if re.search(pattern, text, re.IGNORECASE):
                return True
        return False
    
    def log_security_event(self, event_type, details):
        """Log security events"""
        logger.error(f"Security Event: {event_type}", extra={
            'event_type': event_type,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })

# ========================================
# BRUTE FORCE PROTECTION
# ========================================

class BruteForceProtection:
    """Brute force protection for login attempts"""
    
    failed_attempts = {}
    
    @classmethod
    def is_locked_out(cls, ip):
        """Check if IP is locked out due to failed attempts"""
        if ip not in cls.failed_attempts:
            return False
        
        attempts = cls.failed_attempts[ip]
        if attempts['count'] >= SecurityConfig.MAX_LOGIN_ATTEMPTS:
            lockout_end = attempts['last_attempt'] + timedelta(seconds=SecurityConfig.LOCKOUT_DURATION)
            if datetime.now() < lockout_end:
                return True
            else:
                # Lockout expired, reset
                del cls.failed_attempts[ip]
        
        return False
    
    @classmethod
    def record_failed_attempt(cls, ip):
        """Record a failed login attempt"""
        now = datetime.now()
        
        if ip not in cls.failed_attempts:
            cls.failed_attempts[ip] = {'count': 0, 'last_attempt': now}
        
        cls.failed_attempts[ip]['count'] += 1
        cls.failed_attempts[ip]['last_attempt'] = now
        
        logger.warning(f"Failed login attempt from IP: {ip} (attempt {cls.failed_attempts[ip]['count']})")
    
    @classmethod
    def reset_attempts(cls, ip):
        """Reset failed attempts for IP (on successful login)"""
        if ip in cls.failed_attempts:
            del cls.failed_attempts[ip]

# ========================================
# SIGNAL HANDLERS
# ========================================

@receiver(user_login_failed)
def handle_failed_login(sender, credentials, request, **kwargs):
    """Handle failed login attempts"""
    if request:
        ip = EnhancedSecurityMiddleware().get_client_ip(request)
        BruteForceProtection.record_failed_attempt(ip)

# ========================================
# SECURITY UTILITIES
# ========================================

class SecurityUtils:
    """Security utility functions"""
    
    @staticmethod
    def sanitize_input(text):
        """Sanitize user input"""
        if not text:
            return text
        
        # Remove suspicious patterns
        for pattern in SecurityConfig.SUSPICIOUS_PATTERNS:
            text = re.sub(pattern, '', text, flags=re.IGNORECASE)
        
        return text
    
    @staticmethod
    def validate_file_upload(file):
        """Validate file uploads"""
        # Check file size
        max_size = 10 * 1024 * 1024  # 10MB
        if file.size > max_size:
            raise ValueError("File too large")
        
        # Check file type
        allowed_types = [
            'image/jpeg', 'image/png', 'image/gif', 'image/webp',
            'application/pdf', 'text/plain', 'application/json'
        ]
        
        if hasattr(file, 'content_type') and file.content_type not in allowed_types:
            raise ValueError("File type not allowed")
        
        # Check filename
        if '..' in file.name or '/' in file.name or '\\' in file.name:
            raise ValueError("Invalid filename")
        
        return True
    
    @staticmethod
    def generate_secure_token(length=32):
        """Generate cryptographically secure token"""
        import secrets
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def hash_password_secure(password):
        """Hash password with secure algorithm"""
        from django.contrib.auth.hashers import make_password
        return make_password(password)

# ========================================
# SECURITY MONITORING
# ========================================

class SecurityMonitor:
    """Security monitoring and alerting"""
    
    @staticmethod
    def check_system_security():
        """Perform security health check"""
        issues = []
        
        # Check Django settings
        if settings.DEBUG:
            issues.append("DEBUG mode is enabled in production")
        
        if not settings.SECRET_KEY or settings.SECRET_KEY == 'your-secret-key-here':
            issues.append("Weak or default SECRET_KEY")
        
        if not settings.SECURE_SSL_REDIRECT and not settings.DEBUG:
            issues.append("SSL redirect not enabled")
        
        # Check middleware
        required_middleware = [
            'django.middleware.security.SecurityMiddleware',
            'django.middleware.csrf.CsrfViewMiddleware',
        ]
        
        for middleware in required_middleware:
            if middleware not in settings.MIDDLEWARE:
                issues.append(f"Missing security middleware: {middleware}")
        
        return issues
    
    @staticmethod
    def get_security_metrics():
        """Get security metrics"""
        return {
            'failed_login_attempts': len(BruteForceProtection.failed_attempts),
            'blocked_ips': len(SecurityConfig.BLOCKED_IPS),
            'security_issues': len(SecurityMonitor.check_system_security()),
            'last_check': datetime.now().isoformat()
        }

# ========================================
# DJANGO SETTINGS ENHANCEMENTS
# ========================================

def apply_security_settings():
    """Apply security settings to Django"""
    
    # Security settings
    settings.SECURE_BROWSER_XSS_FILTER = True
    settings.SECURE_CONTENT_TYPE_NOSNIFF = True
    settings.SECURE_HSTS_SECONDS = 31536000
    settings.SECURE_HSTS_INCLUDE_SUBDOMAINS = True
    settings.SECURE_HSTS_PRELOAD = True
    settings.SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
    
    # Session security
    settings.SESSION_COOKIE_SECURE = not settings.DEBUG
    settings.SESSION_COOKIE_HTTPONLY = True
    settings.SESSION_COOKIE_SAMESITE = 'Lax'
    settings.SESSION_COOKIE_AGE = 3600  # 1 hour
    
    # CSRF security
    settings.CSRF_COOKIE_SECURE = not settings.DEBUG
    settings.CSRF_COOKIE_HTTPONLY = True
    settings.CSRF_COOKIE_SAMESITE = 'Lax'
    
    # File upload security
    settings.FILE_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
    settings.DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024  # 10MB
    
    # Logging configuration
    if 'security' not in settings.LOGGING.get('loggers', {}):
        settings.LOGGING['loggers']['security'] = {
            'handlers': ['file'],
            'level': 'WARNING',
            'propagate': False,
        }
