/**
 * 🔧 ADMIN SHARED COMPONENTS
 * Consolidated reusable components for all admin pages to eliminate duplication
 * 
 * This file provides:
 * - AdminPageHeader: Consistent page headers
 * - AdminStatsCard: Standardized stats cards
 * - AdminLoadingState: Consistent loading states
 * - AdminErrorState: Consistent error states
 * - AdminRefreshButton: Standardized refresh functionality
 */

import React from 'react';
import { LucideIcon, RefreshCw, AlertTriangle } from 'lucide-react';
import { RTLIcon, RTLText } from '../../common';
import { useLanguage } from '../../../hooks/useLanguage';
import { useTranslation } from 'react-i18next';
import { EnhancedButton, EnhancedCard, designSystem } from '../../ui/DesignSystem';

// ========================================
// TYPES
// ========================================

export interface AdminPageHeaderProps {
  title: string;
  titleAr?: string;
  subtitle?: string;
  subtitleAr?: string;
  icon: LucideIcon;
  onRefresh?: () => void;
  refreshing?: boolean;
  actions?: React.ReactNode;
}

export interface AdminStatsCardProps {
  title: string;
  titleAr?: string;
  value: string | number;
  subtitle?: string;
  subtitleAr?: string;
  icon: LucideIcon;
  iconColor?: string;
  badgeText?: string;
  badgeColor?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
}

export interface AdminLoadingStateProps {
  message?: string;
  messageAr?: string;
}

export interface AdminErrorStateProps {
  error: string;
  onRetry?: () => void;
  retrying?: boolean;
}

// ========================================
// ADMIN PAGE HEADER
// ========================================

export const AdminPageHeader: React.FC<AdminPageHeaderProps> = ({
  title,
  titleAr,
  subtitle,
  subtitleAr,
  icon,
  onRefresh,
  refreshing = false,
  actions
}) => {
  const { isRTL } = useLanguage();
  const { t } = useTranslation();

  return (
    <EnhancedCard className="mb-6">
      <div className={`flex justify-between items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`flex items-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="inline-flex items-center justify-center w-12 h-12 rounded-full bg-purple-600/20 mr-4">
            <RTLIcon icon={icon} size={24} className="text-purple-400" />
          </div>
          <div>
            <RTLText as="h1" className="text-3xl font-bold text-white">
              {isRTL && titleAr ? titleAr : title}
            </RTLText>
            {subtitle && (
              <RTLText className="text-gray-300">
                {isRTL && subtitleAr ? subtitleAr : subtitle}
              </RTLText>
            )}
          </div>
        </div>

        <div className={`flex items-center space-x-4 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {onRefresh && (
            <EnhancedButton
              onClick={onRefresh}
              disabled={refreshing}
              variant="primary"
            >
              <RTLIcon
                icon={RefreshCw}
                size={16}
                className={refreshing ? 'animate-spin' : ''}
              />
              <RTLText>
                {refreshing ? t('admin.refreshing', 'Refreshing...') : t('admin.refresh', 'Refresh')}
              </RTLText>
            </EnhancedButton>
          )}
          {actions}
        </div>
      </div>
    </EnhancedCard>
  );
};

// ========================================
// ADMIN STATS CARD
// ========================================

export const AdminStatsCard: React.FC<AdminStatsCardProps> = ({
  title,
  titleAr,
  value,
  subtitle,
  subtitleAr,
  icon,
  iconColor = 'text-purple-400',
  badgeText,
  badgeColor = 'bg-purple-500/20 text-purple-400',
  trend,
  onClick
}) => {
  const { isRTL } = useLanguage();

  const cardContent = (
    <>
      <div className={`flex items-center justify-between mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`inline-flex items-center justify-center w-10 h-10 rounded-full ${iconColor.replace('text-', 'bg-').replace('-400', '-600/20')}`}>
          <RTLIcon icon={icon} size={20} className={iconColor} />
        </div>
        {badgeText && (
          <span className={`text-xs px-2 py-1 rounded-full ${badgeColor}`}>
            {badgeText}
          </span>
        )}
        {trend && (
          <span className={`text-xs px-2 py-1 rounded-full ${
            trend.isPositive ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
          }`}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </span>
        )}
      </div>
      
      <RTLText className="text-2xl font-bold text-white mb-1">
        {typeof value === 'number' ? value.toLocaleString() : value}
      </RTLText>
      
      <RTLText className="text-xl font-bold text-white mb-2">
        {isRTL && titleAr ? titleAr : title}
      </RTLText>
      
      {subtitle && (
        <RTLText className="text-gray-300 text-sm">
          {isRTL && subtitleAr ? subtitleAr : subtitle}
        </RTLText>
      )}
    </>
  );

  if (onClick) {
    return (
      <EnhancedCard 
        className="cursor-pointer hover:bg-white/10 transition-colors duration-200"
        onClick={onClick}
      >
        {cardContent}
      </EnhancedCard>
    );
  }

  return <EnhancedCard>{cardContent}</EnhancedCard>;
};

// ========================================
// ADMIN LOADING STATE
// ========================================

export const AdminLoadingState: React.FC<AdminLoadingStateProps> = ({
  message = 'Loading...',
  messageAr = 'جاري التحميل...'
}) => {
  const { isRTL } = useLanguage();

  return (
    <div className={`min-h-screen ${designSystem.backgrounds.main} flex items-center justify-center`}>
      <EnhancedCard className="p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <RTLText className="text-white">
            {isRTL ? messageAr : message}
          </RTLText>
        </div>
      </EnhancedCard>
    </div>
  );
};

// ========================================
// ADMIN ERROR STATE
// ========================================

export const AdminErrorState: React.FC<AdminErrorStateProps> = ({
  error,
  onRetry,
  retrying = false
}) => {
  const { t } = useTranslation();

  return (
    <div className={`min-h-screen ${designSystem.backgrounds.main} flex items-center justify-center`}>
      <EnhancedCard className="p-8 max-w-md w-full">
        <div className="text-center">
          <RTLIcon icon={AlertTriangle} size={48} className="text-red-400 mx-auto mb-4" />
          <RTLText className="text-red-400 text-lg mb-4">{error}</RTLText>
          {onRetry && (
            <EnhancedButton
              onClick={onRetry}
              disabled={retrying}
              variant="primary"
            >
              <RTLIcon
                icon={RefreshCw}
                size={16}
                className={retrying ? 'animate-spin' : ''}
              />
              <RTLText>
                {retrying ? t('admin.retrying', 'Retrying...') : t('admin.tryAgain', 'Try Again')}
              </RTLText>
            </EnhancedButton>
          )}
        </div>
      </EnhancedCard>
    </div>
  );
};

// ========================================
// ADMIN REFRESH BUTTON
// ========================================

export const AdminRefreshButton: React.FC<{
  onRefresh: () => void;
  refreshing?: boolean;
  variant?: 'primary' | 'secondary';
}> = ({ onRefresh, refreshing = false, variant = 'primary' }) => {
  const { t } = useTranslation();

  return (
    <EnhancedButton
      onClick={onRefresh}
      disabled={refreshing}
      variant={variant}
    >
      <RTLIcon
        icon={RefreshCw}
        size={16}
        className={refreshing ? 'animate-spin' : ''}
      />
      <RTLText>
        {refreshing ? t('admin.refreshing', 'Refreshing...') : t('admin.refresh', 'Refresh')}
      </RTLText>
    </EnhancedButton>
  );
};

// ========================================
// EXPORTS
// ========================================

export default {
  AdminPageHeader,
  AdminStatsCard,
  AdminLoadingState,
  AdminErrorState,
  AdminRefreshButton
};
