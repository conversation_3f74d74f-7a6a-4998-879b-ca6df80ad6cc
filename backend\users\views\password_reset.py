"""
Password Reset Views
Handles password reset endpoints
"""

from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django.contrib.auth.models import User
from django.utils import timezone
from django.shortcuts import get_object_or_404
import logging

from ..models.password_reset import (
    PasswordResetToken, 
    PasswordResetAttempt,
    PasswordResetSettings
)
from ..serializers.password_reset import (
    PasswordResetRequestSerializer,
    PasswordResetConfirmSerializer,
    PasswordResetValidateSerializer,
    PasswordResetSettingsSerializer
)

logger = logging.getLogger(__name__)


class PasswordResetRequestView(APIView):
    """
    Request password reset
    POST /api/auth/password/reset-request/
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        Send password reset email to user
        """
        serializer = PasswordResetRequestSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response({
                    'success': True,
                    'message': result['message'],
                    'email': result['email']
                }, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Password reset request failed: {e}")
                return Response({
                    'success': False,
                    'message': 'Failed to process password reset request',
                    'errors': {'non_field_errors': [str(e)]}
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': 'Invalid request data',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetConfirmView(APIView):
    """
    Confirm password reset with token
    POST /api/auth/password/reset-confirm/
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        Reset password using token
        """
        serializer = PasswordResetConfirmSerializer(
            data=request.data,
            context={'request': request}
        )
        
        if serializer.is_valid():
            try:
                result = serializer.save()
                return Response({
                    'success': True,
                    'message': result['message'],
                    'user_id': result['user_id']
                }, status=status.HTTP_200_OK)
                
            except Exception as e:
                logger.error(f"Password reset confirmation failed: {e}")
                return Response({
                    'success': False,
                    'message': 'Failed to reset password',
                    'errors': {'non_field_errors': [str(e)]}
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'success': False,
            'message': 'Invalid request data',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetValidateView(APIView):
    """
    Validate password reset token
    POST /api/auth/password/reset-validate/
    """
    permission_classes = [permissions.AllowAny]
    
    def post(self, request):
        """
        Validate if a password reset token is valid
        """
        serializer = PasswordResetValidateSerializer(data=request.data)
        
        if serializer.is_valid():
            token_value = serializer.validated_data['token']
            
            try:
                token = PasswordResetToken.objects.get(token=token_value)
                
                if token.is_valid:
                    return Response({
                        'success': True,
                        'message': 'Token is valid',
                        'valid': True,
                        'expires_at': token.expires_at,
                        'email': token.email
                    }, status=status.HTTP_200_OK)
                else:
                    reason = 'expired' if token.is_expired else 'used'
                    return Response({
                        'success': False,
                        'message': f'Token is {reason}',
                        'valid': False,
                        'reason': reason
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except PasswordResetToken.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Invalid token',
                    'valid': False,
                    'reason': 'not_found'
                }, status=status.HTTP_404_NOT_FOUND)
        
        return Response({
            'success': False,
            'message': 'Invalid request data',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class PasswordResetSettingsView(APIView):
    """
    Get password reset settings
    GET /api/auth/password/reset-settings/
    """
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        """
        Get current password reset settings
        """
        try:
            settings = PasswordResetSettings.get_settings()
            serializer = PasswordResetSettingsSerializer(settings)
            
            return Response({
                'success': True,
                'settings': serializer.data
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get password reset settings: {e}")
            return Response({
                'success': False,
                'message': 'Failed to get settings',
                'errors': {'non_field_errors': [str(e)]}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def validate_reset_token(request, token):
    """
    Validate password reset token via URL
    GET /api/auth/password/validate/<uuid:token>/
    """
    try:
        reset_token = get_object_or_404(PasswordResetToken, token=token)
        
        if reset_token.is_valid:
            return Response({
                'success': True,
                'message': 'Token is valid',
                'valid': True,
                'expires_at': reset_token.expires_at,
                'email': reset_token.email
            }, status=status.HTTP_200_OK)
        else:
            reason = 'expired' if reset_token.is_expired else 'used'
            return Response({
                'success': False,
                'message': f'Token is {reason}',
                'valid': False,
                'reason': reason
            }, status=status.HTTP_400_BAD_REQUEST)
            
    except Exception as e:
        logger.error(f"Token validation failed: {e}")
        return Response({
            'success': False,
            'message': 'Invalid token',
            'valid': False,
            'reason': 'not_found'
        }, status=status.HTTP_404_NOT_FOUND)


# Statistics and monitoring views for admin
class PasswordResetStatsView(APIView):
    """
    Get password reset statistics (admin only)
    GET /api/auth/password/stats/
    """
    permission_classes = [permissions.IsAdminUser]
    
    def get(self, request):
        """
        Get password reset statistics
        """
        try:
            from django.db.models import Count, Q
            from datetime import timedelta
            
            now = timezone.now()
            last_24h = now - timedelta(hours=24)
            last_7d = now - timedelta(days=7)
            last_30d = now - timedelta(days=30)
            
            stats = {
                'total_tokens': PasswordResetToken.objects.count(),
                'active_tokens': PasswordResetToken.objects.filter(
                    is_used=False, 
                    expires_at__gt=now
                ).count(),
                'used_tokens': PasswordResetToken.objects.filter(is_used=True).count(),
                'expired_tokens': PasswordResetToken.objects.filter(
                    is_used=False,
                    expires_at__lte=now
                ).count(),
                'last_24h': PasswordResetToken.objects.filter(
                    created_at__gte=last_24h
                ).count(),
                'last_7d': PasswordResetToken.objects.filter(
                    created_at__gte=last_7d
                ).count(),
                'last_30d': PasswordResetToken.objects.filter(
                    created_at__gte=last_30d
                ).count(),
                'total_attempts': PasswordResetAttempt.objects.count(),
                'failed_attempts_24h': PasswordResetAttempt.objects.filter(
                    created_at__gte=last_24h,
                    success=False
                ).count()
            }
            
            return Response({
                'success': True,
                'stats': stats
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"Failed to get password reset stats: {e}")
            return Response({
                'success': False,
                'message': 'Failed to get statistics',
                'errors': {'non_field_errors': [str(e)]}
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
