# Production Environment Configuration
# Copy this file to .env and update with your production values

# Django Settings
DEBUG=False
SECRET_KEY=your-super-secret-production-key-here-change-this
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com,localhost

# Database Configuration (PostgreSQL for production)
DATABASE_URL=postgresql://username:password@localhost:5432/yasmeen_ai_prod
# Alternative format:
# DB_ENGINE=django.db.backends.postgresql
# DB_NAME=yasmeen_ai_prod
# DB_USER=your_db_user
# DB_PASSWORD=your_db_password
# DB_HOST=localhost
# DB_PORT=5432

# Redis Configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379/0
CACHE_URL=redis://localhost:6379/1

# Email Configuration (for email verification and notifications)
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
DEFAULT_FROM_EMAIL=<EMAIL>

# AI Configuration
GEMINI_API_KEY=your-production-gemini-api-key-here

# Security Settings
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
SECURE_REFERRER_POLICY=strict-origin-when-cross-origin

# Session Configuration
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True
SESSION_COOKIE_SAMESITE=Strict
CSRF_COOKIE_SECURE=True
CSRF_COOKIE_HTTPONLY=True
CSRF_COOKIE_SAMESITE=Strict

# Static Files (for production with CDN)
STATIC_URL=/static/
STATIC_ROOT=/var/www/yasmeen-ai/static/
MEDIA_URL=/media/
MEDIA_ROOT=/var/www/yasmeen-ai/media/

# AWS S3 Configuration (optional, for file storage)
# AWS_ACCESS_KEY_ID=your-aws-access-key
# AWS_SECRET_ACCESS_KEY=your-aws-secret-key
# AWS_STORAGE_BUCKET_NAME=your-bucket-name
# AWS_S3_REGION_NAME=us-east-1
# AWS_S3_CUSTOM_DOMAIN=your-bucket-name.s3.amazonaws.com

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=/var/log/yasmeen-ai/django.log

# Performance Settings
CONN_MAX_AGE=60
DATABASE_CONN_MAX_AGE=60

# Monitoring and Analytics
SENTRY_DSN=your-sentry-dsn-here
GOOGLE_ANALYTICS_ID=your-ga-id-here

# API Rate Limiting
API_RATE_LIMIT=1000/hour
API_BURST_LIMIT=100/minute

# Backup Configuration
BACKUP_ENABLED=True
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Feature Flags
ENABLE_REGISTRATION=True
ENABLE_EMAIL_VERIFICATION=True
ENABLE_AI_FEATURES=True
ENABLE_SOCIAL_LOGIN=True
ENABLE_ANALYTICS=True

# Social Authentication (optional)
GOOGLE_OAUTH2_CLIENT_ID=your-google-client-id
GOOGLE_OAUTH2_CLIENT_SECRET=your-google-client-secret
LINKEDIN_OAUTH2_CLIENT_ID=your-linkedin-client-id
LINKEDIN_OAUTH2_CLIENT_SECRET=your-linkedin-client-secret

# Payment Configuration (if needed)
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/2
CELERY_RESULT_BACKEND=redis://localhost:6379/3
CELERY_ACCEPT_CONTENT=json
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_TIMEZONE=UTC

# File Upload Limits
FILE_UPLOAD_MAX_MEMORY_SIZE=5242880  # 5MB
DATA_UPLOAD_MAX_MEMORY_SIZE=5242880  # 5MB
FILE_UPLOAD_PERMISSIONS=0o644

# CORS Configuration (for frontend)
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
CORS_ALLOW_CREDENTIALS=True

# Internationalization
LANGUAGE_CODE=en-us
TIME_ZONE=UTC
USE_I18N=True
USE_L10N=True
USE_TZ=True

# Admin Configuration
ADMIN_URL=admin/  # Change this for security
ADMIN_EMAIL=<EMAIL>

# Health Check Configuration
HEALTH_CHECK_ENABLED=True
HEALTH_CHECK_URL=/health/

# Documentation
API_DOCS_ENABLED=False  # Disable in production for security
